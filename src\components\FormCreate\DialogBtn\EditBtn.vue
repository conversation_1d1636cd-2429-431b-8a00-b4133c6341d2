<template>
  <DialogBtn ref="dialogRef" v-bind="{ ...props, btn, dialog: { closeOnClickModal: false, ...dialog } }">
    <slot />
  </DialogBtn>
</template>

<script lang="ts" setup>
import { Plus } from "@element-plus/icons-vue";
import DialogBtn from "./index.vue";
import { DialogBtnProps } from "./types";
import { computed, inject, nextTick, ref } from "vue";
import { Emitter } from "mitt";
import { Api } from "@form-create/element-ui";

const props = defineProps<DialogBtnProps>();

const mittBus = inject<Emitter<any>>("mittBus");
const emit = defineEmits(["init"]);
const dialogRef = ref();

// 定义一个函数，处理传入的 formatter 用于打开编辑弹窗前处理数据
function getHandleData(data: { [key: string]: any }, childrenApi: Api) {
  // 获取formatter函数
  const formatter = props.formCreateInject?.rule?.props?.formatter;
  // 确保 formatter 是一个函数，如果不是，直接返回formatter的值
  if (formatter) return typeof formatter === "function" ? formatter(data, childrenApi) : formatter;
  return data;
}

const btn = computed(() => {
  return {
    icon: Plus,
    link: true,
    show: false,
    content: "修改",
    ...props.btn
  } as DialogBtnProps["btn"];
});
// 接收表格操作事件
mittBus?.off(`action-${props.action}`);
mittBus?.on(`action-${props.action}`, ({ row, api }: { row: { [key: string]: any }; api: Api }) => {
  dialogRef.value.toggle();
  nextTick(async () => {
    // 自定义api的值 用于处理api层级不为0的情况,详情见运营管理
    if (props.customApi) {
      props.customApi(api.children).setValue(await getHandleData(row));
    } else {
      let childrenApi = api.children.at(-1);
      // @ts-ignore
      const formCreate = api.children.find(item => item.config.n === props.n);
      if (formCreate) {
        childrenApi = formCreate;
      }
      // 默认api的值
      childrenApi?.setValue(await getHandleData(row, childrenApi));
    }
    // 储存行数据 用于动态添加弹窗副标题subtitle
    if (props.dialog) props.dialog["rowData"] = row || {};
    emit("init", row, api);
  });
});

defineExpose({
  toggle() {
    dialogRef.value.toggle();
  }
});
</script>

<style lang="scss" scoped></style>
