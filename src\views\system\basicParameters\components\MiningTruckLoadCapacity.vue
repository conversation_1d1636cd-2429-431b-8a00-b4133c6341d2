<template>
  <form-create v-model:api="fApi" :option="option" :rule="rule"></form-create>
</template>

<script lang="tsx" setup>
/**
 * @file 系统管理-基础参数-矿卡载重
 * <AUTHOR>
 * @date 2024/11/21
 */
import { ref, watch, computed } from "vue";
import functionalModuleList from "./functionalModuleList.vue";
import { ElMessage } from "element-plus/es";

import formCreate from "@form-create/element-ui";
import { getMaterialTrainList } from "@/api/modules";
// import fetchLoad from "./fetch.json";
formCreate.component("functionalModuleList", functionalModuleList);
const fApi = ref();
const option = { resetBtn: false, submitBtn: false, row: { gutter: 25 }, form: { inline: true } };
// 载重数据
const loadTable = ref();
// 获取矿卡载重数据
const fetchData = async () => {
  let { data: loadTableData } = await getMaterialTrainList();
  // console.log(loadTableData);
  loadTable.value = loadTableData;
  fApi.value.findRule({ name: "loadTable" }).props.data = loadTable.value[0]?.materialTrainList;
  fApi.value.findRule({ name: "title" }).children[0] = loadTable.value[0]?.name;
};
fetchData();
const rule = ref([
  {
    type: "div",
    // props: { span: 4 },
    class: "left-list",
    children: [
      // 左侧功能模块
      {
        type: "functionalModuleList",
        props: {
          loadTable: computed(() => {
            return loadTable.value;
          })
        },
        on: {
          // 更新值时更新右侧数据
          "update:modelValue": (val: any) => {
            console.log(val);
            fApi.value.findRule({ name: "loadTable" }).props.data = val?.materialTrainList || [];
            fApi.value.findRule({ name: "title" }).children[0] = val?.name;
          }
        }
      }
    ]
  },
  {
    type: "div",
    style: {
      flex: 1,
      width: "10%"
    },
    children: [
      // 右侧标题
      {
        type: "div",
        name: "title",
        style: { marginBottom: "10px", lineHeight: "1", fontWeight: "bold", color: "#1A1A1A" },
        children: []
      },
      // 右侧表格
      {
        type: "ProTable",
        name: "loadTable",
        style: { height: "calc(67vh - 24px)" },
        props: {
          pagination: false,
          data: [],
          columns: [
            { prop: "trainName", label: "矿卡名称" },
            { prop: "trainCode", label: "矿卡编码" },
            { prop: "trainModel", label: "矿卡型号" },
            { prop: "loadAbilities", label: "承载能力(t)" },
            {
              prop: "loadCapacity",
              label: "载重",
              render: scope => {
                return (
                  <el-input
                    v-model={scope.row.loadCapacity}
                    style="height: 32px;"
                    v-slots={{ suffix: () => "t" }}
                    onBlur={() => {
                      if (Number(scope.row.loadCapacity) > 0) {
                        scope.row.loadCapacity = Number(scope.row.loadCapacity).toFixed(2);
                      } else {
                        ElMessage.error("请输入非负数字");
                        scope.row.loadCapacity = "";
                      }
                    }}
                  />
                );
              }
            }
          ]
        }
      }
    ]
  }
]);

const emit = defineEmits(["update:modelValue"]);

watch(
  () => loadTable.value,
  () => {
    emit("update:modelValue", loadTable.value);
  },
  { deep: true }
);
</script>
<style lang="scss" scoped>
:deep(.el-table__inner-wrapper:before) {
  height: 0;
}
:deep(.table-box) {
  border: 1px solid #e4e7ed;
  .table-main {
    width: 99%;
    margin: auto;
  }
}
:deep(.el-col) {
  display: flex;
  justify-content: space-between;
  flex-direction: column;
}
//.el-form-item__content {
//  height: 100%;
//}
//.el-form-item {
//  width: 100%;
//  height: 100%;
//}

:deep(.el-form-item) {
  width: 100%;
  height: 100%;
  margin: 0;
}
</style>
