<template>
  <vc-entity v-for="(entity, index) in entitiesData" :key="index" v-bind="entity" />
</template>
<script lang="ts" setup>
/*
  自由点加载
*/
import { ref } from "vue";
import { VcEntity } from "vue-cesium";
import marker from "../assets/marker.webp";

const billboard = {
  image: marker,
  scale: 0.5,
  verticalOrigin: 1,
  horizontalOrigin: 0
};

const entitiesData = ref([
  {
    billboard,
    position: { lng: 139.93309281848, lat: 35.90375498678, height: 0 }
  }
]);

const addPoint = (point: { lng: number; lat: number; height?: number }) => {
  entitiesData.value.push({
    billboard,
    position: { lng: point.lng, lat: point.lat, height: point.height || 0 }
  });
};

const removeLastPoint = () => {
  entitiesData.value.pop();
};

defineExpose({ addPoint, removeLastPoint });
</script>
