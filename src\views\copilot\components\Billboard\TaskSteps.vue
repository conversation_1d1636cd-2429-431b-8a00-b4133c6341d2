<template>
  <div v-for="(task, index) in list" v-if="list?.length" class="task-details">
    <div class="task-container">
      <div v-if="showName" style="color: #c9cad9; font-size: 13px">{{ task?.trainName }}</div>
      <el-steps
        v-if="task?.taskStepList?.length"
        :active="getActive(task?.taskStepList)"
        align-center
        finish-status="success"
        style="flex: 1"
      >
        <el-step v-for="v in task?.taskStepList" :key="v.id" :title="v.name">
          <template #icon>
            <!--执行完成-->
            <!--            <img v-if="v.status === 2" alt="" class="step__icon" src="./imgs/successIcon.svg" />-->
            <!--执行中-->
            <!--            <img v-else-if="v.status === 1" alt="" class="step__icon" src="./imgs/loadIcon.svg" />-->
            <!--待执行-->
            <div class="step__icon"></div>
          </template>
        </el-step>
      </el-steps>
    </div>
    <el-divider v-if="index !== list.length - 1" style="margin: 14px 0" />
  </div>
  <!-- <div v-else style="height: 100%; display: flex; align-items: center; justify-content: center">

  </div> -->
  <el-empty v-else image-size="0" style="padding: 0; background-color: var(--card-bg-color)" />
</template>

<script lang="tsx" setup>
withDefaults(defineProps<{ list: { [key: string]: any }[]; showName?: boolean }>(), {
  showName: true,
  list: () => []
});

// 写一个函数传入数组对象,根据数组对象中的status字段返回,正在执行中对象的索引
const getActive = (list: any[]) => {
  if (list?.length) return list.findIndex(v => Number(v.status) === 1);
  return 0;
};
</script>

<style lang="scss" scoped>
.task-details {
  width: 100%;
  box-sizing: border-box;
  .task-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
  }

  //border-radius: 10px;
  :deep(.el-step__line-inner) {
    height: 2px;
    top: 12px;
    border-color: var(--el-color-primary);
  }
  :deep(.el-step__main) {
    height: 26px;
  }
  :deep(.el-step__icon) {
    width: unset;
    height: unset;
  }
  .step__icon {
    height: 8px;
    width: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid var(--el-color-primary);
    border-radius: 50%;
  }
  :deep(.is-wait) {
    .step__icon {
      border: 1px solid transparent;
      background: #2e3070;
    }
  }

  :deep(.el-step__head.is-success) {
    color: var(--el-color-primary);
  }

  :deep(.is-success .el-step__line-inner) {
    width: 100% !important;
    border-width: 1px !important;
  }

  :deep(.el-step__title) {
    font-size: 13px;
    white-space: nowrap;
    overflow: hidden;
    color: #c9cad9;
    &.is-process,
    &.is-success {
      color: var(--el-color-primary);
      font-weight: normal;
    }
  }

  :deep(.el-step__line) {
    background-color: #2e3070;
  }
}
:deep(.el-empty__description) {
  margin-top: 0;
}
</style>
