import { defineStore } from "pinia";
import { AuthState } from "@/stores/interface";
import {
  getFlatMenuList,
  getShowMenuList,
  getAllBreadcrumbList,
  processAsyncRoutes,
  ascending,
  addRoutesRedirect,
  ascendingBtnAuth
} from "@/utils";
import { getAsyncRoutes } from "@/api/modules";

export const useAuthStore = defineStore({
  id: "geeker-auth",
  state: (): AuthState => ({
    // 按钮权限列表
    authButtonList: {},
    // 菜单权限列表
    authMenuList: [],
    // 未经处理的原始数据
    originAuthMenuList: [],
    // 当前页面的 router name，用来做按钮权限筛选
    routeName: ""
  }),
  getters: {
    // 按钮权限列表
    authButtonListGet: state => state.authButtonList,
    // 菜单权限列表 ==> 这里的菜单没有经过任何处理
    authMenuListGet: state => state.authMenuList,
    // 菜单权限列表 ==> 左侧菜单栏渲染，需要剔除 isHide == true
    showMenuListGet: state => getShowMenuList(state.authMenuList),
    // 菜单权限列表 ==> 扁平化之后的一维数组菜单，主要用来添加动态路由
    flatMenuListGet: state => getFlatMenuList(state.authMenuList),
    // 递归处理后的所有面包屑导航列表
    breadcrumbListGet: state => getAllBreadcrumbList(state.authMenuList),
    // 当前页面按钮权限
    currentPageRolesGet: state => state.authButtonList[state.routeName] ?? {}
  },
  actions: {
    // 获取按钮权限
    // async getAuthButtonList() {
    //   // data的数据格式{menuMange: ['add', 'delete'], ...}
    //   // 获取按钮权限接口数据
    //   const { data } = await getAuthButtonListApi();
    //   this.authButtonList = data;
    // },
    // 获取菜单权限
    async getAuthMenuList() {
      // 获取菜单接口数据
      // const { data } = await getAuthMenuListApi();
      const { data: menuList } = await getAsyncRoutes();
      // 保存接口获取的原始数据
      this.originAuthMenuList = menuList as unknown as any[];
      // 把后端返回的数据组装成前端路由所需要的格式(处理字段名称,按钮权限等数据)

      const processedRoutesData = processAsyncRoutes(menuList as unknown as any[]);
      if (this.originAuthMenuList?.length === 0) {
        this.authMenuList = menuList as unknown as any[];
      } else {
        // 把所有的路由根据rank属性进行递归排序
        const sortRoutes = ascending(processedRoutesData);
        // 默认跳转
        // 处理好的路由数据
        let routes: any = addRoutesRedirect(sortRoutes);
        // 存储菜单数据
        this.authMenuList = routes;
        // 储存处理好的按钮权限数据
        this.authButtonList = ascendingBtnAuth(routes);
        // console.log("按钮权限数据", authButtonListData);
      }
      // this.authMenuList = data;
    },
    // Set RouteName
    async setRouteName(name: string) {
      this.routeName = name;
    }
  }
});
