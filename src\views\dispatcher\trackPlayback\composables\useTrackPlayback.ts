import { ref, computed, watch } from "vue";
import { ElMessage } from "element-plus";
import type { TrackData, OfflineDevice, VehicleInfo } from "../types";
import { mockTrackData } from "../mock/trackData";

export function useTrackPlayback() {
  // 响应式状态
  const selectedVehicles = ref<string[]>([]);
  const selectedDate = ref<string>("");
  const currentTime = ref<number>(0);
  const isPlaying = ref<boolean>(false);
  const playSpeed = ref<number>(1);
  const trackData = ref<TrackData[]>([]);
  const offlineDevices = ref<OfflineDevice[]>([]);
  const loading = ref<boolean>(false);
  const totalDuration = ref<number>(0);
  const startTime = ref<number>(0);
  const endTime = ref<number>(0);
  const showTimelineControl = ref<boolean>(false);

  // 播放定时器
  let playTimer: number | null = null;
  let cesiumViewer: any = null;

  // 计算属性
  const timelinePercent = computed({
    get: () => {
      if (totalDuration.value === 0) return 0;
      return ((currentTime.value - startTime.value) / totalDuration.value) * 100;
    },
    set: (percent: number) => {
      if (totalDuration.value > 0) {
        currentTime.value = startTime.value + (totalDuration.value * percent) / 100;
      }
    }
  });

  const hasTrackData = computed(() => trackData.value.length > 0);

  // 时间轴显示的开始和结束时间（基于选择的日期）
  const timelineStartTime = computed(() => {
    if (!selectedDate.value) return 0;
    const date = new Date(selectedDate.value);
    date.setHours(0, 0, 0, 0);
    return date.getTime();
  });

  const timelineEndTime = computed(() => {
    if (!selectedDate.value) return 0;
    const date = new Date(selectedDate.value);
    date.setHours(23, 59, 59, 999);
    return date.getTime();
  });

  // 格式化时间显示
  const formatTime = (timestamp: number): string => {
    if (!timestamp) return "00:00:00";
    const date = new Date(timestamp);
    return date.toLocaleTimeString("zh-CN", { hour12: false });
  };

  // 处理回放按钮点击
  const handleReplay = async () => {
    if (!selectedVehicles.value.length) {
      ElMessage.warning("请选择要回放的车辆");
      return;
    }

    if (!selectedDate.value) {
      ElMessage.warning("请选择回放日期");
      return;
    }

    loading.value = true;

    try {
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 使用mock数据
      const mockData = mockTrackData.filter(item => selectedVehicles.value.includes(item.id));

      if (mockData.length === 0) {
        ElMessage.warning("没有找到选中车辆的轨迹数据");
        return;
      }

      // 设置轨迹数据
      trackData.value = mockData;

      // 计算时间范围
      const allStartTimes = mockData.map(item => item.startTime);
      const allEndTimes = mockData.map(item => item.endTime);
      startTime.value = Math.min(...allStartTimes);
      endTime.value = Math.max(...allEndTimes);
      totalDuration.value = endTime.value - startTime.value;
      currentTime.value = startTime.value;

      // 更新离线设备（模拟）
      updateOfflineDevices();

      // 显示时间轴控制
      showTimelineControl.value = true;

      ElMessage.success("轨迹数据加载成功");
    } catch (error) {
      console.error("加载轨迹数据失败:", error);
      ElMessage.error("加载轨迹数据失败");
    } finally {
      loading.value = false;
    }
  };

  // 处理重置按钮点击
  const handleReset = () => {
    // 停止播放
    if (isPlaying.value) {
      togglePlayback();
    }

    // 重置所有状态
    selectedVehicles.value = [];
    selectedDate.value = "";
    currentTime.value = 0;
    trackData.value = [];
    offlineDevices.value = [];
    totalDuration.value = 0;
    startTime.value = 0;
    endTime.value = 0;
    showTimelineControl.value = false;

    ElMessage.info("已重置所有设置");
  };

  // 处理时间轴变化
  const handleTimelineChange = (percent: number) => {
    timelinePercent.value = percent;
    updateOfflineDevices();

    // 同步Cesium时钟
    if (cesiumViewer) {
      cesiumViewer.clock.currentTime = Cesium.JulianDate.fromDate(new Date(currentTime.value));
    }
  };

  // 处理时间轴拖拽
  const handleTimelineDrag = (percent: number) => {
    timelinePercent.value = percent;
  };

  // 切换播放状态
  const togglePlayback = () => {
    if (!hasTrackData.value) return;

    isPlaying.value = !isPlaying.value;

    if (isPlaying.value) {
      startPlayback();
    } else {
      stopPlayback();
    }
  };

  // 开始播放
  const startPlayback = () => {
    if (playTimer) {
      clearInterval(playTimer);
    }

    // 根据播放倍速设置定时器间隔
    const interval = 1000 / playSpeed.value;

    playTimer = window.setInterval(() => {
      const nextTime = currentTime.value + 1000 * playSpeed.value;

      if (nextTime >= endTime.value) {
        // 播放结束
        currentTime.value = endTime.value;
        stopPlayback();
        ElMessage.info("轨迹回放完成");
      } else {
        currentTime.value = nextTime;
        updateOfflineDevices();
      }
    }, interval);

    // 同步Cesium播放状态
    if (cesiumViewer) {
      cesiumViewer.clock.shouldAnimate = true;
      cesiumViewer.clock.multiplier = playSpeed.value;
    }
  };

  // 停止播放
  const stopPlayback = () => {
    if (playTimer) {
      clearInterval(playTimer);
      playTimer = null;
    }

    isPlaying.value = false;

    // 同步Cesium播放状态
    if (cesiumViewer) {
      cesiumViewer.clock.shouldAnimate = false;
    }
  };

  // 更新离线设备列表
  const updateOfflineDevices = () => {
    if (!hasTrackData.value || currentTime.value === 0) {
      offlineDevices.value = [];
      return;
    }

    const offline: OfflineDevice[] = [];

    trackData.value.forEach(vehicle => {
      // 检查当前时间点车辆是否有位置数据
      const hasPositionAtCurrentTime = vehicle.positions.some(
        pos => Math.abs(pos.timestamp - currentTime.value) < 60000 // 1分钟内
      );

      if (!hasPositionAtCurrentTime) {
        offline.push({
          id: vehicle.id,
          name: vehicle.name,
          type: vehicle.type,
          offlineTime: currentTime.value,
          lastPosition: vehicle.positions[vehicle.positions.length - 1]
        });
      }
    });

    offlineDevices.value = offline;
  };

  // 处理地图准备就绪
  const handleMapReady = (viewer: any) => {
    cesiumViewer = viewer;
    console.log("地图初始化完成");
  };

  // 监听播放倍速变化
  watch(playSpeed, newSpeed => {
    if (isPlaying.value) {
      // 重新开始播放以应用新的倍速
      stopPlayback();
      isPlaying.value = true;
      startPlayback();
    }

    // 同步Cesium倍速
    if (cesiumViewer) {
      cesiumViewer.clock.multiplier = newSpeed;
    }
  });

  // 监听当前时间变化，同步Cesium时钟
  watch(currentTime, newTime => {
    if (cesiumViewer && newTime > 0) {
      cesiumViewer.clock.currentTime = Cesium.JulianDate.fromDate(new Date(newTime));
    }
  });

  // 组件卸载时清理定时器
  const cleanup = () => {
    if (playTimer) {
      clearInterval(playTimer);
    }
  };

  return {
    // 状态
    selectedVehicles,
    selectedDate,
    currentTime,
    isPlaying,
    playSpeed,
    trackData,
    offlineDevices,
    loading,

    // 计算属性
    timelinePercent,
    hasTrackData,

    // 方法
    handleReplay,
    handleReset,
    handleTimelineChange,
    handleTimelineDrag,
    togglePlayback,
    formatTime,
    handleMapReady,
    cleanup,

    // 时间相关
    startTime,
    endTime,
    totalDuration,
    timelineStartTime,
    timelineEndTime,
    showTimelineControl
  };
}
