import { createGlobalState, useIntervalFn } from "@vueuse/core";
import { computed, ref, Ref, watch, onMounted, onUnmounted } from "vue";

// 设备信息接口
interface DeviceInfo {
  id: string;
  type: string;
  name?: string;
  status?: number;
  rawData?: Record<string, any>;
  detail?: Record<string, any>;

  [key: string]: any;
}

// 设备选择状态管理
export const useDeviceSelection = createGlobalState(() => {
  const selectedDeviceId = ref<string | null>(null);
  const selectedDeviceType = ref<string | null>(null);
  const selectedDeviceInfo = ref<DeviceInfo | null>(null);

  // 设置选中设备
  function selectDevice(deviceInfo: DeviceInfo) {
    selectedDeviceId.value = deviceInfo.id;
    selectedDeviceType.value = deviceInfo.type;
    selectedDeviceInfo.value = deviceInfo;
  }

  // 更新设备信息
  function updateDeviceInfo(info: Partial<DeviceInfo>) {
    if (selectedDeviceInfo.value && info.id === selectedDeviceInfo.value.id) {
      // 如果status是数字，转换为枚举
      const convertedInfo = { ...info };
      if (typeof info.status === "number") {
        convertedInfo.status = info.status;
      }
      selectedDeviceInfo.value = { ...selectedDeviceInfo.value, ...convertedInfo };
    }
  }

  // 清除选中设备
  function clearSelection() {
    selectedDeviceId.value = null;
    selectedDeviceType.value = null;
    selectedDeviceInfo.value = null;
  }

  // 计算属性：设备状态
  const isDeviceSelected = computed(() => selectedDeviceId.value !== null);
  const deviceStatus = computed(() => Number(selectedDeviceInfo.value?.status));
  const deviceName = computed(() => selectedDeviceInfo.value?.name);

  return {
    // 基础状态
    selectedDeviceId,
    selectedDeviceType,
    selectedDeviceInfo,

    // 计算属性
    isDeviceSelected,
    deviceStatus,
    deviceName,

    // 方法
    selectDevice,
    updateDeviceInfo,
    clearSelection
  };
});

// 单个异步数据源的配置接口
interface AsyncStoreOptions<T, R = T> {
  // 请求函数
  fetcher: () => Promise<T>;
  // 数据转换函数
  transform?: (data: T) => R;
  // 自动刷新间隔(ms)，0表示不自动刷新
  refreshInterval?: number;
  // 是否立即获取数据
  immediate?: boolean;
  // 依赖的设备ID，如果设置了这个值，只有在设备ID匹配时才会获取数据
  deviceId?: string | null;
  // 依赖的设备类型，如果设置了这个值，只有在设备类型匹配时才会获取数据
  deviceType?: string | null;
}

// 创建单个异步数据源
function createSingleAsyncStore<T, R = T>(options: AsyncStoreOptions<T, R>) {
  const data = ref<R | null>(null);
  const error = ref<Error | null>(null);
  const isLoading = ref(false);
  const lastUpdated = ref<Date | null>(null);
  const isEnabled = ref(true);

  // 获取设备选择状态
  const { selectedDeviceId, selectedDeviceType } = useDeviceSelection();
  // 监听设备选择变化
  watch(
    () => [selectedDeviceId.value, selectedDeviceType.value],
    ([newDeviceId, newDeviceType]) => {
      // 只有当明确声明了设备依赖时才进行设备相关的判断
      const hasDeviceDependency = "deviceId" in options || "deviceType" in options;

      if (hasDeviceDependency) {
        // 检查是否满足设备依赖条件
        const deviceIdMatch = !options.deviceId || options.deviceId === newDeviceId;
        const deviceTypeMatch = !options.deviceType || options.deviceType === newDeviceType;
        isEnabled.value = deviceIdMatch || deviceTypeMatch;
        // console.log("isEnabled", options.deviceId, options.deviceType, newDeviceId, newDeviceType, isEnabled.value);

        // 如果满足条件且有数据需要刷新，则刷新数据
        if (isEnabled.value && (options.immediate || data.value !== null)) {
          fetchData();
        } else if (!isEnabled.value) {
          // 如果不满足条件，清空数据
          data.value = null;
          error.value = null;
          lastUpdated.value = null;
        }
      }
      // 如果没有声明设备依赖，则不进行任何操作
    },
    { immediate: true }
  );

  // 获取数据的核心函数
  async function fetchData() {
    if (isLoading.value || !isEnabled.value) return;

    isLoading.value = true;
    error.value = null;

    try {
      const rawData = await options.fetcher();
      data.value = options.transform ? options.transform(rawData) : (rawData as unknown as R);
      lastUpdated.value = new Date();
    } catch (e) {
      error.value = e as Error;
      console.error("Fetch error:", e);
    } finally {
      isLoading.value = false;
    }
  }

  // 设置自动刷新
  let intervalControl: { pause: () => void; resume: () => void } | null = null;
  if (options.refreshInterval && options.refreshInterval > 0) {
    intervalControl = useIntervalFn(
      () => {
        if (isEnabled.value) {
          fetchData();
        }
      },
      options.refreshInterval,
      { immediate: false }
    );
  }

  // 自动处理组件生命周期
  onMounted(() => {
    if (options.immediate && isEnabled.value) {
      fetchData();
    }
    intervalControl?.resume();
  });

  onUnmounted(() => {
    intervalControl?.pause();
  });

  // 计算属性：检查数据是否已过期
  const isStale = computed(() => {
    if (!lastUpdated.value || !options.refreshInterval || !isEnabled.value) return false;
    return Date.now() - lastUpdated.value.getTime() > options.refreshInterval;
  });

  return {
    data,
    error,
    isLoading,
    isStale,
    lastUpdated,
    isEnabled,
    refresh: fetchData,
    pauseAutoRefresh: intervalControl?.pause,
    resumeAutoRefresh: intervalControl?.resume
  };
}

// 集中管理所有异步数据源的store
export const createAsyncStoreManager = () => {
  const stores = new Map<string, ReturnType<typeof createSingleAsyncStore>>();
  console.log("集中管理所有异步数据源的store", stores);

  function register<T, R = T>(key: string, options: AsyncStoreOptions<T, R>) {
    if (stores.has(key)) {
      console.warn(`Store with key ${key} already exists. It will be overwritten.`);
    }
    const store = createSingleAsyncStore(options);
    stores.set(key, store);
    return store;
  }

  function getStore(key: string, throwError = true) {
    const store = stores.get(key);
    if (!store && throwError) {
      throw new Error(`Store with key ${key} not found`);
    }
    return store;
  }

  function refreshAll() {
    return Promise.all(Array.from(stores.values()).map(store => store.refresh()));
  }

  function pauseAll() {
    stores.forEach(store => store.pauseAutoRefresh?.());
  }

  function resumeAll() {
    stores.forEach(store => store.resumeAutoRefresh?.());
  }

  return {
    register,
    getStore,
    refreshAll,
    pauseAll,
    resumeAll
  };
};

// 创建全局单例
export const useAsyncStoreManager = createGlobalState(() => createAsyncStoreManager());
