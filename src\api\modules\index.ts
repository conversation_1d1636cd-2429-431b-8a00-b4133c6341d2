import http from "@/api";
/**
 * @name 综合管理平台-系统管理-api接口统一出口
 */

// 获取用户菜单列表
export const getAsyncRoutes = () => {
  return http.post("/usercenter-server/system/resource/getAuthSystemResourceDtoTree");
};

// 个人中心-修改个人信息
export const updatePersonInfo = params => {
  return http.post("/usercenter-server/system/employee/updatePersonInfo", params);
};
// 系统管理-菜单管理-获取菜单列表
export const getMenuList = () => {
  return http.post("/usercenter-server/system/resource/getSystemResourceDtoTree");
};
// 系统管理-菜单管理-删除菜单、菜单资源
export const deleteMenu = params => {
  return http.delete("/usercenter-server/system/resource/deleteById", params);
};
// 系统管理-菜单管理-获取菜单或按钮权限资源，不带分页
export const getMenuSelectList = params => {
  return http.post("/usercenter-server/system/resource/list", params);
};

// 添加、编辑用户菜单
export const addUserMenu = params => {
  return http.post("/usercenter-server/system/resource/save", params);
};
// 系统管理-角色管理-获取角色列表
export const getRoleList = params => {
  return http.post("/usercenter-server/system/role/listQueryByPage", params);
};
// 系统管理-角色管理-添加/编辑 角色
export const addRole = params => {
  return http.post("/usercenter-server/system/role/save", params);
};
// 系统管理-角色管理-删除角色
export const deleteRole = params => {
  return http.delete("/usercenter-server/system/role/deleteById", params);
};
// 获取该角色已经授权的数据
export const fetchRoleMenusList = params => {
  return http.post("/usercenter-server/system/roleResource/list", params);
};
// 系统管理-角色管理-编辑功能权限
export const fetchGrantMenu = params => {
  return http.post("/usercenter-server/system/roleResource/grantMenu", params, {
    headers: {
      "Content-Type": "application/json"
    }
  });
};
// 获取所有的菜单以及资源权限
export const getMenusTreeWithFn = () => {
  return http.post("/usercenter-server/system/resource/getSystemResourceDtoTreeByFunction");
};
// 系统管理-用户管理-获取用户列表
export const getUserList = params => {
  return http.post("/usercenter-server/system/user/listQueryByPage", params);
};
// 系统管理-用户管理-获取所有用户列表
export const getAllUserList = () => {
  return http.post("/usercenter-server/system/user/getUserInfoList", undefined, { cancel: false, loading: false });
};

// 系统管理-用户管理-新增用户
export const addUser = params => {
  return http.post("/usercenter-server/system/user/saveDto", params);
};
// 系统管理-用户管理-删除用户
export const deleteUser = params => {
  return http.delete("/usercenter-server/system/user/deleteById", params);
};
// 系统管理-用户管理-冻结/解冻用户
export const updateUserStatus = params => {
  return http.post("/usercenter-server/system/user/updateStatus", params);
};
// 系统管理-用户管理-重置密码
export const resetPassword = params => {
  return http.post("/usercenter-server/system/user/resetPassword", params);
};
// 系统管理-组织机构-获取列表
export const getOrgList = () => {
  return http.post("/usercenter-server/system/org/getSystemOrgDtoTree");
};

// 系统管理-组织机构-获取组织下的用户列表
export const getOrgById = params => {
  return http.post("/usercenter-server/system/org/getById", params);
};

// 系统管理-组织机构-新增/编辑组织
export const addOrg = params => {
  return http.post("/usercenter-server/system/org/save", params);
};
// 系统管理-组织机构-删除组织
export const deleteOrg = params => {
  return http.delete("/usercenter-server/system/org/deleteById", params);
};
// 系统管理-参数配置-获取参数列表
export const getParamList = params => {
  return http.post("/usercenter-server/system/disposition/listQueryByPage", params);
};
// 系统管理-参数配置-新增/编辑参数
export const addParam = params => {
  return http.post("/usercenter-server/system/disposition/save", params);
};

// 系统管理-参数配置-删除参数
export const deleteParam = params => {
  return http.delete("/usercenter-server/system/disposition/deleteById", params);
};

// 系统管理-基础参数-物料管理-获取物料列表
export const getMaterialList = params => {
  return http.post("/usercenter-server/system/material/listQueryByPage", params);
};

// 系统管理-基础参数-物料管理-新增/编辑物料
export const addMaterial = params => {
  return http.post("/usercenter-server/system/material/save", params);
};
// 系统管理-基础参数-物料管理-矿卡修改
export const updateMaterialTrain = params => {
  return http.post("/usercenter-server/system/materialTrain/save", params);
};
// 系统管理-基础参数-物料管理-矿卡载重保存
export const materialTrainSaveList = params => {
  return http.post("/usercenter-server/system/materialTrain/saveList", params, {
    headers: {
      "Content-Type": "application/json"
    }
  });
};

// 系统管理-基础参数-物料管理-删除物料
export const deleteMaterial = params => {
  return http.delete("/usercenter-server/system/material/deleteById", params);
};

// 系统管理-基础参数-物料管理-矿卡列表
export const getMaterialTrainList = () => {
  return http.post("/usercenter-server/system/materialTrain/listQueryMaterialTrain");
};
// 系统管理-基础参数-班次管理-获取班次列表
export const getFlightList = params => {
  return http.post("/usercenter-server/system/flights/listQueryByPage", params);
};
// 系统管理-基础参数-班次管理-获取所有班次列表
export const getAllFlightPagelist = () => {
  return http.post("/usercenter-server/system/flights/list");
};
// 系统管理-基础参数-班次管理-新增/编辑班次
export const addFlight = params => {
  return http.post("/usercenter-server/system/flights/save", params);
};
// 系统管理-基础参数-班次管理-删除
export const deleteFlight = params => {
  return http.delete("/usercenter-server/system/flights/deleteById", params);
};

// 系统管理-字典管理/字典列表-列表查询
export const getDictList = params => {
  return http.post("/usercenter-server/system/dictionary/listQueryByPage", params);
};

// 系统管理-字典管理/字典列表-新增/编辑
export const addDict = params => {
  return http.post("/usercenter-server/system/dictionary/save", params);
};

// 系统管理-字典管理/字典列表-删除
export const deleteDict = params => {
  return http.delete("/usercenter-server/system/dictionary/deleteById", params);
};
// 获取字典枚举项
export const getDictionaryEnum = params => {
  return http.post("/usercenter-server/system/dictionary/queryChildrenByCode", params);
};
// 系统管理-字典管理/字典列表-修改为系统字典
export const updateDict = params => {
  return http.post("/usercenter-server/system/dictionary/updateSystemDictionary", params);
};
// 系统管理-操作日志-操作记录-列表查询
export const getOperateLogList = params => {
  return http.post("/usercenter-server/system/operateLog/listQueryByPage", params);
};
// 系统管理-操作日志-登录日志-列表查询
export const getLoginLogList = params => {
  return http.post("/usercenter-server/system/loginRecord/listQueryByPage", params);
};
