<template>
  <form-create v-model:api="fApi" :option="option" :rule="rule"></form-create>
</template>
<script lang="tsx" setup>
/**
 * @file 系统管理-字典管理-列表页
 * <AUTHOR>
 * @date 2024/11/14
 */
import { ref } from "vue";
import { columnMap } from "./types";
import { dictListFormCreate } from "./components/formCreate";
import formCreate from "@form-create/element-ui";
import { useRoute } from "vue-router";

import { addDict, deleteDict, getDictList } from "@/api/modules";
const fApi = ref();
const router: any = useRoute();
const option = {
  form: { inline: true },
  resetBtn: false,
  submitBtn: false
};

// 获取路由的参数
const options = JSON.parse(router?.query.option as string);
const rule = ref([
  {
    type: "SearchFormOperation",
    field: "v:search",
    wrap: { style: "marginBottom: 0" },

    children: [
      {
        type: "input",
        field: "search",
        props: {
          size: "default",
          placeholder: "字典名称/编码"
        }
      },
      // 新增
      {
        type: "AddBtn",
        slot: "suffix",

        props: {
          btn: { content: "新增字典", auth: "add" },
          // isCustomDialog是否是自定义表单弹窗 未嵌入表格只是显示弹窗form 与formatter搭配使用
          isCustomDialog: true,
          dialog: { title: "新增字典" },
          formatter: () => {
            // 回显上级字典数据
            return { pcode: options.code, pname: options.dname };
          },
          size: "default",
          submitRequest: addDict
        },
        children: [dictListFormCreate]
      }
    ]
  },
  {
    type: "ProTable",
    props: {
      columns: [
        {
          prop: columnMap.get("字典名称"),
          label: "字典名称"
        },
        {
          prop: columnMap.get("字典编码"),
          label: "字典编码"
        },
        {
          prop: columnMap.get("上级字典"),
          label: "上级字典",
          render: () => {
            return options.dname;
          }
        },

        { prop: "operation", label: "操作", fixed: "right" }
      ],
      fetch: param => {
        return getDictList({ ...param, pcode: options.code }).then(res => {
          return res;
        });
      },
      operations: [
        { content: "修改", action: "edit", auth: "update" },
        { content: "删除", action: "delete", props: { style: { color: "rgba(242, 85, 85, 1)" }, auth: "delete" } }
      ]
    },

    children: [
      {
        type: "EditBtn",
        props: {
          data: { pcode: options.code, pname: options.dname },
          action: "edit",
          dialog: { title: "修改字典" },
          submitRequest: addDict
        },
        on: {
          // 打开弹窗抛出的事件
          init(row, api) {
            api.children[0]?.setValue({ pcode: options.code, pname: options.dname });
          }
        },

        children: [dictListFormCreate]
      },
      {
        type: "ConfirmDialog",
        on: {
          // 监听弹窗组件抛出的的afterSubmit事件，用于刷新页面
          afterSubmit: () => {
            // 刷新，调用组件内部请求方法
            fApi.value.exec("v:search", "onSearch");
          }
        },
        props: {
          action: "delete",
          // 模拟请求param：参数
          submitRequest: deleteDict
        }
      }
    ]
  }
]);
</script>
<style lang="scss" scoped>
:deep(.el-row) {
  height: var(--page-height);
}

.el-form-item {
  margin-right: 4px !important;
}

:deep(.search-form__operator) {
  margin-bottom: 10px;
}
</style>
