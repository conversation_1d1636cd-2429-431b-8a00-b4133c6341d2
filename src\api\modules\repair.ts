import http from "@/api";
/**
 * @name 综合管理平台-维修保养-api接口统一出口
 */
// 维修保养-维修工单-修改矿卡
export const deviceRepairWorkOrderSave = (params: any) => {
  return http.post("/wcs-server/wcs/deviceRepairWorkOrder/save", params);
};
// 维修保养-维修工单-查询列表
export const getRepairWorkOrderList = (params: any) => {
  return http.post("/wcs-server/wcs/deviceRepairWorkOrder/listQueryByPage", params);
};
// 维修保养-维修工单-关闭工单
export const closeWorkOrder = (params: any) => {
  return http.post("/wcs-server/wcs/deviceRepairWorkOrder/closeWorkOrder", params);
};
// 维修保养-维修工单-填写维修结果
export const modifyWorkOrder = (params: any) => {
  return http.post("/wcs-server/wcs/deviceRepairWorkOrder/modifyWorkOrder", params);
};
// 维修保养-维修工单-转让工单
export const transferWorkOrder = (params: any) => {
  return http.post("/wcs-server/wcs/deviceRepairWorkOrder/transferWorkOrder", params);
};
// 维修保养-维修工单-查询维修工单详情
export const getWorkOrderDetail = (params: any) => {
  return http.post("/wcs-server/wcs/deviceRepairWorkOrder/getById", params);
};
// 维修保养-维修工单-查询维修记录
export const getRepairRecordList = (params: any) => {
  return http.post("/wcs-server/wcs/deviceRepairRecord/list", params);
};
// 维修保养-维修经验库-查询矿车型号
export const getRepairExperienceModel = (params: any) => {
  return http.post("/wcs-server/wcs/deviceRepairExperience/queryModel", params, {
    loading: false
  });
};
// 维修保养-维修经验库-查询记录
export const getRepairExperienceList = (params: any) => {
  return http.post("/wcs-server/wcs/deviceRepairExperience/listQueryByPage", params);
};
// 维修保养-维修经验库-保存记录
export const saveRepairExperience = (params: any) => {
  return http.post("/wcs-server/wcs/deviceRepairExperience/save", params);
};
// 维修保养-维修经验库-删除记录
export const deleteRepairExperience = (params: any) => {
  return http.delete("/wcs-server/wcs/deviceRepairExperience/deleteByIds", params);
};
// 维修保养-维修经验库-保存记录详情里的经验
export const saveRepairExperienceRecord = (params: any) => {
  return http.post("/wcs-server/wcs/deviceRepairExperienceRecord/save", params);
};
// 维修保养-维修经验库-删除记录详情里的经验
export const deleteRepairExperienceRecordById = (params: any) => {
  return http.delete("/wcs-server/wcs/deviceRepairExperienceRecord/deleteById", params);
};
// 维修保养-维修经验库-查询记录详情列表
export const getRepairExperienceRecordList = (params: any) => {
  return http.post("/wcs-server/wcs/deviceRepairExperienceRecord/list", params);
};
// 维修保养-保养工单-查询保养工单
export const getMaintenanceWorkOrderList = (params: any) => {
  return http.post("/wcs-server/wcs/deviceMaintenanceWorkOrder/listQueryByPage", params);
};
// 维修保养-保养工单-查询保养间隔
export const getMaintenanceCycleList = (params: any) => {
  return http.post("/wcs-server/wcs/deviceMaintenanceKnowledge/queryAllCycleList", params);
};
// 维修保养-保养工单-查询保养工单详情
export const getMaintenanceWorkOrderDetail = (params: any) => {
  return http.post("/wcs-server/wcs/deviceMaintenanceWorkOrder/getDetailById", params);
};
// 维修保养-保养工单-查询保养项目
export const getMaintenancePositionList = (params: any) => {
  return http.post("/wcs-server/wcs/deviceWorkOrderKnowledge/getMaintenancePosition", params);
};
// 维修保养-保养工单-派工
export const assignPerson = (params: any) => {
  return http.post("/wcs-server/wcs/deviceMaintenanceWorkOrder/assignPerson", params);
};
// 维修保养-保养工单-填写保养结果
export const maintenanceResultOrder = (params: any) => {
  return http.post("/wcs-server/wcs/deviceMaintenanceWorkOrder/modifyWorkOrder", params);
};
// 维修保养-保养工单-转让工单
export const maintenanceTransferWorkOrder = (params: any) => {
  return http.post("/wcs-server/wcs/deviceMaintenanceWorkOrder/transferWorkOrder", params);
};
// 维修保养-保养工单-查询保养记录
export const getMaintenanceRecordList = (params: any) => {
  return http.post("/wcs-server/wcs/deviceMaintenanceRecord/list", params);
};
// 维修保养-保养经验库-查询记录
export const getMaintenanceExperienceList = (params: any) => {
  return http.post("/wcs-server/wcs/deviceMaintenanceKnowledge/listQueryByPage", params, {
    cancel: false
  });
};
// 维修保养-保养经验库-保存记录
export const saveMaintenanceExperience = (params: any) => {
  return http.post("/wcs-server/wcs/deviceMaintenanceKnowledge/save", params);
};
// 维修保养-保养经验库-删除记录
export const deleteMaintenanceExperience = (params: any) => {
  return http.delete("/wcs-server/wcs/deviceMaintenanceKnowledge/deleteByIds", params);
};
