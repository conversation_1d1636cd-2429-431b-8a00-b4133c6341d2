<template>
  <form-create v-model:api="fApi" :option="option" :rule="rule"></form-create>
</template>
<script lang="tsx" setup>
/**
 * @file 系统管理-角色管理
 * <AUTHOR>
 * @date 2024/11/14
 */
import { ref } from "vue";
import { getRoleList, addRole, deleteRole, fetchGrantMenu } from "@/api/modules";
import { columnMap, RoleTypeEnum } from "./types";
import { roleFormCreate, permissionsFormCreate } from "./components/utils";
import formCreate from "@form-create/element-ui";
import PermissionDialog from "./components/GrantAuth.vue";
import { useUserStore } from "@/stores/modules/user";

formCreate.component("PermissionDialog", PermissionDialog);

// 获取个人信息
const userInfo = useUserStore().userInfo;
const fApi = ref();
const option = {
  form: { inline: true },
  resetBtn: false,
  submitBtn: false
};

const rule = ref([
  {
    type: "SearchFormOperation",
    field: "v:search",
    wrap: { style: "marginBottom: 0" },
    children: [
      {
        type: "input",
        field: "search",
        props: {
          size: "default",
          placeholder: "角色名称/编码"
        }
      },
      {
        type: "AddBtn",
        slot: "suffix",
        props: {
          btn: { content: "新增角色", auth: "add" },
          dialog: { title: "新增角色" },
          size: "default",
          submitRequest: data => {
            // 提交的请求
            return addRole(data);
          }
        },
        children: [roleFormCreate]
      }
    ]
  },
  {
    type: "ProTable",
    props: {
      columns: [
        {
          prop: columnMap.get("角色名称"),
          label: "角色名称",
          render(scope: any) {
            return (
              <div>
                {scope.row.roleType === RoleTypeEnum.PRESUPPOSE_ROLE ? (
                  <el-tag type="primary" effect="plain" size="small" style={{ marginRight: "2px", padding: "0 4px" }}>
                    预设
                  </el-tag>
                ) : null}
                {scope.row[columnMap.get("角色名称")]}
              </div>
            );
          }
        },
        {
          prop: columnMap.get("编码"),
          label: "角色编码"
        },
        { prop: "operation", label: "操作", fixed: "right" }
      ],

      fetch: params => {
        return getRoleList(params);
      },
      operationDisabled: row => {
        // 如果是当前用户为超管角色则可以进行操作,超管角色一直处于禁用状态
        if (row.roleCode === "ROLE_ADMIN") return true;
        if (userInfo.roleCode === "ROLE_ADMIN") return false;
        return [RoleTypeEnum.PRESUPPOSE_ROLE].includes(row.roleType);
      },
      operations: [
        {
          content: "功能权限",
          action: "permissions",
          auth: "update",
          onClick: row => {
            // 点击传递参数
            fApi.value.findRule({ name: "permissionsConfig" }).children[0].props.rule[0].props.roleId = row.id;
          }
        },
        { content: "修改", action: "edit", auth: "update" },
        { content: "删除", action: "delete", auth: "delete", props: { style: { color: "rgba(242, 85, 85, 1)" } } }
      ]
    },
    children: [
      {
        type: "EditBtn",
        name: "permissionsConfig",
        props: {
          action: "permissions",
          dialog: {
            title: "功能权限",
            // 绑定到弹窗根节点的样式
            class: "dialog-custom-width permissions-custom"
          },
          submitRequest: data => {
            // console.log("功能权限", data);

            const params = { resourceList: data.resourceList, roleId: data.id };
            // 提交的请求
            return fetchGrantMenu(params);
          }
        },
        children: [permissionsFormCreate]
      },
      {
        type: "EditBtn",
        props: {
          action: "edit",
          dialog: { title: "修改角色" },
          submitRequest: data => {
            // 提交的请求
            return addRole(data);
          }
        },
        children: [roleFormCreate]
      },
      {
        type: "ConfirmDialog",
        on: {
          // 监听弹窗组件抛出的的afterSubmit事件，用于刷新页面
          afterSubmit: () => {
            // 刷新，调用组件内部请求方法
            fApi.value.exec("v:search", "onSearch");
          }
        },
        props: {
          action: "delete",
          subtitle: row => {
            return row.roleName;
          },
          title: "是否删除角色",
          message: "删除后不可恢复",
          // 请求param：参数
          submitRequest: param => {
            // 模拟提交的请求为2秒
            return deleteRole(param);
          }
        }
      }
    ]
  }
]);
</script>
<style lang="scss" scoped>
:deep(.el-row) {
  height: calc(100vh - 138px) !important;
}

.el-form-item {
  margin-right: 4px !important;
}
</style>
<style lang="scss">
.permissions-custom {
  .el-form-item--large {
    margin-bottom: 0;
  }

  .el-dialog__body {
    padding: 20px;
  }
}
</style>
