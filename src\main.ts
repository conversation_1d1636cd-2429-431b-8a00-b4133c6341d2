import { createApp } from "vue";
import formCreate from "@form-create/element-ui";
import install from "@form-create/element-ui/auto-import";
import registerComponents from "./components";

import App from "./App.vue";
// reset style sheet
import "@/styles/reset.scss";
// CSS common style sheet
import "@/styles/common.scss";
// iconfont css
import "@/assets/iconfont/iconfont.scss";

// element css
import "element-plus/dist/index.css";
// element dark css
import "element-plus/theme-chalk/dark/css-vars.css";
// custom element dark css
import "@/styles/element-dark.scss";
// custom element css
import "@/styles/element.scss";
// svg icons
import "virtual:svg-icons-register";
// element plus
import ElementPlus from "element-plus";
// element icons
import * as Icons from "@element-plus/icons-vue";
// custom directives
import directives from "@/directives/index";
// vue Router
import router from "@/routers";
// pinia store
import pinia from "@/stores";

const app = createApp(App);
// app.config.errorHandler = errorHandler;

// register the element Icons components
Object.keys(Icons).forEach(key => {
  app.component(key, Icons[key as keyof typeof Icons]);
});

const getAllRules = (rule: any): any[] => {
  const result = [rule];

  // 递归检查 children 属性
  if (rule.children && Array.isArray(rule.children) && rule.children.length > 0) {
    rule.children.forEach((child: any) => {
      result.push(...getAllRules(child));
    });
  }

  // 递归检查 props.rule 属性
  if (rule.props && rule.props.rule) {
    if (Array.isArray(rule.props.rule)) {
      rule.props.rule.forEach((propRule: any) => {
        result.push(...getAllRules(propRule));
      });
    } else {
      result.push(...getAllRules(rule.props.rule));
    }
  }

  return result;
};
formCreate.extendApi(api => {
  return {
    // 通过属性查找对应的组件rule
    findRule(filter: { [key: string]: any }) {
      const rule = getAllRules({ children: api.rule });
      return rule.find(item => {
        return Object.keys(filter).every(key => {
          const keyArr = key.split(".");
          const value = keyArr.reduce((pre, cur) => {
            return pre[cur];
          }, item);
          return value === filter[key];
        });
      });
    }
  };
  // return api;
});
formCreate.use(install);
registerComponents(app);
// @ts-ignore
app.use(ElementPlus).use(directives).use(router).use(pinia).use(formCreate).mount("#app");
