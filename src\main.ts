import { createApp } from "vue";
import formCreate from "@form-create/element-ui";
import install from "@form-create/element-ui/auto-import";
import registerComponents from "./components";

// import "dayjs/locale/zh-cn";
import App from "./App.vue";
// reset style sheet
import "@/styles/reset.scss";
// CSS common style sheet
import "@/styles/common.scss";
// iconfont css
import "@/assets/iconfont/iconfont.scss";

// element css
import "element-plus/dist/index.css";
// element dark css
import "element-plus/theme-chalk/dark/css-vars.css";
// custom element dark css
import "@/styles/element-dark.scss";
// custom element css
import "@/styles/element.scss";
// svg icons
import "virtual:svg-icons-register";
// element plus
import ElementPlus from "element-plus";
// element icons
import * as Icons from "@element-plus/icons-vue";
// custom directives
import directives from "@/directives/index";
// vue Router
import router from "@/routers";
// pinia store
import pinia from "@/stores";
import * as echarts from "echarts";
import { ThemeColors } from "@/styles/theme/echarts";
const getPercentWithPrecision = echarts.number.getPercentWithPrecision;

const app = createApp(App);
app.provide("ThemeColors", ThemeColors);
// app.config.errorHandler = errorHandler;
/**
 * 获取给定精度的数据，确保 numArr 中的百分比之和为1。
 * 使用最大余数法。
 * getPercentWithPrecision
 * @param numArr a list of all data
 * @param idx index of the data to be processed in numArr
 * @param precision integer number showing digits of precision
 * @return percent ranging from 0 to 100
 */
const $getPercentWithPrecision = (numArr: number[], precision: number = 2) => {
  const result: number[] = [];
  for (let idx = 0; idx < numArr.length; idx++) {
    const ratio = getPercentWithPrecision(numArr, idx, precision);
    result.push(ratio);
  }
  return result;
};
app.provide("getPercentWithPrecision", $getPercentWithPrecision);

// register the element Icons components
Object.keys(Icons).forEach(key => {
  app.component(key, Icons[key as keyof typeof Icons]);
});

const getAllRules = (rule: any): any[] => {
  const result = [rule];

  // 递归检查 children 属性
  if (rule.children && Array.isArray(rule.children) && rule.children.length > 0) {
    rule.children.forEach((child: any) => {
      result.push(...getAllRules(child));
    });
  }

  // 递归检查 props.rule 属性
  if (rule.props && rule.props.rule) {
    if (Array.isArray(rule.props.rule)) {
      rule.props.rule.forEach((propRule: any) => {
        result.push(...getAllRules(propRule));
      });
    } else {
      result.push(...getAllRules(rule.props.rule));
    }
  }

  return result;
};
formCreate.extendApi(api => {
  return {
    // 通过属性查找对应的组件rule
    findRule(filter: { [key: string]: any }) {
      const rule = getAllRules({ children: api.rule });
      return rule.find(item => {
        return Object.keys(filter).every(key => {
          const keyArr = key.split(".");
          const value = keyArr.reduce((pre, cur) => {
            return pre[cur];
          }, item);
          return value === filter[key];
        });
      });
    }
  };
  // return api;
});
formCreate.use(install);
registerComponents(app);
// @ts-ignore
app.use(ElementPlus).use(directives).use(router).use(pinia).use(formCreate).mount("#app");
