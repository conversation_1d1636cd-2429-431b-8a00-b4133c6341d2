<template>
  <form-create v-model:api="fApi" :option="option" :rule="rule"> </form-create>
</template>
<script lang="tsx" setup>
import { ref } from "vue";
import formCreate from "@form-create/element-ui";
import { departmentFormCreate } from "./utils";
import SearchHeader from "./SearchHeader.vue";
import { getUserList, getOrgList, deleteOrg, addOrg } from "@/api/modules";
import { columnMap, statusEnum } from "@/views/system/userManage/types";
import { useUserStore } from "@/stores/modules/user";
// 获取个人信息
const userInfo = useUserStore().userInfo;
// 表格组件
const fApi = ref();
// 激活项
const option = { resetBtn: false, submitBtn: false, row: { gutter: 4 }, form: { inline: true } };
const handleTreeData = res => {
  return [
    {
      id: undefined,
      fullName: "全部",
      isRoot: true,
      children: res.data
    }
  ];
};
const rule = ref([
  {
    type: "el-col",
    class: "form-item-unset",
    style: {
      minWidth: "280px"
    },
    props: { span: 4 },
    children: [
      {
        type: "LACard",
        style: "overflow: auto",
        props: { showHeader: false },
        children: [
          {
            // 左侧树组件
            type: "LATree",
            field: "v:tree",
            props: {
              // 默认展开
              defaultExpandAll: true,
              // 搜索框
              needFilter: true,
              // 参数定义
              treeProps: { label: "fullName" },
              nodeKey: "id",
              // 请求接口
              fetch: getOrgList,
              // 数据结构处理
              handleData: handleTreeData,
              // 控制显示小图标
              isShowIcon: true,
              // 下拉选项
              operations: (row, api) => {
                // 根目录只显示新增
                if (row.id)
                  return [
                    { content: "新增", action: "add" },
                    { content: "修改", action: "edit" },
                    { content: "删除", action: "delete", props: { style: { color: "rgba(242, 85, 85, 1)" } } }
                  ];
                else return [{ content: "新增", action: "add", props: { type: "primary" } }];
              }
            },
            on: {
              // 监听组件抛出的的currentChange事件
              currentChange: (val, api) => {
                api.findRule({ name: "SearchHeader" }).props.title = val.fullName;
                api.findRule({ name: "ProTable" }).props.params = { orgId: val.id };
              }
            },
            children: [
              {
                type: "AddBtn",
                props: {
                  action: "add",
                  btn: { show: false },
                  dialog: { title: "新增部门" },
                  /**
                   * 处理数据弹窗方法,弹窗生成前设置需要的值
                   * @param data 获取的表单数据
                   * @return {Object} 返回的数据
                   * <AUTHOR>
                   * @date 2024/11/7
                   * */
                  formatter: (data: any): object => {
                    return { pname: data.pname || "全部", pcode: data.id || "" };
                  },
                  submitRequest: addOrg
                },
                children: [departmentFormCreate]
              },
              {
                type: "EditBtn",
                props: {
                  action: "edit",
                  dialog: {
                    title: "修改菜单"
                  },
                  /**
                   * 处理数据弹窗方法,弹窗生成前设置需要的值
                   * @param data 获取的表单数据
                   * @return {Object} 返回的数据
                   * <AUTHOR>
                   * @date 2024/11/7
                   * */
                  formatter: (data: any): object => {
                    return { ...data, pname: data.pname || "全部", id: data.id || "" };
                  },
                  submitRequest: addOrg
                },
                children: [departmentFormCreate]
              },
              {
                type: "ConfirmDialog",
                on: {
                  // 监听弹窗组件抛出的的afterSubmit事件，用于刷新页面
                  afterSubmit: () => {
                    // 刷新，调用组件内部请求方法
                    fApi.value.exec("v:tree", "fetchData");
                  }
                },
                props: {
                  action: "delete",
                  subtitle: row => {
                    return row.fullName;
                  },
                  title: "是否删除部门",
                  message: "删除后不可恢复",
                  // 模拟请求param：参数
                  submitRequest: deleteOrg
                }
              }
            ]
          }
        ]
      }
    ]
  },
  {
    type: "el-col",
    props: { span: 20 },
    class: "form-item-unset",
    style: "display: flex; flex-direction: column;",
    children: [
      {
        component: SearchHeader,
        props: {
          title: "全部"
        },
        name: "SearchHeader",
        children: [
          {
            type: "SearchFormOperation",
            children: [
              {
                type: "input",
                field: "search",
                props: {
                  size: "default",
                  placeholder: "账号/手机号/姓名"
                }
              }
            ]
          }
        ]
      },
      {
        type: "ProTable",
        name: "ProTable",
        props: {
          columns: [
            { prop: "userName", label: "账号" },
            {
              prop: "employeeName",
              label: "真实姓名",
              render(scope: any) {
                return (
                  <div>
                    {scope.row.id === userInfo.id ? (
                      <el-tag type="primary" effect="plain" size="small" style={{ marginRight: "2px", padding: "0 4px" }}>
                        我
                      </el-tag>
                    ) : null}
                    {scope.row[columnMap.get("账号")] || ""}
                  </div>
                );
              }
            },
            { prop: "mobile", label: "手机号码" },
            {
              prop: "roleName",
              label: "角色"
            },
            { prop: "orgName", label: "所属部门" },
            { prop: "job", label: "职位" },
            {
              prop: "status",
              label: "状态",
              tag: true,
              enum: [...statusEnum]
            }
          ],
          fetch: getUserList
        }
      }
    ]
  }

  // 右侧组件详情
]);
</script>
<style lang="scss" scoped>
:deep(.el-row) {
  height: 100%;
  flex-wrap: nowrap;
  width: 100%;
}
:deep(.el-table__header) {
  border-top: 1px solid rgba(235, 237, 241, 1);
}
:deep(.table-main) {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
:deep(.search-header) {
  box-sizing: border-box;
  height: 72px;
  flex: none;
}
:deep(.table-box) {
  flex: 1;
}
//:deep(.form-item-clear) {
//  .el-form-item,
//  .el-form-item__content {
//    width: 100%;
//  }
//}
</style>
