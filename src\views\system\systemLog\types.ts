/**
 * @file 系统管理-操作日志-类型声明文件
 * <AUTHOR>
 * @date 2024/11/14
 */
// 操作记录表格字段声明
export const columnMap = new Map([
  ["租户ID", "tenantId"],
  ["操作人/账号", "operatorName"],
  ["操作端", "operateDeck"],
  ["服务模块", "owningModuleName"],
  ["调用接口", "api"],
  ["请求方式", "requestMethod"],
  ["响应时长", "responseTime"],
  ["状态", "status"],
  ["设备IP", "deviceIp"],
  ["操作时间", "createDate"]
]);
// 登录日志表格字段声明
export const loginLogColumnMap = new Map([
  ["操作人/账号", "userName"],
  ["操作端", "operateDeck"],
  ["设备IP", "ip"],
  ["登录时间", "createDate"]
]);
// 操作记录状态枚举
export enum OperationStatus {
  SUCCESS = 101,
  FAIL = 102
}
// 状态额外的UI信息
export const operationStatusInfo = [
  // 启用
  {
    bg: "rgba(0, 194, 144, .1)",
    status: OperationStatus.SUCCESS,
    text: "正常",
    color: "rgba(0, 194, 144, 1)"
  },
  // 禁用
  {
    status: OperationStatus.FAIL,
    text: "异常",
    bg: "rgba(242, 85, 85, 0.1)",
    color: "rgba(242, 85, 85, 1)"
  }
];
