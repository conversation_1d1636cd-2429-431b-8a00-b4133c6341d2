/**
 * @file 账号中心-表单声明文件
 * <AUTHOR>
 * @date 2024/11/13
 */

import { formMap } from "@/views/system/userManage/types";
import LASelect from "@/components/LASelect";
import { getRoleList } from "./api";
import { passwordFormMap } from "./types";
import { ref } from "vue";

import { logoutApi } from "@/api/modules/login";

import { ElMessage } from "element-plus";
import { useUserStore } from "@/stores/modules/user";

const userStore = useUserStore();
export const infoFormCreate = {
  type: "form-create",
  props: {
    rule: [
      {
        type: "input",
        field: formMap.get("账号"),
        props: {
          disabled: true
        },

        title: "账号",
        validate: [{ required: true, message: "请输入账号" }]
      },
      {
        type: "input",
        field: formMap.get("真实姓名"),
        title: "真实姓名",
        validate: [
          { required: true, message: "请输入真实姓名" },
          {
            pattern: /^.{1,20}$/,
            message: "字符限长20位"
          }
        ]
      },
      {
        type: "input",
        field: formMap.get("手机号码"),
        inject: true,
        // on: {
        //   input: (inject, option) => {
        //     console.log("inject", inject);
        //     // 更新请求参数
        //     inject.api.getRule(formMap.get("角色id")).props.params.roleType = option;
        //     // 发送请求
        //     inject.api.exec(formMap.get("角色id"), "fetchData");
        //     // 如果是数组的话每一项取id
        //   }
        // },
        title: "手机号码",
        validate: [
          { required: true, message: "请输入手机号码" },
          {
            pattern: /^1[3-9]\d{9}$/,
            message: "请输入正确的手机号码"
          }
        ]
      },
      // LASelect组件式
      {
        component: LASelect,
        field: formMap.get("角色id"),
        props: {
          selectDisabled: true,
          replaceFields: { key: "id", label: "roleName", value: "id" },
          fetch: getRoleList,
          multiple: true
        },
        title: "角色",
        validate: [{ required: true, message: "请选择角色" }]
      },
      {
        type: "input",
        field: formMap.get("所属部门名称"),
        props: {
          disabled: true
        },
        title: "所属部门"
      },
      {
        type: "input",
        field: formMap.get("职位"),
        props: {
          disabled: true
        },
        title: "职位"
      }
    ],
    option: {
      submitBtn: false
    }
  }
};
const data = ref({});
export const passwordFormCreate = {
  type: "form-create",
  props: {
    rule: [
      {
        type: "input",
        field: passwordFormMap.get("旧密码"),
        props: {
          showPassword: true
        },
        validate: [
          {
            required: true,
            validator(rule, value, callback) {
              // console.log(userStore.password, data.value[passwordFormMap.get("旧密码")]);
              if (data.value[passwordFormMap.get("旧密码")] === userStore.password) {
                callback();
              } else {
                callback("旧密码错误");
              }
            }
          }
        ],
        title: "旧密码"
      },
      {
        type: "input",
        field: passwordFormMap.get("新密码"),
        props: {
          showPassword: true
        },
        title: "新密码",
        // 密码必须包含字母、数字,且长度为8到20位
        validate: [
          { required: true, message: "请输入密码" },
          {
            pattern: "^(?=.*[A-Za-z])(?=.*\\d)[A-Za-z\\d\\W]{8,20}$",
            message: "密码必须包含字母、数字,且长度为8到20位"
          }
        ]
      },
      {
        type: "input",
        field: passwordFormMap.get("确认新密码"),
        props: {
          showPassword: true
        },
        title: "再次输入新密码",
        validate: [
          {
            required: true,
            validator(rule, value, callback) {
              if (data.value[passwordFormMap.get("新密码")] === value) {
                callback();
              } else {
                callback("两次输入的密码不一致");
              }
            }
          }
        ]
      }
    ],
    option: {
      submitBtn: false,
      async onSubmit(formData, api) {
        // 1.执行退出登录接口
        await logoutApi();

        // 2.清除 Token、清除信息
        userStore.resetState();
        // 3.重定向到登陆页
        setTimeout(() => {
          window.location.reload();
          ElMessage.success("请重新登陆！");
        }, 200);
      }
    }
  },
  on: {
    "update:modelValue"(val) {
      data.value = val;
    }
  }
};
