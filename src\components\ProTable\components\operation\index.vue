<template>
  <el-dropdown v-if="hasAuthOperations.length > 1" :disabled="disabled">
    <!-- 默认显示操作按钮-->
    <!--如果禁用颜色rgba(202, 217, 252, 1) 否则var(--el-color-primary)-->
    <div
      v-if="!isShowIcon"
      :style="{ color: disabled ? 'rgba(202, 217, 252, 1) !important' : 'var(--el-color-primary)' }"
      class="el-dropdown-link"
    >
      <div>操作</div>
      <el-icon class="el-icon--right">
        <arrow-down />
      </el-icon>
    </div>
    <img v-else alt="" class="more-icon" src="./more_icon.png" />

    <template #dropdown>
      <el-dropdown-menu>
        <slot />
        <template v-for="item in hasAuthOperations" :key="item.content">
          <div v-auth="item.auth" style="height: 30px">
            <el-dropdown-item @click="onClick(item)">
              <el-button
                :icon="item.icon"
                link
                style="width: 100%; height: 100%"
                v-bind="{
                  ...item.props,
                  disabled: typeof item.props?.disabled === 'function' ? item.props.disabled(props.row) : item.props?.disabled
                }"
              >
                {{ item.content ?? currentPageRoles[item.auth!] ?? item.auth }}
              </el-button>
            </el-dropdown-item>
          </div>
        </template>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
  <el-button
    v-for="item in hasAuthOperations"
    v-else
    :key="item.content"
    v-auth="item.auth"
    :icon="item.icon"
    :style="{ color: disabled ? 'rgba(202, 217, 252, 1)' : 'var(--el-color-primary)' }"
    link
    style="height: 100%"
    v-bind="{
      ...(item?.props || {}),
      disabled:
        typeof item?.props?.disabled === 'function' ? item.props.disabled(row) : (item?.props?.disabled ?? disabled ?? false)
    }"
    @click="onClick(item)"
  >
    {{ item.content ?? currentPageRoles[item.auth!] ?? item.auth }}
  </el-button>
</template>
<script lang="ts" setup>
import type { ButtonProps } from "element-plus";
import { Api } from "@form-create/element-ui";
import { Emitter } from "mitt";
import { computed } from "vue";
import { useAuthStore } from "@/stores/modules/auth";

// 定义 disabled 类型
interface CustomButtonProps {
  disabled?: boolean | ((row: any) => boolean);
}

const authStore = useAuthStore();
const currentPageRoles = authStore.currentPageRolesGet;

export interface OperationItem {
  content: string;
  action: string;
  auth?: string;
  icon?: string;
  onClick: (row: { [key: string]: any }, api: Api) => void;
  props?: Partial<ButtonProps> & CustomButtonProps;
}

// table 编辑按钮组
const props = withDefaults(
  defineProps<{
    operations: OperationItem[];
    // 是否禁用
    disabled?: boolean;
    isShowIcon?: boolean;
    row?: { [key: string]: any };
    formCreateInject?: { api: Api };
    mittBus: Emitter<any>;
  }>(),
  {
    operations: () => [],
    row: () => ({}),
    isShowIcon: false
  }
);
// todo 上线后换成下面的代码
const hasAuthOperations = computed(() => {
  return props.operations;
  // return props.operations.filter(item => currentPageRoles[item.auth!]);
});

const onClick = (item: OperationItem) => {
  const api = props.formCreateInject.api;
  item.onClick?.(props.row, api);
  props.mittBus?.emit(`action-${item.action}`, { row: props.row, api });
};
</script>
<style lang="scss" scoped>
.el-dropdown {
  cursor: pointer;
}

.el-dropdown-link {
  display: flex;
  align-items: end;
}

.el-icon--right {
  margin-top: 5px;
}
</style>
