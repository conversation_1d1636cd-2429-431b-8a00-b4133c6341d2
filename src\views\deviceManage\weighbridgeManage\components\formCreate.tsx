/**
 * @file 设备管理-地磅管理-表单创建文件
 * <AUTHOR>
 * @date 2025/7/23
 */
import { formMap } from "../types";
import LASelect from "@/components/LASelect.tsx";
import { getCrusherList } from "@/api/modules/device.ts";

export const weighbridgeMachineFormCreate = {
  type: "form-create",
  props: {
    option: {
      submitBtn: false,
      onSubmit(formData, api) {
        // 通知 table 搜索数据变化，刷新数据
        api.top.bus.$emit("searchFormChanged");
      }
    },
    rule: [
      {
        type: "input",
        field: formMap.get("地磅名称"),
        title: "地磅名称",
        validate: [
          { required: true, message: "请输地磅名称" },
          {
            pattern: /^.{1,20}$/,
            message: "字符限长20位"
          }
        ]
      },
      {
        type: "input",
        field: formMap.get("编码"),
        title: "地磅编码",
        validate: [{ required: true, message: "请输入地磅编码" }]
      },
      {
        component: LASelect,
        field: formMap.get("绑定破碎站"),
        title: "绑定破碎站",
        props: {
          replaceFields: { key: "id", label: "name", value: "id" },
          multiple: true,
          fetch: getCrusherList
        }
      },
      {
        type: "input",
        field: formMap.get("最大称量"),
        title: "最大称量",
        children: [{ type: "div", slot: "suffix", children: ["t"] }],
        validate: [
          {
            pattern: /^\d+(\.\d+)?$/, // 允许输入数字和小数
            message: "请输入大于0的数字"
          }
        ]
      },
      {
        type: "input",
        field: formMap.get("经度"),
        title: "经度"
      },
      {
        type: "input",
        field: formMap.get("纬度"),
        title: "纬度"
      },
      {
        type: "input",
        field: formMap.get("海拔"),
        title: "海拔",
        children: [{ type: "div", slot: "suffix", children: ["米"] }],
        validate: [
          {
            pattern: /^\d+(\.\d+)?$/, // 允许输入数字和小数
            message: "请输入大于0的数字"
          }
        ]
      },
      {
        type: "input",
        field: formMap.get("朝向"),
        title: "朝向"
      },
      {
        type: "input",
        field: formMap.get("osmID"),
        title: "osmID"
      }
    ]
  }
};
