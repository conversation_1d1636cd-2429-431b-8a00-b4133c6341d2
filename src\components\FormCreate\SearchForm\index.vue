<template><form-create :option="option" :rule="rule"></form-create></template>
<script lang="ts" setup>
import { ref } from "vue";
defineProps<{
  // rules:
}>();
const option = {
  form: { inline: true },
  resetBtn: false,
  submitBtn: false
};

const rule = ref([
  {
    type: "input",
    field: "search",
    props: {
      placeholder: "输入账号"
    }
  }
]);
</script>
<style lang="scss" scoped>
:deep(.el-form-item) {
  margin-right: 4px !important;
}
</style>
