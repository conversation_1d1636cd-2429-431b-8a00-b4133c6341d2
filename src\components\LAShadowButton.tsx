/**
 * @file 椭圆阴影按钮公用组件
 * <AUTHOR>
 * @date 2022/4/28
 */
import { computed, defineComponent, PropType, ref } from "vue";
// element-plush的按钮，按钮组 组件
import { ElButton, ElRadioGroup } from "element-plus";
import commonStyle from "@/assets/style/common.module.scss";

interface ButtonType {
  // 按钮文本
  label: string;
  // 按钮本身的值
  value: string;
}

/**
 * 椭圆阴影按钮组件
 * <AUTHOR>
 * @date 2022/5/5
 */
export const LAShadowButton = defineComponent({
  name: "LAShadowButton",
  props: {
    // 按钮文本
    label: String as PropType<ButtonType["label"]>,
    // 按钮本身的值
    value: String as PropType<ButtonType["value"]>,
    // 激活按钮的值
    active: String as PropType<ButtonType["value"]>
  },
  setup(props) {
    // 当前按钮是否是激活按钮
    const isActive = computed(() => props.active === undefined || props.active === props.value);
    return () => (
      <ElButton
        type={isActive.value ? "primary" : "text"}
        class={["LAbtn", isActive.value && [commonStyle.shadowButton, "active"]]}
      >
        {props.label}
      </ElButton>
    );
  }
});
/**
 * 椭圆阴影按钮组组件，保证同时只有一个激活按钮
 * <AUTHOR>
 * @date 2022/5/5
 */
export const LAShadowButtonGroup = defineComponent({
  name: "LAShadowButtonGroup",
  props: {
    // 按钮组列表
    list: { type: Array as PropType<ButtonType[]> },
    // 双向绑定 激活默认值
    modelValue: { type: String as PropType<ButtonType["value"]> }
  },
  // eslint-disable-next-line vue/no-setup-props-destructure
  setup(props: any, { emit }) {
    const active: any = ref(props.modelValue || props.list[0].value);
    emit("update:modelValue", active.value);
    // 激活按钮改变，更新父组件更新v-model
    const onChange = (prop: ButtonType): void => {
      active.value = prop.value;
      emit("update:modelValue", active.value);
    };
    return () => (
      <ElRadioGroup class={commonStyle.shadowButtonGroup}>
        {props.list.map(prop => (
          <LAShadowButton {...prop} active={active.value} onClick={() => onChange(prop)} />
        ))}
      </ElRadioGroup>
    );
  }
});
