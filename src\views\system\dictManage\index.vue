<template>
  <form-create v-model:api="fApi" :option="option" :rule="rule"></form-create>
</template>
<script lang="tsx" setup>
/**
 * @file 系统管理-字典管理
 * <AUTHOR>
 * @date 2024/11/14
 */
import { ref } from "vue";
import { columnMap, DictionaryTypeEnum } from "./types";
import { dictManageFormCreate } from "./components/formCreate";
import formCreate, { Api } from "@form-create/element-ui";
import { useRouter } from "vue-router";
import { addDict, deleteDict, getDictList, updateDict } from "@/api/modules";
const fApi = ref();
const option = {
  form: { inline: true },
  resetBtn: false,
  submitBtn: false
};
const router = useRouter();

const rule = ref([
  {
    type: "SearchFormOperation",
    field: "v:search",
    wrap: { style: "marginBottom: 0" },
    children: [
      {
        type: "input",
        field: "search",
        props: {
          size: "default",
          placeholder: "字典名称/编码"
        }
      },
      // 新增
      {
        type: "AddBtn",
        slot: "suffix",
        props: {
          btn: { content: "新增字典", auth: "add" },
          dialog: { title: "新增字典" },
          size: "default",
          submitRequest: addDict
        },
        children: [dictManageFormCreate]
      }
    ]
  },
  {
    type: "ProTable",
    props: {
      columns: [
        {
          prop: columnMap.get("字典名称"),
          label: "字典名称"
        },
        {
          prop: columnMap.get("字典编码"),
          label: "字典编码"
        },
        {
          prop: columnMap.get("系统字典"),
          label: "系统字典",
          render: (scope: any) => {
            return scope.row.isSystem === 1 ? "是" : "否";
          }
        },

        { prop: "operation", label: "操作", fixed: "right" }
      ],
      fetch: getDictList,
      operations: (row: any) => {
        type btnType = {
          content: string;
          action: string;
          onClick?: (row: { [key: string]: any }, api: Api) => void;
          auth?: string;
          props?: {};
          show?: boolean;
        };
        const btn: btnType[] = [
          {
            content: "字典列表",
            action: "list",
            auth: "list",

            onClick: row => {
              // 点击携带参数跳转到列表页
              router.push({ name: "dictList", query: { option: JSON.stringify(row) } });
            }
          },
          {
            content: "设为系统字典",
            action: "systemDictionary",
            auth: "update",
            show: Number(row[columnMap.get("系统字典")]) === DictionaryTypeEnum.NORMAL_DICTIONARY
          },
          { content: "修改", action: "edit", auth: "update" },
          {
            content: "删除",
            action: "delete",
            auth: "delete",
            props: { style: { color: "rgba(242, 85, 85, 1)" } },
            show: Number(row[columnMap.get("系统字典")]) === DictionaryTypeEnum.NORMAL_DICTIONARY
          }
        ];

        return btn.filter(item => item.show !== false);
      }
    },

    children: [
      {
        type: "ConfirmDialog",
        on: {
          // 监听弹窗组件抛出的的afterSubmit事件，用于刷新页面
          afterSubmit: () => {
            // 刷新，调用组件内部请求方法
            fApi.value.exec("v:search", "onSearch");
          }
        },
        props: {
          action: "systemDictionary",
          title: "是否设置为系统字典",
          message: "设置为系统字典后将不能删除",
          // 模拟请求param：参数
          submitRequest: (param: any) => updateDict({ ...param, ids: param.id })
        }
      },
      {
        type: "EditBtn",
        props: {
          action: "edit",
          dialog: { title: "修改参数" },
          submitRequest: addDict
        },
        children: [dictManageFormCreate]
      },
      {
        type: "ConfirmDialog",
        on: {
          // 监听弹窗组件抛出的的afterSubmit事件，用于刷新页面
          afterSubmit: () => {
            // 刷新，调用组件内部请求方法
            fApi.value.exec("v:search", "onSearch");
          }
        },
        props: {
          action: "delete",
          subtitle: row => {
            return row.dname;
          },
          title: "是否删除字典",
          message: "删除后不可恢复",
          // 模拟请求param：参数
          submitRequest: deleteDict
        }
      }
    ]
  }
]);
</script>
<style lang="scss" scoped>
:deep(.el-row) {
  height: var(--page-height);
}

.el-form-item {
  margin-right: 4px !important;
}
</style>
