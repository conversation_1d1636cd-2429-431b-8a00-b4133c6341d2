/**
 * @file 设备管理-地磅管理-类型声明文件
 * <AUTHOR>
 * @date 2025/7/22
 */

// table字段声明
export const columnMap: any = new Map([
  ["名称", "machineName"],
  ["编码", "machineCode"],
  ["绑定破碎站", "crushingStationList"],
  ["最大称量", "maxCapacity"],
  ["经度", "longitude"],
  ["纬度", "latitude"],
  ["海拔", "ele"],
  ["朝向", "orientations"],
  ["osmID", "osmId"]
]);
// 新增/修改form字段声明
export const formMap = new Map([
  ["地磅名称", "machineName"],
  ["编码", "machineCode"],
  ["绑定破碎站", "crushingStationList"],
  ["最大称量", "maxCapacity"],
  ["经度", "longitude"],
  ["纬度", "latitude"],
  ["海拔", "ele"],
  ["朝向", "orientations"],
  ["osmID", "osmId"]
]);

// 地磅状态枚举
export enum StatusEnum {
  /** 停用*/
  STOP = 0,
  /** 启用*/
  ENABLE = 1,
  /** 维修中*/
  MAINTAIN = 2
}
