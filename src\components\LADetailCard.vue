<template>
  <div class="detail-card">
    <div class="content">
      <!-- 标题 -->
      <div class="title">
        <div class="text">
          {{ showValue(title) }}
          <StatusTag
            v-if="tagIcon"
            :enum="tagIcon.enum"
            :prop="tagIcon.prop"
            :style="{ height: '20px' }"
            :value="tagIcon.value"
          />
          <slot :data="detailData" name="tag"></slot>
        </div>

        <div style="display: flex; gap: 4px">
          <slot :data="detailData" name="extra-btn"></slot>
        </div>
      </div>
      <!-- 内容 -->
      <div class="content-container">
        <div
          v-for="field of Object.keys(detailLabels)"
          :key="field"
          :class="['field', { 'block-item': detailLabels[field].block }]"
          :style="detailLabels?.[field]?.style"
        >
          <span class="label">{{ detailLabels[field].label }}</span>
          <span v-show="detailLabels[field].hiddenColon" style="font-weight: bold">: </span>
          <RenderValue :field="field" />
        </div>
      </div>
    </div>
    <!-- 右上角icon -->
    <!--    <img v-if="tagIcon" :src="tagIcon" alt="状态" class="tag" style="width: 80px" />-->
  </div>
</template>
<script lang="tsx" setup>
/**
 * @file 对应详情中的固定的详情组件
 * <AUTHOR>
 * @date 2025/1/24
 */
import { toRef, defineComponent } from "vue";
import { showValue } from "@/utils/helpers.ts";
import StatusTag from "@/components/StatusTag.vue";

const props = withDefaults(
  defineProps<{
    // 几列
    columns?: number;
    // 详情labels
    detailLabels: {
      [key: string]: {
        // 数据label
        label: string;
        // 单位
        suffix?: string;
        // 是否显示成一行
        block?: boolean;
        // 自定义样式
        style?: { [key: string]: any };
        // 是否隐藏冒号
        hiddenColon?: boolean;
        // 自定义格式化
        formatter?: (value: any, data: any) => any;
      };
    };
    // 详情数据
    detailData: { [key: string]: any };
    // 标题
    title: string;
    // 右上角状态icon是否显示
    tagIcon?: {
      enum: {
        color?: string;
        bg?: string;
        text: string;
        [key: string]: any;
      }[];
      prop: string;
      value: string | number;
    };
  }>(),
  {
    detailData: () => ({}),
    columns: 1
  }
);

// 详情数据
const data = toRef(props, "detailData");

const RenderValue = defineComponent({
  name: "RenderValue",
  props: {
    field: {
      type: String
    }
  },
  // eslint-disable-next-line vue/no-setup-props-destructure
  setup({ field }) {
    return () => {
      const valueContent = props.detailLabels[field!]?.formatter?.(data.value[field!], data.value) || data.value[field!];
      const style = props.detailLabels[field!].style;
      return (
        // <span class="value" style="white-space:nowrap;flex:1;overflow:hidden;text-overflow:ellipsis" title={valueContent || ""}>
        //   {showValue(valueContent)}
        //   {valueContent ? props.detailLabels[field].suffix : ""}
        // </span>
        <span class="value" style={style} title={valueContent || ""}>
          {showValue(valueContent)}
          {valueContent ? props.detailLabels[field!].suffix : ""}
        </span>
      );
    };
  }
});
</script>

<style lang="scss" scoped>
.detail-card {
  position: relative;
  display: flex;
  width: 100%;
  //min-height: 200px;
  border-radius: 8px;
  gap: 20px;
}

.content {
  font-size: 14px;
  display: flex;
  flex: 1;
  flex-direction: column;
  gap: 20px;

  .content-container {
    display: inline-grid;
    --reapt-num: v-bind("columns");
    row-gap: 20px;
    grid-template-columns: repeat(var(--reapt-num), 1fr);

    .field {
      display: flex;
      min-width: 0;

      .label {
        font-weight: bold;
        display: inline-block;
        min-width: 60px;
        text-align: justify;
        //text-align-last: justify;
        color: rgba(26, 26, 26, 1);
      }

      :deep(.value) {
        padding: 0 0 0 20px;
        color: rgba(101, 102, 102, 1);
      }
    }

    .block-item {
      grid-column-start: span var(--reapt-num);
    }
  }
}

.title {
  font-size: 16px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid var(--el-border-color-lighter);
  color: #1a1a1a;
  gap: 6px;
  padding-bottom: 20px;

  .text {
    font-size: 20px;
    line-height: 100%;
    display: flex;
    align-items: center;
    gap: 4px;
  }
}

.tag {
  position: absolute;
  top: 0;
  right: 0;
}
</style>
