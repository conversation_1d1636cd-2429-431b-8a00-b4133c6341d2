<template>
  <div class="dispatch-plan">
    <LACard :showHeader="false" shadow="never">
      <LATitle :bottom="0" title="调度计划" />
      <div v-if="!hasPermission" class="plan-container">
        <div class="plan-executing plan">
          <div class="plan-title">执行中</div>
          <span class="plan-content">{{ data?.progressCount }}</span>
        </div>
        <div class="plan-not-started plan">
          <div class="plan-title">未开始</div>
          <span class="plan-content">{{ data?.executionCount }}</span>
        </div>
      </div>
      <PlaceholderImage v-else type="noPermission" />
    </LACard>
  </div>
</template>

<script lang="ts" setup>
/**
 * @file 首页-调度计划
 * <AUTHOR>
 * @date 2025/1/21
 */

import LATitle from "@/components/LATitle.vue";
import LACard from "@/components/LACard/index.vue";
import PlaceholderImage from "@/components/PlaceholderImage.vue";
import { computed } from "vue";

import { useDispatchPlanStatistics } from "@/views/home/<USER>";
const { data, error } = useDispatchPlanStatistics();
// 401则为没有权限
const hasPermission = computed(() => {
  return error.value?.status === 401;
});
</script>

<style scoped>
.plan-container {
  flex: 1;
  display: flex;
  gap: 10px;
  padding-top: 20px;

  .plan-title {
    font-size: 14px;
    color: #656666;
    white-space: nowrap;
  }

  .plan-content {
    font-size: 28px;
    color: #1a1a1a;
    font-weight: bold;
  }

  .plan {
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 20px;
    flex: 1;
  }

  .plan-executing {
    background-color: rgba(0, 194, 144, 0.08);
  }

  .plan-not-started {
    background-color: rgba(245, 249, 252, 1);
  }
}
</style>
