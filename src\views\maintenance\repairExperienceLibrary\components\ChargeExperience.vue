<template>
  <form-create v-model:api="fApi" :option="option" :rule="rule"></form-create>
</template>
<script lang="tsx" setup>
/**
 * @file 维修保养-维修经验库-充电桩页
 * <AUTHOR>
 * @date 2025/2/13
 */
import { ref } from "vue";
import { deleteFlight } from "@/api/modules";
import { TramcarColumnMap } from "../types";
import formCreate from "@form-create/element-ui";
import { getRepairExperienceList, getRepairExperienceModel, saveRepairExperience } from "@/api/modules/repair.ts";
import LASelect from "@/components/LASelect.tsx";
import { chargeFormCreate } from "@/views/maintenance/repairExperienceLibrary/components/formCreate.ts";
import { detailFormCreate } from "@/views/maintenance/repairExperienceLibrary/components/detailFormCreate.ts";
const fApi = ref();
const option = {
  form: { inline: true },
  resetBtn: false,
  submitBtn: false
};

const rule = ref([
  {
    type: "SearchFormOperation",
    field: "v:search",
    wrap: { style: "marginBottom: 0" },
    children: [
      {
        component: LASelect,
        field: "deviceType",
        style: { width: "200px" },
        props: {
          fetch: getRepairExperienceModel,
          params: {
            deviceType: "deviceChargingPile"
          },
          replaceFields: { key: "code", label: "name", value: "code" },
          placeholder: "充电桩型号"
        }
      },
      {
        type: "input",
        field: "faultCodeOrDesc",
        props: {
          size: "default",
          placeholder: "故障码/故障描述"
        }
      },
      // 新增
      {
        type: "AddBtn",
        slot: "suffix",
        props: {
          btn: { content: "新增" },
          dialog: { title: "新增维修经验" },
          size: "default",
          submitRequest: params => {
            return saveRepairExperience({ ...params, deviceType: "deviceChargingPile" });
          }
        },
        children: [chargeFormCreate]
      }
    ]
  },
  {
    type: "ProTable",
    props: {
      columns: [
        {
          prop: TramcarColumnMap.get("设备型号"),
          label: "充电桩型号"
        },

        {
          prop: TramcarColumnMap.get("故障码"),
          label: "故障码"
        },
        {
          prop: TramcarColumnMap.get("故障描述"),
          label: "故障描述"
        },
        {
          prop: TramcarColumnMap.get("维修经验"),
          label: "维修经验",
          render: () => {
            return "...";
          }
        },

        { prop: "operation", label: "操作", style: { color: "red" }, fixed: "right" }
      ],
      fetch: params => getRepairExperienceList({ ...params, deviceType: "deviceChargingPile" }),

      operations: [
        { content: "详情", action: "detail" },
        { content: "修改", action: "edit" },
        { content: "删除", action: "delete", props: { style: { color: "rgba(242, 85, 85, 1)" } } }
      ]
    },
    children: [
      // 详情组件
      {
        type: "DetailBtn",
        props: {
          action: "detail",
          dialog: {
            size: "900px",
            title: "维修经验详情"
          }
        },
        children: [detailFormCreate]
      },
      {
        type: "EditBtn",
        props: {
          action: "edit",
          dialog: { title: "修改维修经验" },
          submitRequest: params => saveRepairExperience({ ...params, deviceType: "deviceChargingPile" })
        },
        children: [chargeFormCreate]
      },
      {
        type: "ConfirmDialog",
        on: {
          // 监听弹窗组件抛出的的afterSubmit事件，用于刷新页面
          afterSubmit: () => {
            // 刷新，调用组件内部请求方法
            fApi.value.exec("v:search", "onSearch");
          }
        },
        props: {
          action: "delete",
          subtitle: row => {
            return row.name;
          },
          title: "是否删除班次",
          message: "删除后不可恢复",
          // 请求param：参数
          submitRequest: deleteFlight
        }
      }
    ]
  }
]);
</script>
<style lang="scss" scoped>
:deep(.el-row) {
  height: calc(100vh - 178px) !important;
}

.el-form-item {
  margin-right: 4px !important;
}
</style>
