<template>
  <!-- CZML数据源 -->
  <vc-datasource-czml v-if="czmlData && show" :czml="czmlData" :show="true" @ready="onCzmlReady" />

  <!-- 车辆浮窗 -->
  <VehicleTooltip
    v-for="tooltip in visibleTooltips"
    :key="tooltip.vehicleId"
    :visible="tooltip.visible"
    :vehicle-status="tooltip.vehicleStatus"
    :vehicle-type="tooltip.vehicleType"
    :position="tooltip.position"
  />
</template>

<script setup lang="ts">
import { ref, watch, onUnmounted } from "vue";
import { VcDatasourceCzml } from "vue-cesium";
import type { TrackData } from "@/views/dispatcher/trackPlayback/types";
import VehicleTooltip from "./VehicleTooltip.vue";
// 导入图标
import excavatorIcon from "../assets/excavator.png";
import truckIcon from "../assets/truck.png";

// 车辆状态数据接口
interface VehicleStatus {
  name: string;
  heavy?: boolean;
  speed: number;
  errorCode?: number;
}

interface Props {
  trackData: TrackData[];
  currentTime: number;
  isPlaying: boolean;
  playSpeed: number;
  show?: boolean;
  // 车辆状态数据，用于显示浮窗信息
  vehicleStatusData?: Record<string, VehicleStatus>;
}

const props = withDefaults(defineProps<Props>(), {
  show: true
});

const emit = defineEmits<{
  ready: [viewer: any];
}>();

const czmlData = ref<any>(null);
const cesiumViewer = ref<any>(null);

// 浮窗状态管理
interface TooltipInfo {
  vehicleId: string;
  visible: boolean;
  vehicleStatus?: VehicleStatus;
  vehicleType: "truck" | "excavator";
  position: { x: number; y: number };
}

const visibleTooltips = ref<TooltipInfo[]>([]);

// CZML数据源准备就绪
const onCzmlReady = ({ viewer }: any) => {
  console.log("轨迹回放CZML数据源加载完成");
  cesiumViewer.value = viewer;

  // 同步时钟设置
  if (viewer && props.trackData.length > 0) {
    viewer.clock.shouldAnimate = props.isPlaying;
    viewer.clock.multiplier = props.playSpeed;

    if (props.currentTime > 0) {
      viewer.clock.currentTime = Cesium.JulianDate.fromDate(new Date(props.currentTime));
    }

    // 自动飞行到轨迹数据区域
    flyToTrackDataArea(viewer);
  }

  // 启动浮窗更新
  startTooltipUpdates();

  emit("ready", viewer);
};

// 飞行到轨迹数据区域
function flyToTrackDataArea(viewer: any) {
  if (!props.trackData || props.trackData.length === 0) return;

  // 计算所有轨迹点的边界
  let minLng = Number.MAX_VALUE;
  let maxLng = Number.MIN_VALUE;
  let minLat = Number.MAX_VALUE;
  let maxLat = Number.MIN_VALUE;

  props.trackData.forEach(vehicle => {
    if (vehicle.positions && vehicle.positions.length > 0) {
      vehicle.positions.forEach(pos => {
        minLng = Math.min(minLng, pos.longitude);
        maxLng = Math.max(maxLng, pos.longitude);
        minLat = Math.min(minLat, pos.latitude);
        maxLat = Math.max(maxLat, pos.latitude);
      });
    }
  });

  // 如果找到了有效的边界
  if (minLng !== Number.MAX_VALUE && maxLng !== Number.MIN_VALUE) {
    // 添加一些边距
    const lngPadding = (maxLng - minLng) * 0.1;
    const latPadding = (maxLat - minLat) * 0.1;

    // 创建边界矩形
    const rectangle = Cesium.Rectangle.fromDegrees(
      minLng - lngPadding,
      minLat - latPadding,
      maxLng + lngPadding,
      maxLat + latPadding
    );

    console.log("飞行到轨迹区域:", {
      west: minLng - lngPadding,
      south: minLat - latPadding,
      east: maxLng + lngPadding,
      north: maxLat + latPadding
    });

    // 飞行到该区域
    viewer.camera.flyTo({
      destination: rectangle,
      duration: 2.0 // 飞行时间2秒
    });
  }
}

// 启动浮窗位置更新
function startTooltipUpdates() {
  if (!cesiumViewer.value) return;

  // 监听场景渲染事件，更新浮窗位置
  cesiumViewer.value.scene.postRender.addEventListener(updateTooltipPositions);
}

// 更新浮窗位置
function updateTooltipPositions() {
  if (!cesiumViewer.value || !props.vehicleStatusData) return;

  const newTooltips: TooltipInfo[] = [];

  props.trackData.forEach(vehicle => {
    const vehicleStatus = props.vehicleStatusData?.[vehicle.id];
    if (!vehicleStatus) return;

    // 获取车辆实体
    const entity = cesiumViewer.value.entities.getById(vehicle.id);
    if (!entity || !entity.position) return;

    try {
      // 获取当前时间的位置
      const currentTime = cesiumViewer.value.clock.currentTime;
      const position = entity.position.getValue(currentTime);
      if (!position) return;

      // 转换为屏幕坐标
      const screenPosition = Cesium.SceneTransforms.worldToWindowCoordinates(cesiumViewer.value.scene, position);

      if (screenPosition) {
        newTooltips.push({
          vehicleId: vehicle.id,
          visible: true,
          vehicleStatus,
          vehicleType: vehicle.type as "truck" | "excavator",
          position: { x: screenPosition.x, y: screenPosition.y }
        });
      }
    } catch (error) {
      // 忽略位置计算错误
    }
  });

  visibleTooltips.value = newTooltips;
}

// 组件卸载时清理
onUnmounted(() => {
  if (cesiumViewer.value) {
    cesiumViewer.value.scene.postRender.removeEventListener(updateTooltipPositions);
  }
});

// 监听轨迹数据变化，转换为CZML格式
watch(
  () => props.trackData,
  newTrackData => {
    if (newTrackData && newTrackData.length > 0) {
      czmlData.value = convertToCzml(newTrackData);
    } else {
      czmlData.value = null;
    }
  },
  { immediate: true }
);

// 将轨迹数据转换为CZML格式
function convertToCzml(trackData: TrackData[]): any {
  if (!trackData || trackData.length === 0) return null;

  // 计算整体时间范围
  const allStartTimes = trackData.map(item => item.startTime);
  const allEndTimes = trackData.map(item => item.endTime);
  const globalStartTime = Math.min(...allStartTimes);
  const globalEndTime = Math.max(...allEndTimes);

  const czml = [
    {
      id: "document",
      name: "Track Playback",
      version: "1.0",
      clock: {
        interval: `${new Date(globalStartTime).toISOString()}/${new Date(globalEndTime).toISOString()}`,
        currentTime: new Date(globalStartTime).toISOString(),
        multiplier: props.playSpeed || 1,
        range: "LOOP_STOP",
        step: "SYSTEM_CLOCK_MULTIPLIER"
      }
    }
  ];

  // 为每个车辆创建CZML实体
  trackData.forEach(vehicle => {
    if (vehicle.positions && vehicle.positions.length > 0) {
      // 创建位置数据数组
      const positions: number[] = [];
      vehicle.positions.forEach(pos => {
        positions.push(
          (pos.timestamp - vehicle.startTime) / 1000, // 相对时间（秒）
          pos.longitude,
          pos.latitude,
          pos.altitude || 0
        );
      });

      // 车辆实体
      const vehicleEntity: any = {
        id: vehicle.id,
        name: vehicle.name,
        availability: `${new Date(vehicle.startTime).toISOString()}/${new Date(vehicle.endTime).toISOString()}`,
        position: {
          epoch: new Date(vehicle.startTime).toISOString(),
          cartographicDegrees: positions
        },
        orientation: {
          velocityReference: "#position"
        }
      };

      // 根据车辆类型设置不同的显示样式
      if (vehicle.type === "excavator") {
        // 挖机使用本地图标
        vehicleEntity.billboard = {
          image: excavatorIcon,
          scale: 1.0,
          width: 32,
          height: 32,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
          heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
        };
      } else if (vehicle.type === "truck") {
        // 矿车使用本地图标
        vehicleEntity.billboard = {
          image: truckIcon,
          scale: 1.0,
          width: 32,
          height: 32,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
          heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
        };
      }

      // 浮窗通过HTML组件实现，这里不需要添加CZML标签

      // 添加轨迹路径
      vehicleEntity.path = {
        material: {
          solidColor: {
            color: {
              rgba: vehicle.type === "excavator" ? [255, 165, 0, 200] : [0, 191, 255, 200] // 挖机橙色，矿车蓝色
            }
          }
        },
        width: 4,
        leadTime: 0,
        trailTime: 1800, // 显示30分钟的轨迹
        resolution: 2,
        show: true
      };

      czml.push(vehicleEntity);
    }
  });

  return czml;
}
</script>
