/**
 * @file 设备管理-破碎站管理-表单创建文件
 * <AUTHOR>
 * @date 2024/11/25
 */
import { formMap } from "../types";
export const crushingStationFormCreate = {
  type: "form-create",
  props: {
    option: {
      row: { gutter: 24 },
      global: { "*": { col: { span: 12 } } },
      form: { labelWidth: "180px" },
      submitBtn: false,
      onSubmit(formData, api) {
        // 通知 table 搜索数据变化，刷新数据
        api.top.bus.$emit("searchFormChanged");
      }
    },
    rule: [
      {
        type: "input",
        field: formMap.get("破碎站名称"),
        title: "破碎站名称",
        validate: [
          { required: true, message: "请输入破碎站名称" },
          {
            pattern: /^.{1,20}$/,
            message: "字符限长20位"
          }
        ]
      },
      {
        type: "input",
        field: formMap.get("破碎站编码"),
        title: "破碎站编码",
        validate: [{ required: true, message: "请输入破碎站编码" }]
      },
      {
        type: "input",
        field: formMap.get("允许卸矿最低安全深度"),
        title: "允许卸矿最低安全深度",
        children: [{ type: "div", slot: "suffix", children: ["米"] }],
        validate: [
          {
            pattern: /^(?:[1-9]\d*|0*[1-9]\d*(?:\.\d+)?|0*\.\d*[1-9]\d*)$/,
            message: "请输入大于0的数字"
          }
        ]
      },
      {
        type: "input",
        field: formMap.get("经度"),
        title: "经度"
      },
      {
        type: "input",
        field: formMap.get("纬度"),
        title: "纬度"
      },
      {
        type: "input",
        field: formMap.get("海拔"),
        title: "海拔",
        children: [{ type: "div", slot: "suffix", children: ["米"] }]
      },
      {
        type: "input",
        field: formMap.get("朝向"),
        title: "朝向"
      },
      {
        type: "input",
        field: formMap.get("最大喂矿率"),
        title: "最大喂矿率",
        children: [{ type: "div", slot: "suffix", children: ["t/h"] }],
        validate: [
          {
            pattern: /^(?:[1-9]\d*|0*[1-9]\d*(?:\.\d+)?|0*\.\d*[1-9]\d*)$/,
            message: "请输入大于0的数字"
          }
        ]
      }
    ]
  }
};
