<template>
  <div>
    <LADetailCard :detail-data="detailData" :detail-labels="detailLabels" :title="orderDetail?.value.faultCode"></LADetailCard>
    <div class="user-record">
      <LATitle title="用户维修经验记录" />
      <div v-for="data in recordList" class="record-info">
        <div class="username">{{ data.personName }}</div>

        <div class="record-content">
          <div class="time">{{ dayjs(data.createDate).format("YYYY-MM-DD HH:mm:ss") }}</div>
          <div class="content">
            <div style="white-space: pre-line; line-height: 1.6">{{ data.record }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="tsx" setup>
/**
 * @file 维修保养-维修工单-详情-故障码详情
 * <AUTHOR>
 * @date 2025/2/11
 */
import LADetailCard from "@/components/LADetailCard.vue";
import { PropType, ref } from "vue";
import LATitle from "@/components/LATitle.vue";
import dayjs from "dayjs";
import { TramcarColumnMap } from "@/views/maintenance/repairExperienceLibrary/types.ts";
import { getRepairExperienceList, getRepairExperienceRecordList } from "@/api/modules/repair.ts";

interface RepairExperience {
  id: string | number;
  [key: string]: any;
}

const props = defineProps({
  orderDetail: {
    type: Object as PropType<{ faultCode?: string; [key: string]: any }>,
    default: () => ({})
  }
});
// 储存详情方法
const detailData = ref<RepairExperience>();
const recordList = ref();

// 刷新/获取数据的方法
const refresh = async () => {
  await getRepairExperienceList({
    current: 1,
    size: 9999,
    faultCodeOrDesc: props.orderDetail.value.faultCode,
    deviceType: "deviceMineTrain",
    modelNumber: props.orderDetail.value.modelNumber
  }).then(res => {
    detailData.value = (res.data as { records: any[] }).records[0]; // 添加断言
  });
  if (detailData.value) {
    await getRepairExperienceRecordList({ experienceId: detailData.value?.id }).then(res => {
      // console.log(res);
      recordList.value = res.data;
    });
  } else {
    recordList.value = [];
  }
};

// 详情数据
const detailLabels = {
  [TramcarColumnMap.get("设备型号")]: {
    label: "矿车型号"
  },
  [TramcarColumnMap.get("故障描述")]: {
    label: "故障描述"
  },
  [TramcarColumnMap.get("维修经验")]: {
    label: "维修经验",
    formatter: (value: string) => {
      if (value == null) return "-";
      // value返回的是html
      return <div v-html={value}></div>;
    }
  }
};
refresh();
</script>

<style scoped>
.user-record {
  font-size: 14px;
  padding-top: 20px;
  margin-top: 20px;
  border-top: 1px solid var(--el-border-color-lighter);
}

div {
  line-height: 100%;
}

.record-info {
  display: flex;
  align-items: flex-start;
  gap: 50px;
  margin-top: 20px;

  .username {
    height: 100%;
    font-weight: bold;
  }

  .time {
    margin-bottom: 20px;
  }

  .content {
    display: flex;
    flex-direction: column;
    gap: 10px;
    color: #666666;
  }

  .record-btn {
    display: flex;
    gap: 5px;
  }
}
</style>
