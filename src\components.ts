import SearchFormOperation from "@/components/FormCreate/SearchForm/Operation.vue";
import DialogBtn from "@/components/FormCreate/DialogBtn/index.vue";
import DetailBtn from "@/components/FormCreate/DialogBtn/DetailBtn.vue";
import AddBtn from "@/components/FormCreate/DialogBtn/AddBtn.vue";
import EditBtn from "@/components/FormCreate/DialogBtn/EditBtn.vue";
import ConfirmDialog from "@/components/FormCreate/DialogBtn/ConfirmDialog.vue";
import ProTable from "@/components/ProTable/index.vue";
import StatusTag from "@/components/StatusTag.vue";
import Operation from "@/components/ProTable/components/operation/index.vue";
import LATree from "@/components/LATree/index.vue";
import LACard from "@/components/LACard/index.vue";
import LAImport from "@/components/LAImport.vue";
import LAExport from "@/components/LAExport/index.vue";
import { LAShadowButtonGroup } from "@/components/LAShadowButton";
import LADateTimeRangePicker from "@/components/LADateTimeRangePicker";
/**
 * 全局注册组件
 * @param app
 */

export default function (app) {
  const components = [
    ["SearchFormOperation", SearchFormOperation],
    ["DialogBtn", DialogBtn],
    ["DetailBtn", DetailBtn],
    ["AddBtn", AddBtn],
    ["ConfirmDialog", ConfirmDialog],
    ["EditBtn", EditBtn],
    ["ProTable", ProTable],
    ["StatusTag", StatusTag],
    ["Operation", Operation],
    ["LATree", LATree],
    ["LACard", LACard],
    ["LAImport", LAImport],
    ["LAExport", LAExport],
    ["LAShadowButtonGroup", LAShadowButtonGroup],
    ["LADateTimeRangePicker", LADateTimeRangePicker]
  ];
  for (const [name, component] of components) {
    app.component(name, component);
  }
}
