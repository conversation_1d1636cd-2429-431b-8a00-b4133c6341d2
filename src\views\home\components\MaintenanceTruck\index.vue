<template>
  <div class="maintenance-truck">
    <LACard :showHeader="false" shadow="never">
      <LATitle :bottom="dynamicsBottom" title="待保养矿车" />
      <div v-if="data?.length > 0" class="truck-container">
        <div v-for="item in data" class="truck" @click="toPage(item)">
          <span>{{ item.code }}</span>
          <img alt="" src="../../imgs/maintainMiningTruckIcon.png" />
        </div>
      </div>
      <PlaceholderImage v-else :type="showImgType" />
    </LACard>
  </div>
</template>

<script lang="ts" setup>
/**
 * @file 首页-待保养矿车
 * <AUTHOR>
 * @date 2025/1/22
 */
import { computed } from "vue";
import LATitle from "@/components/LATitle.vue";
import LACard from "@/components/LACard/index.vue";
import PlaceholderImage from "@/components/PlaceholderImage.vue";
import { useRouter } from "vue-router";
import { useAllMaintenanceTrain } from "@/views/home/<USER>";
// 接口获取实时故障数据
const { data, error } = useAllMaintenanceTrain();

const router = useRouter();

// 动态bottom边距
const dynamicsBottom = computed(() => {
  if (data?.value?.length > 0) {
    return 10;
  } else {
    return 0;
  }
});

// 控制占位图类型noPermission/noData
const showImgType = computed(() => {
  // 没有权限401则为没有权限
  if (error.value?.status === 401) {
    return "noPermission";
  }
  if (data?.value?.length === 0) {
    return "noData";
  }
  return "noData";
});
// 跳转保养工单
const toPage = (item: any) => {
  router.push("/maintenance/maintenanceOrder/index");
};
</script>

<style scoped>
.truck-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0 10px;

  overflow-y: auto;

  .truck {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 0;
    border-bottom: 1px solid rgba(235, 237, 241, 1);
    cursor: pointer;
  }
}
</style>
