<template>
  <div>
    <LADetailCard :detail-data="detailData" :detail-labels="detailLabels" :title="detailData?.faultCode"></LADetailCard>
    <div class="user-record">
      <LATitle title="用户维修经验记录" />
      <form-create v-model:api="fApi" :option="option" :rule="rule"></form-create>
      <div v-for="data in userExperienceRecordList">
        <UserExperienceRecordInfo :key="data" :record-list="data" @refresh="refresh" />
      </div>
    </div>
  </div>
</template>

<script lang="tsx" setup>
/**
 * @file 维修保养-维修经验库-无人矿车-详情组件
 * <AUTHOR>
 * @date 2025/2/13
 */
import LADetailCard from "@/components/LADetailCard.vue";
import formCreate from "@form-create/element-ui";
import { nextTick, ref } from "vue";
import { TramcarColumnMap } from "@/views/maintenance/repairExperienceLibrary/types.ts";
import { getRepairExperienceRecordList, saveRepairExperienceRecord } from "@/api/modules/repair.ts";
import LATitle from "@/components/LATitle.vue";
import { Plus } from "@element-plus/icons-vue";
import { userExperienceFormCreate } from "@/views/maintenance/repairExperienceLibrary/components/formCreate.ts";
import UserExperienceRecordInfo from "@/views/maintenance/repairExperienceLibrary/components/UserExperienceRecordInfo.vue";

const props = defineProps({
  formCreateInject: {
    type: Object,
    default: () => {}
  }
});

// 用户经验记录数据
const userExperienceRecordList = ref();
const fApi = ref();
const option = {
  form: { inline: true },
  resetBtn: false,
  submitBtn: false
};
const rule = ref([
  {
    type: "AddBtn",
    props: {
      btn: {
        icon: Plus,
        style: {
          width: "80px",
          height: "32px"
        },
        content: "新增",
        auth: "add"
      },
      // 是否是自定义表单弹窗 未嵌入表格只是显示弹窗form
      // isCustomDialog: true,
      dialog: { title: "新增用户经验记录" },

      submitRequest: (params, api) => {
        return saveRepairExperienceRecord({ experienceId: detailData.value.id, ...params }).then(res => {
          refresh();
          return res;
        });
      }
    },
    children: [userExperienceFormCreate]
  }
]);

const detailData = ref();

// 刷新/获取数据的方法
const refresh = async () => {
  await getRepairExperienceRecordList({ experienceId: detailData.value.id }).then(res => {
    userExperienceRecordList.value = res.data;
  });
};

nextTick(() => {
  detailData.value = props.formCreateInject.api.formData();
  refresh();
});

const detailLabels = {
  [TramcarColumnMap.get("设备型号")]: {
    label: "矿车型号"
  },
  [TramcarColumnMap.get("故障描述")]: {
    label: "故障描述"
  },
  [TramcarColumnMap.get("维修经验")]: {
    label: "维修经验",
    formatter: (value: string, data: object) => {
      // value返回的是html
      return <div v-html={value}></div>;
    }
  }
};
</script>

<style scoped>
.user-record {
  font-size: 14px;
  padding-top: 20px;
  margin-top: 20px;
  border-top: 1px solid var(--el-border-color-lighter);
}
</style>
