<template>
  <div v-if="maintenanceRecordList.length > 0" class="repair-record">
    <LATitle :bottom="0" title="保养记录" />
    <div v-for="data in maintenanceRecordList" :key="data.id" class="repair-record-content">
      <div class="record-content">
        <div class="user-name">{{ data.maintenanceResponsiblePersonName }}</div>
        <div class="describe">{{ maintenanceOperateResults[data.maintenanceResults] }}</div>
        <div class="describe">{{ dayjs(data.createDate).format("YYYY-MM-DD HH:mm:ss") }}</div>
      </div>
      <div v-if="['transfer'].includes(data.maintenanceResults)" class="record-content">
        <div class="describe">
          <span class="title">{{ repairContent[data.maintenanceResults] }}:</span>
          {{ data.newResponsiblePersonName }}
        </div>
      </div>
      <div v-if="['finish'].includes(data.maintenanceResults)" class="record-content">
        <div class="describe">
          <span class="title">{{ repairContent[data.maintenanceResults] }}:</span>
          {{ data.maintenanceContent }}
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
/**
 * @file 维修保养-保养工单-详情-保养记录组件
 * <AUTHOR>
 * @date 2025/2/11
 */
import LATitle from "@/components/LATitle.vue";
import { PropType } from "vue";
import dayjs from "dayjs";
interface RepairRecord {
  id: number;
  maintenanceResponsiblePersonName: string;
  maintenanceResults: string;
  createDate: string;
  maintenanceContent: string;
  newResponsiblePersonName: string;
}
const props = defineProps({
  // 接收数据
  maintenanceRecordList: {
    type: Array as PropType<RepairRecord[]>,
    default: () => []
  }
});
// 维修结果/维修备注的字段
const repairContent = {
  transfer: "保养的负责人",
  finish: "保养备注"
};
// 工单操作描述（repairResults的三种操作描述："transfer"：'转让了工单'，"unRepaired"\"repaired":'填写了维修结果'，"closed":'关闭了工单'）
const maintenanceOperateResults = {
  transfer: "转让了工单",

  finish: "填写了维修结果"
};
</script>

<style scoped>
.title,
.user-name {
  font-weight: bold;
  color: #1a1a1a;
}
div {
  line-height: 100%;
}
.repair-record {
  font-size: 14px;
  padding-top: 20px;

  .repair-record-content {
    border-bottom: 1px solid var(--el-border-color-lighter);
    padding-bottom: 20px;
    margin-top: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
    &:last-child {
      border-bottom: none;
    }
  }
  .record-content {
    display: flex;
    gap: 10px;
  }
  .describe {
    color: rgba(101, 102, 102, 1);
    display: flex;
    gap: 10px;
  }
}
</style>
