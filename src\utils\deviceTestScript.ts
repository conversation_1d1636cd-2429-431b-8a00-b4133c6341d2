/**
 * 设备数据测试脚本
 * 用于测试WebSocket消息处理和设备数据更新功能
 */

import type { WSDeviceInitData } from '@/stores/deviceTypes'

// 模拟设备数据生成器
class DeviceDataGenerator {
  private mineTrainCodes = ['LAKK-202410-1', 'LAKK2025011', 'LAKK-202410-3', 'LAKK-202501-1']
  private bulldozerCodes = ['waji01', 'WA-JI2', 'WAJI-1', 'waji02', 'waji03']
  
  // 生成随机数值
  private randomInt(min: number, max: number): number {
    return Math.floor(Math.random() * (max - min + 1)) + min
  }
  
  private randomFloat(min: number, max: number, decimals: number = 2): number {
    return parseFloat((Math.random() * (max - min) + min).toFixed(decimals))
  }
  
  // 生成随机位置
  private generateRandomLocation(): string {
    const lon = this.randomFloat(104.0, 104.4, 4)
    const lat = this.randomFloat(30.2, 30.6, 4)
    const alt = this.randomInt(380, 420)
    const direction = this.randomFloat(0, 360, 2)
    return `${lon},${lat},${alt},${direction}`
  }
  
  // 生成设备初始化数据
  generateDeviceInitData(): WSDeviceInitData {
    const bulldozersList = this.bulldozerCodes.map(code => ({
      code,
      name: `挖机-${code}`,
      id: `id_${code}_${Date.now()}`,
      posture: this.generateRandomLocation()
    }))
    
    const mineTrainList = this.mineTrainCodes.map(code => ({
      code,
      name: `矿车-${code}`,
      id: `id_${code}_${Date.now()}`,
      currentLocation: this.generateRandomLocation(),
      electricQuantity: this.randomInt(20, 100),
      driveMode: this.randomInt(0, 2),
      loadMode: this.randomInt(0, 1),
      speed: this.randomInt(0, 30),
      deceleration: this.randomInt(0, 5),
      gasPedal: this.randomInt(0, 100),
      gearLevel: this.randomInt(1, 5).toString(),
      handbrake: this.randomInt(0, 1),
      highBeam: this.randomInt(0, 1),
      lowBeam: this.randomInt(0, 1),
      fogLamp: this.randomInt(0, 1),
      turnSignalLamp: this.randomInt(0, 1),
      redirect: this.randomInt(0, 1),
      liftNumber: this.randomInt(0, 3)
    }))
    
    return {
      bulldozersList,
      mineTrainList
    }
  }
  
  // 生成单个矿车更新数据
  generateTrainMessage() {
    const code = this.mineTrainCodes[this.randomInt(0, this.mineTrainCodes.length - 1)]
    return {
      code,
      name: `矿车-${code}`,
      currentLocation: this.generateRandomLocation(),
      electricQuantity: this.randomInt(15, 100),
      speed: this.randomInt(0, 35),
      driveMode: this.randomInt(0, 2),
      loadMode: this.randomInt(0, 1),
      taskCurrentStep: ['装载中', '运输中', '卸载中', '返回中'][this.randomInt(0, 3)],
      errorFlag: this.randomInt(0, 10) < 2 ? 1 : 0, // 20% 概率故障
      errorLevel: this.randomInt(0, 10) < 2 ? '电池电量低' : undefined
    }
  }
  
  // 生成单个挖机更新数据
  generateBulldozerMessage() {
    const code = this.bulldozerCodes[this.randomInt(0, this.bulldozerCodes.length - 1)]
    return {
      code,
      name: `挖机-${code}`,
      posture: this.generateRandomLocation(),
      status: this.randomInt(0, 4), // 0-离线, 1-泊车, 2-等待装载, 3-装载中, 4-空闲
      workMode: ['自动', '手动'][this.randomInt(0, 1)]
    }
  }
}

// 测试脚本类
export class DeviceTestScript {
  private generator = new DeviceDataGenerator()
  private intervalIds: number[] = []
  private isRunning = false
  private messageHandler: ((data: any) => void) | null = null
  
  // 设置消息处理器
  setMessageHandler(handler: (data: any) => void) {
    this.messageHandler = handler
  }
  
  // 发送测试消息
  private sendMessage(messageType: string, data: any) {
    if (this.messageHandler) {
      const message = {
        messageType,
        socketType: 'unmannedPlatform',
        data
      }
      console.log(`[DeviceTest] Sending ${messageType}:`, message)
      this.messageHandler(message)
    }
  }
  
  // 开始测试
  start() {
    if (this.isRunning) {
      console.warn('[DeviceTest] Test is already running')
      return
    }
    
    this.isRunning = true
    console.log('[DeviceTest] Starting device data test...')
    
    // 立即发送一次设备初始化数据
    this.sendDeviceInit()
    
    // 每30秒发送一次设备初始化数据
    const initInterval = setInterval(() => {
      this.sendDeviceInit()
    }, 30000)
    this.intervalIds.push(initInterval)
    
    // 每3秒发送一次矿车更新
    const trainInterval = setInterval(() => {
      this.sendTrainMessage()
    }, 3000)
    this.intervalIds.push(trainInterval)
    
    // 每5秒发送一次挖机更新
    const bulldozerInterval = setInterval(() => {
      this.sendBulldozerMessage()
    }, 5000)
    this.intervalIds.push(bulldozerInterval)
    
    console.log('[DeviceTest] Test started successfully')
  }
  
  // 停止测试
  stop() {
    if (!this.isRunning) {
      console.warn('[DeviceTest] Test is not running')
      return
    }
    
    this.intervalIds.forEach(id => clearInterval(id))
    this.intervalIds = []
    this.isRunning = false
    
    console.log('[DeviceTest] Test stopped')
  }
  
  // 发送设备初始化消息
  sendDeviceInit() {
    const data = this.generator.generateDeviceInitData()
    this.sendMessage('deviceInit', data)
  }
  
  // 发送矿车更新消息
  sendTrainMessage() {
    const data = this.generator.generateTrainMessage()
    this.sendMessage('trainMessage', data)
  }
  
  // 发送挖机更新消息
  sendBulldozerMessage() {
    const data = this.generator.generateBulldozerMessage()
    this.sendMessage('bulldozersMessage', data)
  }
  
  // 获取运行状态
  getStatus() {
    return {
      isRunning: this.isRunning,
      activeIntervals: this.intervalIds.length
    }
  }
}

// 创建全局测试实例
export const deviceTestScript = new DeviceTestScript()

// 在浏览器控制台中使用的便捷方法
if (typeof window !== 'undefined') {
  (window as any).deviceTest = {
    start: () => deviceTestScript.start(),
    stop: () => deviceTestScript.stop(),
    status: () => deviceTestScript.getStatus(),
    sendInit: () => deviceTestScript.sendDeviceInit(),
    sendTrain: () => deviceTestScript.sendTrainMessage(),
    sendBulldozer: () => deviceTestScript.sendBulldozerMessage()
  }
  
  console.log(`
🚀 设备测试脚本已加载！

在浏览器控制台中使用以下命令：
- deviceTest.start()     // 开始自动测试
- deviceTest.stop()      // 停止测试
- deviceTest.status()    // 查看状态
- deviceTest.sendInit()  // 手动发送初始化数据
- deviceTest.sendTrain() // 手动发送矿车更新
- deviceTest.sendBulldozer() // 手动发送挖机更新

测试将模拟以下场景：
- 每30秒发送设备初始化数据
- 每3秒随机更新一个矿车数据
- 每5秒随机更新一个挖机数据
- 随机生成位置、电量、状态等动态数据
  `)
}
