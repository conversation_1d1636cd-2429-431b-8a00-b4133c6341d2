/**
 * @file 设备管理-矿卡管理-类型声明文件
 * <AUTHOR>
 * @date 2024/11/25
 */

// table字段声明
export const columnMap: any = new Map([
  ["名称", "name"],
  ["编码", "code"],
  ["状态", "status"],
  ["型号", "modelNumber"],
  ["绑定流量卡", "cardName"],
  ["承载能力", "loadAbilities"],
  ["ACU版本", "acuVersion"],
  ["授权剩余时长", "authTime"]
]);
// 新增/修改form字段声明
export const formMap = new Map([
  ["矿卡名称", "name"],
  ["矿卡编码", "code"],
  ["型号", "modelNumber"],
  ["绑定流量卡", "card"],
  ["承载能力", "loadAbilities"]
]);
// 矿卡状态枚举
export enum StatusEnum {
  /** 空闲中*/
  TRAIN_READY = 1000,
  /** 运行中*/
  TRAIN_BUSY = 1001,
  /** 充电中*/
  TRAIN_CHARGING = 1002,
  /** 故障中*/
  TRAIN_FAILURE = 1003,
  /** 离线中*/
  TRAIN_OFFLINE = 1006
}

// 矿卡状态
export const miningTruckStatusEnum = [
  {
    bg: "rgba(234, 240, 255)",
    status: StatusEnum.TRAIN_READY,
    color: "rgba(53, 106, 253, 1)",
    text: "空闲中"
  },
  {
    bg: "rgba(229, 249, 244)",
    status: StatusEnum.TRAIN_BUSY,
    color: "rgba(0, 194, 144, 1)",
    text: "运行中"
  },
  {
    bg: "rgba(229, 251, 251)",
    status: StatusEnum.TRAIN_CHARGING,
    color: "rgba(0, 209, 217, 1)",
    text: "充电中"
  },
  {
    bg: "rgba(255, 241, 236)",
    status: StatusEnum.TRAIN_FAILURE,
    color: "rgba(249, 116, 75, 1)",
    text: "故障中"
  },
  {
    bg: "rgba(239, 239, 239)",
    status: StatusEnum.TRAIN_OFFLINE,
    color: "rgba(101, 102, 102, 1)",
    text: "离线中"
  }
];
// 节点表格字段
export const nodeColumnMap: any = new Map([
  ["节点编码", "code"],
  ["节点类型", "type"],
  ["版本号", "version"],
  ["状态", "status"]
]);
// 节点状态枚举
export enum NodeStatusEnum {
  /** 正常*/
  NORMAL = "1000",
  /** 异常*/
  ABNORMAL = "1001",
  /** 离线*/
  OFFLINE = "1002"
}
// 节点状态
export const nodeStatusEnum = [
  {
    bg: "rgba(234, 240, 255)",
    status: NodeStatusEnum.NORMAL,
    color: "rgba(53, 106, 253, 1)",
    text: "正常"
  },
  {
    bg: "rgba(254, 237, 237)",
    status: NodeStatusEnum.ABNORMAL,
    color: "rgba(242, 85, 85, 1)",
    text: "异常"
  },
  {
    bg: "rgba(239, 239, 239)",
    status: NodeStatusEnum.OFFLINE,
    color: "rgba(101, 102, 102, 1)",
    text: "离线"
  }
];
