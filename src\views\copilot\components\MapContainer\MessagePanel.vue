<template>
  <div class="message-panel">
    <el-collapse v-model="activeName" accordion class="card-container">
      <el-collapse-item name="error">
        <template #title>
          <span class="total" style="color: var(--el-color-error)">{{ faultMessageTotal }}</span>
          <el-text size="large" tag="b" style="flex: 1">实时故障</el-text>
        </template>

        <el-scrollbar v-if="faultMessages.length" style="height: 340px; font-size: 13px">
          <template v-for="item of faultMessages" :key="item.taskCode">
            <el-divider style="margin: 16px 0" />
            <div class="message-box">
              <div
                style="
                  overflow: hidden;
                  white-space: nowrap;
                  text-overflow: ellipsis;
                  display: flex;
                  justify-content: space-between;
                  width: 100%;
                  gap: 10px;
                "
              >
                <span>故障码：{{ item.taskCode }}</span>
                <span>{{ formatTime(item.createDate) }}({{ getDuration(item.createDate) }})</span>
              </div>
              <span
                style="width: 100%; overflow: hidden"
                :style="{ color: item.level.toString() === '1' ? 'var(--el-color-error)' : 'var(--el-color-warning)' }"
              >
                <span>{{ item.level }}级故障</span>&nbsp;
                <span>{{ item.content }}</span>
              </span>
            </div>
          </template>
        </el-scrollbar>
        <el-empty v-else image-size="0" style="height: 340px" />
      </el-collapse-item>
    </el-collapse>
    <el-collapse v-model="activeName" accordion class="card-container">
      <el-collapse-item name="obstacle">
        <template #title>
          <span class="total" style="color: var(--el-color-warning)">{{ obstacleMessages?.length }}</span>
          <el-text size="large" tag="b" style="flex: 1">障碍物信息</el-text>
        </template>
        <el-scrollbar v-if="obstacleMessages?.length" style="height: 340px; font-size: 13px">
          <template v-for="item of obstacleMessages" :key="item.id">
            <el-divider style="margin: 16px 0" />
            <div class="message-box">
              <div>
                <div>扫描出障碍物</div>
                <div>{{ item.createDate }}</div>
              </div>
              <div>
                <el-button @click="onLocation(item)" size="default" style="width: 32px; cursor: pointer">
                  <SvgIcon name="ObstacleLocation" icon-style="width:24px;height:24px;" />
                </el-button>
                <el-button @click="onClear(item.id)" size="default" style="width: 32px; cursor: pointer">
                  <SvgIcon name="ObstacleClear" icon-style="width:24px;height:24px;" />
                </el-button>
              </div>
            </div>
          </template>
        </el-scrollbar>
        <el-empty v-else image-size="0" style="height: 340px" />
      </el-collapse-item>
    </el-collapse>
    <MessageBox ref="messageBoxRef" messageBox v-bind="messageProps" @confirm="handleConfirm" @cancel="handleCancel" />
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from "vue";
import { useAllDispatchFaults, useDispatchObstacle } from "@/views/copilot/request";
import { deleteObstacle } from "@/api/modules/copilot";
import dayjs from "dayjs";
import duration from "dayjs/plugin/duration";
import "dayjs/locale/zh-cn";
import MessageBox from "@/views/copilot/components/MessageBox.vue";
import mittBus from "@/utils/mittBus";

dayjs.extend(duration);
dayjs.locale("zh-cn");

const messageBoxRef = ref();
const messageProps = ref({ title: "", message: "" });

interface ErrorMessage {
  id: string;
  title: string;
  level: number;
  content: string;
  status: number;
  deviceId: string;
  deviceName: string;
  taskId: string;
  taskCode: string;
  createAccount: string | null;
  createDate: string;
  resolutionTime: string | null;
}

import SvgIcon from "@/components/SvgIcon/index.vue";
const activeName = ref("error");
const selectedObstacleId = ref("");

const { data } = useAllDispatchFaults<ErrorMessage>()!;
const { data: obstacleMessages, refresh: refreshObstacle } = useDispatchObstacle<any>()!;

// 格式化时间为 HH:mm:ss
const formatTime = (time: string) => {
  return dayjs(time).format("HH:mm:ss");
};

// 计算持续时间
const getDuration = (createTime: string) => {
  const now = dayjs();
  const start = dayjs(createTime);
  const diff = now.diff(start, "second");

  const hours = Math.floor(diff / 3600);
  const minutes = Math.floor((diff % 3600) / 60);
  const seconds = diff % 60;

  let result = "持续";
  if (hours > 0) result += `${hours}小时`;
  if (minutes > 0) result += `${minutes}分`;
  if (seconds > 0 || (!hours && !minutes)) result += `${seconds}秒`;

  return result;
};

const faultMessages = computed(() => {
  if (!data.value) return [];
  const response = data.value;
  return response.records;
});
const faultMessageTotal = computed(() => {
  if (!data.value) return 0;
  return data.value.total;
});

// 清除障碍物
function onClear(id: string) {
  messageProps.value = {
    title: "是否已清除障碍物",
    message: "请确认现场障碍物已被清除！清除障碍物后，该障碍物不会在地图上被标记"
  };
  messageBoxRef.value.show();
  selectedObstacleId.value = id;
}

// 取消清除障碍物
function handleCancel() {
  messageBoxRef.value.hide();
}

// 确认清除障碍物
function handleConfirm() {
  deleteObstacle({ id: selectedObstacleId.value }).then(() => {
    messageBoxRef.value.hide();
    refreshObstacle();
  });
}

// 定位障碍物, 发送事件到 ObstaclePolygonLoader 组件
function onLocation(obstacle: any) {
  mittBus.emit("obstacleLocation", obstacle);
}
</script>

<style lang="scss" scoped>
.message-panel {
  position: absolute;
  z-index: 10;
  width: 280px;
  top: 24px;
  right: 24px;
  color: #c9cad9;
  height: max-content;
  display: flex;
  flex-direction: column;
  gap: 4px;
}
.card-container {
  box-sizing: border-box;
  padding: 0 15px;
}
.message-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}
.total {
  font-size: 20px;
  font-weight: bold;
}
.el-divider:first-child {
  margin-top: 0 !important;
}
</style>
