<template>
  <div class="account-security">
    <div class="title decoration" style="margin-bottom: 20px">安全设置</div>
    <div class="row">
      <div class="left">
        <div class="title">登录密码</div>
        <div class="describe">定期修改密码可提高账号的安全性</div>
      </div>
      <div class="right">
        <!--修改密码-->
        <form-create v-model:api="fApi" :option="option" :rule="rule"></form-create>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
/**
 * @file 右侧账号安全组件模块
 * <AUTHOR>
 * @date 2024/11/13
 */

import { ref } from "vue";
import formCreate from "@form-create/element-ui";
import { passwordFormCreate } from "@/views/profile/AccountCenter/formCreate";
import { editPassword, logoutApi } from "@/api/modules/login";
import { ElMessage } from "element-plus";
import { useUserStore } from "@/stores/modules/user";

const fApi = ref();
const option = {
  resetBtn: false,
  submitBtn: false,
  row: {
    justify: "center"
  }
};
const rule = ref([
  {
    type: "AddBtn",
    props: {
      btn: {
        icon: "",
        style: {
          width: "86px",
          height: "32px",
          borderRadius: "6px",
          color: "#000",
          border: "1px solid #DDE2E8",
          background: "#fff"
        },
        content: "修改密码"
      },
      dialog: { title: "修改密码" },
      submitRequest: params => {
        return editPassword(params).then(async res => {
          const userStore = useUserStore();
          if (res.statusCode === 101) {
            // 1.执行退出登录接口
            await logoutApi();
            // 2.清除 Token、清除信息
            userStore.resetState();
            ElMessage({
              message: "修改密码成功, 请重新登陆！",
              type: "success",
              duration: 5000 // 5000 毫秒即 5 秒
            });
            // 3.重定向到登陆页
            setTimeout(() => {
              window.location.reload();
            }, 800);
          }
        });
      }
    },
    children: [passwordFormCreate]
  }
]);
const props = defineProps<{ data: any }>();

const emits = defineEmits(["updateUserInfo"]);
</script>

<style lang="scss" scoped>
.account-security {
  background-color: white;
  height: 100%;
  border-radius: 8px;
  padding: 20px;

  .title {
    font-size: 16px;
    font-weight: bold;
    color: #1a1a1a;
    display: flex;
    align-items: center;
    gap: 4px;

    &.decoration::before {
      content: "";
      display: inline-block;
      width: 4px;
      height: 16px;
      background: rgba(53, 106, 253, 1);
      border-radius: 2px;
    }
  }

  .row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #eff1f5;
    height: 82px;

    .describe {
      font-size: 14px;
      color: #979998;
      margin-top: 6px;
    }

    .left {
      flex-basis: 300px;
    }
  }

  .bind-status.active {
    color: rgba(53, 106, 253, 1);
  }
}
</style>
