<template>
  <el-config-provider :button="buttonConfig" :locale="zhCn" :size="assemblySize">
    <!--    <PrintTicket />-->
    <router-view></router-view>
  </el-config-provider>
</template>

<script lang="ts" setup>
import { reactive, computed } from "vue";
// import LAEditor from "@/components/LAEditor/index.vue";

import { useTheme } from "@/hooks/useTheme";
import { ElConfigProvider } from "element-plus";

import { useGlobalStore } from "@/stores/modules/global";
// import PrintTicket from "@/components/PrintTicket/index.vue";
import zhCn from "element-plus/es/locale/lang/zh-cn";

const globalStore = useGlobalStore();

// init theme
const { initTheme, resetDarkValue } = useTheme();
resetDarkValue();
initTheme();

// element assemblySize
const assemblySize = computed(() => globalStore.assemblySize);

// element button config
const buttonConfig = reactive({ autoInsertSpace: false });
</script>
