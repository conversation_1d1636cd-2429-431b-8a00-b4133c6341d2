<template>
  <form-create v-model:api="fApi" :option="option" :rule="rule"></form-create>
</template>
<script lang="tsx" setup>
/**
 * @file 系统管理-参数配置
 * <AUTHOR>
 * @date 2024/11/14
 */

import { ref } from "vue";
import { getParamList, addParam, deleteParam } from "@/api/modules";
import { columnMap } from "./types";
import { parameterFormCreate } from "./components/formCreate";
import formCreate from "@form-create/element-ui";
import { parameterStatusInfo } from "./types";

const fApi = ref();
const option = {
  form: { inline: true },
  resetBtn: false,
  submitBtn: false
};
const rule = ref([
  {
    type: "SearchFormOperation",
    wrap: { style: "marginBottom: 0" },
    field: "v:search",
    children: [
      {
        type: "input",
        field: "search",
        props: {
          size: "default",
          placeholder: "参数名称"
        }
      },
      {
        type: "LAImport",
        slot: "suffix",
        props: {
          title: "导入参数配置",
          uploadUrl: `${import.meta.env.MODE === "development" ? `${import.meta.env.VITE_API_URL + "/usercenter-server/system/disposition/uploadDispositionFile"}` : "/usercenter-server/system/disposition/uploadDispositionFile"}`,
          downloadUrl: `${import.meta.env.MODE === "development" ? `${import.meta.env.VITE_API_URL + "/usercenter-server/system/disposition/downloadUploadFile"}` : "/usercenter-server/system/disposition/downloadUploadFile"}`
        },
        on: {
          onSuccess: () => {
            // 刷新，调用组件内部请求方法
            fApi.value.exec("v:search", "onSearch");
          }
        }
      },
      {
        type: "LAExport",
        slot: "suffix",
        props: {
          title: "导出参数配置",
          downloadUrl: `${import.meta.env.MODE === "development" ? `${import.meta.env.VITE_API_URL + "/usercenter-server/system/disposition/exportFile"}` : "/usercenter-server/system/disposition/exportFile"}`
        }
      },
      // 新增
      {
        type: "AddBtn",
        slot: "suffix",
        props: {
          btn: { content: "新增参数", auth: "add" },
          dialog: { title: "新增参数" },
          size: "default",
          submitRequest: addParam
        },
        children: [parameterFormCreate]
      }
    ]
  },
  {
    type: "ProTable",
    props: {
      columns: [
        {
          prop: columnMap.get("参数名称"),
          label: "参数名称"
        },
        {
          prop: columnMap.get("参数值"),
          label: "参数值"
        },
        {
          prop: columnMap.get("触发事件"),
          label: "触发事件"
        },

        {
          prop: "status",
          label: "状态",
          tag: true,
          enum: [...parameterStatusInfo]
        },
        { prop: "operation", label: "操作", fixed: "right" }
      ],
      fetch: getParamList,
      operations: [
        { content: "修改", action: "edit" },
        { content: "删除", action: "delete", props: { style: { color: "rgba(242, 85, 85, 1)" } } }
      ]
    },
    children: [
      {
        type: "EditBtn",
        props: {
          action: "edit",
          dialog: { title: "修改参数" },
          submitRequest: addParam
        },
        children: [parameterFormCreate]
      },
      {
        type: "ConfirmDialog",
        on: {
          // 监听弹窗组件抛出的的afterSubmit事件，用于刷新页面
          afterSubmit: () => {
            // 刷新，调用组件内部请求方法
            fApi.value.exec("v:search", "onSearch");
          }
        },
        props: {
          action: "delete",
          subtitle: row => {
            return row.name;
          },
          title: "是否删除参数",
          message: "删除后不可恢复",
          // 模拟请求param：参数
          submitRequest: deleteParam
        }
      }
    ]
  }
]);
</script>
<style lang="scss" scoped>
:deep(.el-row) {
  height: var(--page-height);
}

.el-form-item {
  margin-right: 4px !important;
}
</style>
