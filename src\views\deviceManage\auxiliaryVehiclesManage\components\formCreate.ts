/**
 * @file 设备管理-辅助车辆管理-表单创建文件
 * <AUTHOR>
 * @date 2024/11/26
 */
import { formMap } from "../types";
import LASelect from "@/components/LASelect";
import { getMobileTrafficList } from "@/api/modules/device";
import { getDictionaryEnum } from "@/api/modules";

export const auxiliaryVehiclesFormCreate = {
  type: "form-create",
  props: {
    option: {
      submitBtn: false,
      onSubmit(formData, api) {
        // 通知 table 搜索数据变化，刷新数据
        api.top.bus.$emit("searchFormChanged");
      }
    },
    rule: [
      {
        type: "input",
        field: formMap.get("辅助车辆名称"),
        title: "辅助车辆名称",
        validate: [
          { required: true, message: "请输入辅助车辆名称" },
          {
            pattern: /^.{1,20}$/,
            message: "字符限长20位"
          }
        ]
      },
      {
        type: "input",
        field: formMap.get("辅助车辆编码"),
        title: "辅助车辆编码",
        validate: [{ required: true, message: "请输入辅助车辆编码" }]
      },
      {
        component: LASelect,
        field: formMap.get("车辆类型"),
        props: {
          replaceFields: { key: "id", label: "dname", value: "code" },
          fetch: getDictionaryEnum,
          params: { code: "CarType" }
        },
        title: "车辆类型"
      },
      {
        type: "input",
        field: formMap.get("OBU编码"),
        title: "OBU编码"
      }
    ]
  }
};
