<template>
  <div class="dispatch-task">
    <LACard :showHeader="false" shadow="never">
      <LATitle :bottom="0" title="调度任务"></LATitle>
      <div v-if="!hasPermission" class="task-container">
        <div class="task-executing task">
          <div class="task-title">执行中</div>
          <div class="task-content">{{ data?.inProgressCount }}</div>
        </div>
        <div class="task-stop task">
          <div class="task-title">暂停中</div>
          <div class="task-content" style="color: rgba(249, 116, 75, 1)">{{ data?.waitCount }}</div>
        </div>
        <div class="task-not-started task">
          <div class="task-title">未开始</div>
          <div class="task-content">{{ data?.pendingExecutionCount }}</div>
        </div>
      </div>
      <PlaceholderImage v-else type="noPermission" />
    </LACard>
  </div>
</template>

<script lang="ts" setup>
/**
 * @file 首页-调度任务
 * <AUTHOR>
 * @date 2025/1/21
 */
import LATitle from "@/components/LATitle.vue";
import LACard from "@/components/LACard/index.vue";
import PlaceholderImage from "@/components/PlaceholderImage.vue";
import { computed } from "vue";

import { useDispatchProjectDetailStatisticsCount } from "@/views/home/<USER>";

const { data, error } = useDispatchProjectDetailStatisticsCount();
// 401则为没有权限
const hasPermission = computed(() => {
  return error.value?.status === 401;
});
</script>

<style scoped>
.task-container {
  flex: 1;
  display: flex;
  gap: 10px;
  padding-top: 20px;
  .task-title {
    font-size: 14px;
    white-space: nowrap;
    color: #656666;
  }

  .task-content {
    font-size: 28px;
    color: #1a1a1a;
    font-weight: bold;
  }

  .task {
    display: flex;
    flex-direction: column;
    flex: 1;
    gap: 10px;
    padding: 20px;
  }

  .task-executing {
    background-color: rgba(0, 194, 144, 0.08);
  }

  .task-not-started {
    background-color: rgba(245, 249, 252, 1);
  }

  .task-stop {
    background-color: rgba(249, 116, 75, 0.08);
  }
}

@media screen and (max-width: 1500px) {
  .task-container {
    overflow-x: scroll;
  }
}
</style>
<style lang="scss"></style>
