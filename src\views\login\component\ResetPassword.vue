<template>
  <!--  重置密码弹窗  -->
  <el-dialog
    :before-close="beforeClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :model-value="visible"
    :show-close="false"
    class="reset-dialog"
    title="请设置新密码"
    width="460px"
  >
    <div class="reset-container">
      <span class="reset-header">您目前使用的是默认密码，为了保障您的账户安全<br />请设置新密码后重新登录。</span>
      <!--   表单组件   -->
      <el-form ref="resetFormRef" :model="resetFormFields" :rules="rules" label-width="0px"
        ><el-form-item prop="password">
          <el-input
            v-model="resetFormFields.password"
            :input-style="inputStyle"
            autocomplete="off"
            clearable
            placeholder="8~20位、包含字母和数字"
            show-password
            size="large"
            type="password"
          >
            <template #prefix>
              <img :src="lock" class="icon" />
            </template>
          </el-input>
        </el-form-item>
        <el-form-item prop="checkPass">
          <el-input
            v-model="resetFormFields.checkPass"
            :input-style="inputStyle"
            autocomplete="off"
            clearable
            placeholder="请再次输入新密码"
            show-password
            size="large"
            type="password"
          >
            <template #prefix>
              <img :src="lock" class="icon" />
            </template>
          </el-input>
        </el-form-item>
      </el-form>
      <el-button class="submit-btn" round size="large" type="primary" @click="onResetPassword"> 确定修改 </el-button>
    </div>
  </el-dialog>
</template>

<script lang="ts" setup>
/**
 * @file 密码重置弹窗组件
 * <AUTHOR>
 * @date 2024/11/4
 */
import { reactive, ref } from "vue";
import lock from "../image/lock.png";
import { ElMessage } from "element-plus";
// import { editPassword } from "/@/api/base";
import { editPassword } from "@/api/modules/login";

import { setRulePattern } from "@/utils/rules";
import { useUserStore } from "@/stores/modules/user";
// import { removeToken, removeUserInfo } from "/@/utils/user";
const props = withDefaults(
  defineProps<{
    // 弹窗是否可见
    visible?: boolean;
    // 用户的旧密码
    oldPassword?: string;
  }>(),
  {
    visible: false,
    oldPassword: ""
  }
);

const emits = defineEmits(["update:visible"]);

const resetFormRef = ref();
const userStore = useUserStore();

const resetFormFields = reactive({
  password: "",
  checkPass: ""
});
const inputStyle = {
  borderRadius: "25px",
  height: "50px",
  fontSize: "16px",
  background: "transparent"
};

/**
 * 验证password字段的函数
 * @param rule 验证规则
 * @param value 新密码输入框的值
 * @param callback 回调函数
 * <AUTHOR>
 * @date 2024/11/4
 */
const validatePass = (rule: any, value: any, callback: any) => {
  // 如果值为空,则提示输入密码
  if (value === "") {
    callback(new Error("密码必须包含字母、数字和特殊字符，且长度为8到20位"));
  } else {
    // 如果输入了密码且符合规则,且重复密码输入框有值,则手动激活重复密码输入框的验证,验证两次输入密码是否一致
    if (resetFormFields.checkPass !== "") {
      if (!resetFormRef.value) return;
      resetFormRef.value.validateField("checkPass", () => null);
    }
    callback();
  }
};
/**
 * 验证checkPass字段的函数
 * @param rule 验证规则
 * @param value 重复密码输入框的值
 * @param callback 回调函数
 * <AUTHOR>
 * @date 2024/11/4
 */
const validateCheckPass = (rule: any, value: any, callback: any) => {
  if (value === "") {
    callback(new Error("请再次输入登录密码"));
  } else if (value !== resetFormFields.password) {
    callback(new Error("两次密码输入不一致"));
  } else {
    callback();
  }
};

// 表单验证规则
const rules = {
  password: [
    { validator: validatePass, trigger: "change" },
    {
      pattern: "^(?=.*[A-Za-z])(?=.*\\d)[A-Za-z\\d\\W]{8,20}$",
      message: "密码必须包含字母、数字,且长度为8到20位"
    }
  ],
  checkPass: [{ validator: validateCheckPass, trigger: "change" }]
};
//设置cookie
const setCookie = (c_name: string, c_pwd: string, exdays: number) => {
  const exdate = new Date(); //获取时间
  exdate.setTime(exdate.getTime() + 24 * 60 * 60 * 1000 * exdays); //保存的天数
  //字符串拼接cookie
  window.document.cookie = `username=${c_name};path=/;expires=${exdate.toUTCString()}`;
  window.document.cookie = `password=${c_pwd};path=/;expires=${exdate.toUTCString()}`;
};
/**
 * 发送重置密码请求
 * <AUTHOR>
 * @date 2024/11/4
 */
const onResetPassword = () => {
  // 表单进行验证
  resetFormRef.value.validate(res => {
    if (!res) return;
    // 如果验证结果正确,则发送请求到后端
    editPassword({
      oldPassword: props.oldPassword,
      newPassword: resetFormFields.password
    }).then(res => {
      // 如果密码重置成功
      if (res.success) {
        let message = ElMessage.success("重置密码成功，请重新登录");
        setCookie("", "", -1); // 清除cookie

        userStore.setToken("");
        userStore.setUserInfo({});
        //     removeUserInfo();
        // 关闭密码重置弹窗
        emits("update:visible", false);
        // 刷新页面,让用户重新登录
        setTimeout(() => {
          message.close();
          window.location.reload();
        }, 500);
      } else {
        ElMessage.error(res.message);
      }
    });
  });
};

// 弹窗关闭之前
const beforeClose = () => {};
</script>

<style lang="scss" scoped>
/*重置密码*/
.reset-container {
  margin: 0 auto;
  width: 380px;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  padding-bottom: 30px;
  //margin-top: -10px;
}

.reset-header {
  width: 100%;
  font-size: 14px;
  font-weight: 400;
  color: #356afdff;
  text-align: center;
  white-space: nowrap;
  padding-bottom: 36px;
}
.icon {
  width: 20px;
  height: 20px;
}

::v-deep(.el-input__prefix-inner) {
  display: flex;
  align-items: center;
}
//::v-deep(.el-input__inner) {
//  --el-input-hover-border-color: #95afef;
//  --el-input-border-color: #cad9fc;
//  &::placeholder {
//    color: #bdbdbf;
//  }
//}

:deep(.el-input) {
  --el-input-hover-border-color: #356afdff;
  --el-input-border-color: #b5c8f3ff;
  --el-text-color-placeholder: #a6bdf3ff;
  --el-border-radius-base: 12px;
  &::placeholder {
    color: #a6bdf3;
  }
}
::v-deep(.el-form-item) {
  margin-bottom: 20px;
}
.submit-btn {
  background: #356afd;
  height: 50px;
  border-radius: 12px;
  font-size: 18px;
}
</style>
<style>
.reset-dialog {
  font-size: 20px;
  border-radius: 12px;
  .el-dialog__header {
    padding: 20px 20px 10px;
    font-weight: bold;
    line-height: 1;
  }
  .el-dialog__body {
    padding: 10px 20px;
  }
}
</style>
