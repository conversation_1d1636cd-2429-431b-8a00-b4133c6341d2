/**
 * @file 系统管理-参数配置-表单文件
 * <AUTHOR>
 * @date 2024/11/8
 */

import { formMap } from "../types";

export const parameterFormCreate = {
  type: "form-create",
  props: {
    rule: [
      {
        type: "input",
        field: formMap.get("参数名称"),
        title: "参数名称",
        validate: [
          { required: true, message: "请输入参数名称" },
          {
            pattern: /^.{1,20}$/,
            message: "字符限长20位"
          }
        ]
      },
      {
        type: "input",
        field: formMap.get("参数值"),
        title: "参数值",
        validate: [{ required: true, message: "请输入参数值" }]
      },
      {
        type: "input",
        field: formMap.get("触发事件"),
        title: "触发事件",
        validate: [{ required: true, message: "请输入触发事件" }]
      },
      {
        type: "radio",
        field: formMap.get("状态"),
        title: "状态",
        value: 0,
        options: [
          {
            label: "启用",
            value: 0
          },
          {
            label: "禁用",
            value: 1
          }
        ],
        validate: [{ required: true, message: "请选择状态" }]
      }
    ],
    option: {
      submitBtn: false,
      onSubmit(formData, api) {
        // 通知 table 搜索数据变化，刷新数据
        api.top.bus.$emit("searchFormChanged");
      }
    }
  }
};
