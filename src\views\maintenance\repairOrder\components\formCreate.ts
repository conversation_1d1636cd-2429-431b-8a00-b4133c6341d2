/**
 * @file 维修保养-维修工单-详情文件
 * <AUTHOR>
 * @date 2025/2/11
 */
import LASelect from "@/components/LASelect.tsx";
import { getAllMineTrainList } from "@/api/modules/device.ts";
import { getAllUserList } from "@/api/modules";
import { fillRepairResultFormMap, formMap, transferOrderFormMap } from "@/views/maintenance/repairOrder/types.ts";
// 新增维修工单
export const repairOrderFormCreate = {
  type: "form-create",
  props: {
    option: {
      submitBtn: false,
      onSubmit(formData, api) {
        console.log("formData", formData);
        // 通知 table 搜索数据变化，刷新数据
        api.top.bus.$emit("searchFormChanged");
      }
    },
    rule: [
      {
        component: LASelect,
        field: formMap.get("矿车名称"),
        props: {
          replaceFields: { key: "id", label: "name", value: "id" },
          fetch: getAllMineTrainList
        },
        title: "矿车名称",
        validate: [{ required: true, message: "请选择矿车" }]
      },
      {
        type: "input",
        field: formMap.get("故障码"),
        title: "故障码",
        validate: [
          {
            validator: (rule, value) => !/[\u4e00-\u9fa5]/.test(value),
            message: "故障码不能包含中文"
          }
        ]
      },
      {
        type: "input",
        field: formMap.get("故障描述"),
        validate: [
          { required: true, message: "请输入故障描述" },
          {
            pattern: /^.{1,200}$/,
            message: "故障描述最多200个字符"
          }
        ],
        title: "故障描述"
      },
      {
        type: "DatePicker",
        field: formMap.get("故障时间"),
        title: "故障时间",
        style: {
          width: "100%"
        },
        props: {
          type: "datetime",
          format: "YYYY-MM-DD HH:mm"
        },
        validate: [{ required: true, message: "请选择故障时间" }]
      },
      {
        component: LASelect,
        field: formMap.get("报修人"),
        props: {
          replaceFields: { key: "id", label: "employeeName", value: "id" },
          fetch: getAllUserList,
          onChange: (value, data, formCreateInject) => {
            formCreateInject.api.setValue(formMap.get("报修人名称"), data.employeeName);
          }
        },

        title: "报修人",
        validate: [{ required: true, message: "请选择报修人" }]
      },
      {
        component: LASelect,
        field: formMap.get("维修负责人"),
        props: {
          replaceFields: { key: "id", label: "employeeName", value: "id" },
          fetch: getAllUserList,
          onChange: (value, data, formCreateInject) => {
            formCreateInject.api.setValue(formMap.get("维修负责人名称"), data.employeeName);
          }
        },
        title: "维修负责人",
        validate: [{ required: true, message: "请选择维修负责人" }]
      }
    ]
  }
};
// 关闭工单
export const closeFormCreate = {
  type: "form-create",
  props: {
    option: {
      submitBtn: false,
      onSubmit(formData, api) {
        // 通知 table 搜索数据变化，刷新数据
        api.top.bus.$emit("searchFormChanged");
      }
    },
    rule: [
      {
        type: "input",
        title: "维修备注",
        props: {
          type: "textarea"
        },
        field: "repairContent",
        validate: [{ required: true, message: "请输入维修备注" }]
      }
    ]
  }
};
// 转让工单
export const transferFormCreate = {
  type: "form-create",
  name: "transferFormCreate",
  props: {
    option: {
      submitBtn: false,
      onSubmit(formData, api) {
        api.top.bus.$emit("searchFormChanged");
      }
    },
    rule: [
      {
        component: LASelect,
        field: transferOrderFormMap.get("维修负责人"),
        props: {
          replaceFields: { key: "id", label: "employeeName", value: "id" },
          fetch: getAllUserList,
          onChange: (value, data, formCreateInject) => {
            formCreateInject.api.setValue(transferOrderFormMap.get("维修负责人名称"), data.employeeName);
          }
        },
        title: "维修负责人",
        validate: [{ required: true, message: "请选择维修负责人" }]
      }
    ]
  }
};
// 填写维修结果
export const repairResultFormCreate = {
  type: "form-create",
  props: {
    option: {
      submitBtn: false,
      onSubmit(formData, api) {
        api.top.bus.$emit("searchFormChanged");
      }
    },
    rule: [
      {
        type: "radio",
        field: fillRepairResultFormMap.get("维修结果"),
        title: "维修结果",
        options: [
          { value: "repaired", label: "已修复" },
          { value: "unRepaired", label: "未修复" }
        ],
        validate: [{ required: true, message: "请选择维修结果" }]
      },
      {
        type: "input",
        title: "维修备注",
        props: {
          type: "textarea"
        },
        field: fillRepairResultFormMap.get("维修内容"),

        validate: [{ required: true, message: "请输入维修内容" }]
      }
    ]
  }
};
