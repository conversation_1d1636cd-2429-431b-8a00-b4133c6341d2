import type { VcViewerProvider } from "vue-cesium/es/utils/types";
import { useMapActionChannel } from "@/views/map/channel";

interface Position {
  lng: number;
  lat: number;
  elevations: number[];
}

interface OSMRoute {
  longitude: number;
  latitude: number;
  elevations: number[];
}

export class AddFreePoint {
  private viewer: Cesium.Viewer;
  private addPointCallback: (position: Position) => void;
  private locationActionActive: boolean = false;

  constructor(vcViewer: VcViewerProvider, addPointCallback: (position: Position) => void) {
    this.viewer = vcViewer.viewer;
    this.addPointCallback = addPointCallback;
  }

  setLocationActive(active: boolean) {
    this.locationActionActive = active;
  }

  // 判断是否是路径
  private isOSMRoute(event: any): boolean {
    const route = this.getOSMRoute(event);
    if (!route) return false;
    return route.elevations?.length > 0;
  }

  private getOSMRoute(event: any): OSMRoute | undefined {
    const pickedPosition = this.viewer.scene.pickPosition(event.windowPosition);
    if (!pickedPosition) return;

    const cartographic = Cesium.Cartographic.fromCartesian(pickedPosition);
    const longitude = Cesium.Math.toDegrees(cartographic.longitude);
    const latitude = Cesium.Math.toDegrees(cartographic.latitude);

    const features = this.viewer.scene.drillPick(event.windowPosition);
    const elevations = features
      .filter(feature => !!feature.id.properties?.ele?.getValue())
      .map(feature => feature.id.properties?.ele?.getValue());

    return { longitude, latitude, elevations };
  }

  // 处理鼠标按下事件
  handleMouseDown(event: any) {
    if (event.button !== 0 || !this.locationActionActive || !this.isOSMRoute(event)) return;
    const point = this.getPositionFromEvent(event);
    if (point) {
      this.addPointCallback(point);
      // 锁定画布交互 用于创建表示朝向的箭头
      this.viewer.scene.screenSpaceCameraController.enableInputs = false;
      this.viewer.screenSpaceEventHandler.setInputAction(() => {
        this.handleMouseUp(point);
      }, Cesium.ScreenSpaceEventType.LEFT_UP);
    }
  }

  // 处理鼠标抬起事件, 显示定位移动的弹窗
  handleMouseUp(position: Position) {
    this.viewer.scene.screenSpaceCameraController.enableInputs = true;
    // 发送定位移动事件
    useMapActionChannel().post({ type: "location-move", data: { position, elevations: position.elevations } });
  }

  // 处理鼠标经过事件
  handleMouseOver(event: any) {
    if (!this.locationActionActive) {
      this.setCursor("default");
      return;
    }

    const cursor = this.isOSMRoute(event) ? "crosshair" : "default";
    this.setCursor(cursor);
  }

  // 处理鼠标离开事件
  handleMouseOut() {
    this.setCursor("default");
  }

  private setCursor(cursorStyle: string) {
    const root = document.getElementById("root");
    if (root) {
      root.style.cursor = cursorStyle;
    }
  }

  private getPositionFromEvent(event: any): Position | undefined {
    const route = this.getOSMRoute(event);
    if (!route) return;

    return {
      lng: route.longitude,
      lat: route.latitude,
      elevations: route.elevations
    };
  }
}
