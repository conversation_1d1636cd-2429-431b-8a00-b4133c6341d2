<template>
  <form-create v-model:api="fApi" :option="option" :rule="rule"></form-create>
</template>
<script lang="tsx" setup>
/**
 * @file 调度管理-呼叫任务
 * <AUTHOR>
 * @date 2025/7/22
 */
import { computed, ref } from "vue";
import { columnMap, StatusInfo } from "./types";
import { useUserStore } from "@/stores/modules/user";
import formCreate from "@form-create/element-ui";
import { getFaultList } from "@/api/modules/historyData.ts";
import LASelect from "@/components/LASelect.tsx";
import { getAllMineTrainList } from "@/api/modules/device.ts";
import { detailFormCreate } from "@/views/dispatcher/callTask/components/formCreate.ts";

// 获取个人信息
const userInfo = useUserStore().userInfo;
// 保存表格选项数据
const selData: any = ref([]);
const fApi = ref();
const option = {
  form: { inline: true },
  resetBtn: false,
  submitBtn: false
};

const rule = ref([
  {
    type: "SearchFormOperation",
    field: "v:search",
    wrap: { style: "marginBottom: 0" },
    children: [
      {
        component: LASelect,
        field: "deviceId",
        style: { width: "200px", lineHeight: "initial" },
        props: {
          fetch: getAllMineTrainList,
          replaceFields: { key: "id", label: "name", value: "id" },
          placeholder: "铲点"
        }
      },
      {
        component: LASelect,
        field: "deviceId",
        style: { width: "200px", lineHeight: "initial" },
        props: {
          fetch: getAllMineTrainList,
          replaceFields: { key: "id", label: "name", value: "id" },
          placeholder: "卸点"
        }
      },
      {
        component: LASelect,
        field: "status",
        style: { width: "200px", lineHeight: "initial" },
        props: {
          list: [
            {
              label: "未结束",
              value: 0
            },
            {
              label: "已结束",
              value: 1
            }
          ],
          placeholder: "状态"
        }
      },
      {
        component: LASelect,
        field: "status",
        style: { width: "200px", lineHeight: "initial" },
        props: {
          list: [
            {
              label: "未结束",
              value: 0
            },
            {
              label: "已结束",
              value: 1
            }
          ],
          placeholder: "物料类型"
        }
      },
      {
        type: "LADateTimeRangePicker",
        name: "time",
        style: { lineHeight: "initial", height: "32px" },
        props: {
          type: "daterange",
          format: "YYYY-MM-DD",
          placeholder: ["起始日期", "截止日期"]
        },

        on: {
          "update:start": val => {
            if (val) {
              fApi.value.form["startTime"] = val;
            } else {
              fApi.value.form["startTime"] = undefined;
            }
          },
          "update:end": val => {
            if (val) {
              fApi.value.form["endTime"] = val;
            } else {
              fApi.value.form["endTime"] = undefined;
            }
          }
        }
      },
      {
        type: "input",
        field: "code",
        style: { width: "200px" },
        props: {
          size: "default",
          placeholder: "任务编号"
        }
      },
      {
        type: "ConfirmDialog",
        on: {
          // 监听弹窗组件抛出的的afterSubmit事件，用于刷新页面
          afterSubmit: () => {
            // 刷新，调用组件内部请求方法
            fApi.value!.exec("v:search", "onSearch");
            // 操作后,取消勾选
            fApi!.value.el("v:table").clearSelection();
          }
        },
        slot: "suffix",
        props: {
          auth: "update",
          disabled: computed(() => {
            return selData.value.length === 0;
          }),
          buttonText: "取消",
          title: "是否取消任务",
          message: "取消后,系统将会暂停取消任务",
          // 模拟请求param：参数
          submitRequest: () => {
            // 调用组件实例方法
            const ids = selData.value.map((item: any) => item.id).join(",");
            console.log(4444, ids);

            // return waitDispatchTask({ projectDetailsIds: ids });
            return Promise.reject(true);
          }
        }
      }
    ]
  },
  {
    type: "ProTable",
    field: "v:table",
    wrap: {
      style: {
        flex: 1,
        margin: 0
      }
    },
    props: {
      columns: [
        { type: "selection", label: "计划编号" },
        {
          prop: columnMap.get("矿车名称"),
          label: "任务编号"
        },
        {
          prop: columnMap.get("状态"),
          label: "状态",
          tag: true,
          enum: [...StatusInfo]
        },
        {
          prop: columnMap.get("铲点"),
          label: "铲点"
        },
        {
          prop: columnMap.get("地磅"),
          label: "地磅"
        },
        {
          prop: columnMap.get("卸点"),
          label: "卸点"
        },
        {
          prop: columnMap.get("物料类型"),
          label: "物料类型"
        },
        {
          prop: columnMap.get("呼叫人"),
          label: "呼叫人"
        },
        {
          prop: columnMap.get("呼叫时间"),
          label: "呼叫时间"
        },
        {
          prop: columnMap.get("矿车"),
          label: "矿车"
        },
        {
          prop: columnMap.get("开始时间"),
          label: "开始时间"
        },
        {
          prop: columnMap.get("重量"),
          label: "重量(t)"
        },
        {
          prop: columnMap.get("里程"),
          label: "里程(km)"
        },
        {
          prop: columnMap.get("结束/取消时间"),
          label: "结束/取消时间"
        },

        { prop: "operation", label: "操作", fixed: "right" }
      ],
      fetch: getFaultList,
      operations: [
        {
          content: "详情",
          action: "detail"
        }
      ]
    },
    on: {
      // 勾选的方法
      selectionChange: (row: any) => {
        selData.value = row;
      }
    },
    children: [
      // 详情组件
      {
        type: "DetailBtn",
        props: {
          action: "detail",
          dialog: {
            class: "task-record-detail",
            title: "呼叫任务详情"
          }
        },
        children: [detailFormCreate]
      }
    ]
  }
]);
</script>
<style lang="scss" scoped>
:deep(.el-row) {
  height: 100%;
  overflow: hidden;
  flex-wrap: nowrap;
  justify-content: space-between;
  flex-direction: column;
  .el-select--large .el-select__wrapper {
    min-height: initial;
  }
  .table-main {
    flex: 1;
  }
}

.el-form-item {
  margin-right: 4px !important;
}
</style>
<style lang="scss">
.task-record-detail {
  width: 400px !important;

  .el-form {
    height: 100%;

    .el-row {
      display: inherit;
      height: 100%;
    }
  }
}
</style>
