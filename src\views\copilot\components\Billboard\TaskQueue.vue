<template>
  <div class="task-queue card-container">
    <el-text size="large" tag="b">任务队列</el-text>
    <el-divider style="background-color: var(--el-bg-color); height: 2px; margin: 14px 0" />
    <el-empty v-if="!data?.length" image-size="0" style="height: 348px" />
    <el-scrollbar>
      <task-steps :list="data" />
    </el-scrollbar>
  </div>
</template>

<script lang="ts" setup>
import TaskSteps from "./TaskSteps.vue";
import { useTaskQueue } from "@/views/copilot/request";

const { data } = useTaskQueue<{ [key: string]: any }>()! as unknown as { data: { [key: string]: any }[] };
</script>

<style lang="scss" scoped>
.task-queue.card-container {
  flex: 1;
  display: flex;
  height: 100%;
  flex-direction: column;
  box-sizing: border-box;
  padding: 16px;
}
</style>
