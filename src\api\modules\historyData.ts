import http from "@/api";
/**
 * @name 综合管理平台-历史数据-api接口统一出口
 */

// 历史数据-任务记录
export const getTaskList = (params: any) => {
  return http.post("/dispatch-server/dispatch/task/listQueryTaskByPage", params);
};
// 历史数据-充电记录
export const getChargeList = (params: any) => {
  return http.post("/dispatch-server/dispatch/chargeRecord/listQueryByPage", params);
};
// 历史数据-故障记录
export const getFaultList = (params: any) => {
  return http.post("/dispatch-server/dispatch/faults/listQueryByPage", params);
};

// 历史数据-障碍物记录
export const getObstacleRecordList = (params: any) => {
  return http.post("/dispatch-server/dispatch/obstacle/listQueryByPage", params);
};
