import type { ButtonProps, DialogProps } from "element-plus";
import type { Api } from "@form-create/element-ui";

export type DialogBtnProps = {
  // 提交请求
  dialog?: Partial<DialogProps> & { subtitle?: string | Function; style?: Record<string, string> };
  btn?: ButtonProps & { content?: string; show: boolean; auth?: string; isPureButton?: boolean };
  rowData?: any;
  // 提交请求
  submitRequest?: (data: { [field: string]: any }, api?: Api) => Promise<any>;
  formCreateInject: { api: Api; rule: any };
  // 事件 键
  action?: string;
  // 自定义api函数
  customApi?: (api: any) => any;
  // formCreate 名称, 用来解决children formCreate 查找错误的问题
  n?: string;
};
