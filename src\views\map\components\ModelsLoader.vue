<template>
  <ModelLoader
    v-for="(item, deviceId) in modelMap"
    :key="deviceId"
    :modelPath="modelPath"
    :currentLocation="item.currentLocation"
    :electricQuantity="item.electricQuantity"
    :speed="item.speed"
    :driveMode="item.driveMode"
    :zoomTo="false"
    :showDialog="true"
  />
</template>

<script lang="ts" setup>
import ModelLoader from "./ModelLoader.vue";
import { reactive, onMounted, watch } from "vue";
import { useMapUpdateChannel, EventChannel } from "@/views/map/channel";
import { TruckChannelMessage, TruckMessage } from "../types";

const modelPath = "https://zouyaoji.top/vue-cesium/SampleData/models/GroundVehicle/GroundVehicle.glb";
const modelMap = reactive<Record<string, TruckMessage>>({});

onMounted(() => {
  watch(useMapUpdateChannel<TruckChannelMessage>().data, newData => {
    switch (newData.type) {
      case EventChannel.update:
        processTruckMessage(newData.data);
        break;
    }
  });
});

function processTruckMessage(message: TruckChannelMessage | TruckChannelMessage[]) {
  const messages = Array.isArray(message) ? message : [message];
  messages.forEach(item => {
    if (item.type === "trainMessage") {
      modelMap[item.deviceId] = item.data;
    }
  });
}
</script>
