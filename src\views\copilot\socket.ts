import { useWebSocket, useBroadcastChannel } from "@vueuse/core";
import { ref } from "vue";
import { useAsyncStoreManager } from "./store";
import { useUserStore } from "@/stores/modules/user";
import { useMapChangeChannel } from "@/views/map/channel";
import { getCurrentLatestMap } from "@/api/modules/dispatch";
import { useDeviceData } from "@/composables/useDeviceData";
import type { WSDeviceInitData } from "@/stores/deviceTypes";
// WebSocket 消息类型定义
interface WebSocketMessage {
  messageType: string;
  socketType: string;
  token?: string;
  data?: any;
}

// 创建全局 WebSocket 连接管理
export const useWebSocketManager = () => {
  const userStore = useUserStore();
  const { post } = useBroadcastChannel({ name: "map-channel" });
  const { refreshAll, getStore } = useAsyncStoreManager();
  const { updateFromWebSocket, updateSingleDevice } = useDeviceData();
  const isConnected = ref(false);
  const lastHeartbeat = ref<Date | null>(null);

  // setInterval(() => {
  //   useMapChangeChannel().post({ type: "map-change", data: new Date().toISOString() });
  // }, 3000);

  const message1 = {
    socketType: "unmannedPlatform",
    messageType: "deviceInit",
    data: {
      bulldozersList: [
        { code: "waji01", name: "WJ-01", posture: "104.0665,30.5725,400,东" },
        { code: "2323" },
        { code: "WA-JI2", id: "1866741908189630466", name: "挖机2" },
        { code: "WAJI-1", id: "1864222101510742017", name: "挖机11" },
        { code: "wajibianma0002", name: "WJ-02", posture: "104.0665,30.5725,400,东" },
        { code: "WAJICESHI", name: "挖机测试" },
        { code: "wajibianma0001", name: "WJ-02", posture: "104.0665,30.5725,400,东" },
        { code: "waji03", name: "WJ-03", posture: "104.0665,30.5725,400,东" },
        { code: "2", posture: "104.051,30.597" },
        { code: "WA-JI", id: "1866741797325787138", name: "大挖机" },
        { code: "waji02", id: "1879103496635588609", name: "WJ-02", posture: "104.356,30.2516,0,3.15" },
        { code: "KUANGKA1", name: "kk矿卡", posture: "104.051,30.597" },
        { code: "CESHI", id: "1866742027001679874", name: "色彩挖机" },
        { code: "2312", name: "21321" }
      ],
      mineTrainList: [
        {
          code: "LAKK-202410-1",
          currentLocation: "104.32 30.23 400 东",
          deceleration: 0,
          driveMode: 0,
          electricQuantity: 99,
          fogLamp: 0,
          gasPedal: 0,
          gearLevel: "2",
          handbrake: 0,
          highBeam: 0,
          id: "1876840337556193281",
          liftNumber: 0,
          loadMode: 1,
          lowBeam: 0,
          name: "KK-01",
          redirect: 0,
          speed: 15,
          turnSignalLamp: 0
        },
        { code: "2323", currentLocation: "104.32 30.23 400 东", id: "1867414489519489026", name: "23223" },
        { code: "LAKK2025011", currentLocation: "104.32 30.23 400 东", id: "1879343611731636225", loadMode: 0, name: "K01" },
        { code: "AAAA", currentLocation: "104.32 30.23 400 东", id: "1878649849019629570", name: "222测试" },
        { code: "12", currentLocation: "104.32 30.23 400 东", id: "1879342478397140993", name: "12" },
        { code: "444", currentLocation: "104.32 30.23 400 东", id: "1877286270412640258", name: "44" },
        { code: "12321312321", currentLocation: "104.32 30.23 400 东", id: "1877627143293784065", name: "12345" },
        { code: "LAKK20250120", currentLocation: "104.32 30.23 400 东", id: "1880171334611783681", name: "KK-20" },
        { code: "5858", currentLocation: "104.32 30.23 400 东", id: "1878625916174204930", name: "这是测试？" },
        { code: "888", currentLocation: "104.32 30.23 400 东", id: "1868574097818693633", name: "888" },
        { code: "LAKK-202410-3", currentLocation: "104.32 30.23 400 东", id: "1876872521616801793", name: "KK-03" },
        { code: "LAKK-202410-9", currentLocation: "104.32 30.23 400 东", id: "1877315113877770241", name: "KK-9" },
        { code: "LAKK-202501-1", currentLocation: "104.32 30.23 400 东", id: "1877637661089931265", name: "KK01" },
        { code: "LAKK-202410-10", currentLocation: "104.32 30.23 400 东", id: "1877314904988848130", name: "KK-10" },
        { code: "LAKK202501-11", currentLocation: "104.32 30.23 400 东", id: "1880063716601991170", name: "KK-11" },
        { code: "KD-HH", currentLocation: "104.32 30.23 400 东", id: "1863834133998538753", name: "测试矿卡" },
        { code: "LAKK202501-10", currentLocation: "104.32 30.23 400 东", id: "1880063604513411074", name: "KK-10" },
        { code: "CDFFF", currentLocation: "104.32 30.23 400 东", id: "1863871887830568962", name: "测试XIAO" },
        { code: "878787", currentLocation: "104.32 30.23 400 东", id: "1878679041228697601", name: "7878" },
        { code: "LAKK2025012", currentLocation: "104.32 30.23 400 东", id: "1879343719005155330", name: "K02" },
        { code: "555555555", currentLocation: "104.32 30.23 400 东", id: "1877618590994788353", name: "测试矿卡8" },
        { code: "LAKK-202410-11", currentLocation: "104.32 30.23 400 东", id: "1877524587120951298", loadMode: 0, name: "KK-11" },
        { code: "11", currentLocation: "104.32 30.23 400 东", id: "1879340926127173634", name: "111" },
        { code: "LAKK-202501-2", currentLocation: "104.32 30.23 400 东", id: "1877637738726498305", name: "KK02" },
        { code: "1", currentLocation: "104.32 30.23 400 东", id: "1867453442482110466", name: "11" },
        { code: "424", currentLocation: "104.32 30.23 400 东", id: "1867414801122721793", name: "342432" },
        { code: "VE", currentLocation: "104.32 30.23 400 东", id: "1867174214406672386", name: "1" },
        { code: "LAKK202501-1", currentLocation: "104.32 30.23 400 东", id: "1879717294891388930", name: "kk-01" }
      ]
    }
  };

  // 处理所有WebSocket消息
  const handleWebSocketMessage = (data: WebSocketMessage) => {
    console.log("[WebSocket] Received message:", data);
    switch (data.messageType) {
      case "mapChange":
        // 地图数据变化，刷新所有数据
        getCurrentLatestMap().then(res => {
          // @ts-ignore
          if (res.data?.filePath) useMapChangeChannel().post({ type: "map-change", data: res.data.filePath });
        });
        break;

      case "pong":
        // 心跳响应
        lastHeartbeat.value = new Date();
        break;

      case "deviceInit":
        // 设备初始化数据，更新设备管理器
        if (data.data) {
          console.log("[WebSocket] Updating devices from deviceInit:", data.data);
          updateFromWebSocket(data.data as WSDeviceInitData);
        }
        break;

      case "trainMessage":
        // 单个矿车数据更新
        if (data.data && data.data.code) {
          console.log("[WebSocket] Updating single mineTrain:", data.data);
          updateSingleDevice(data.data.code, {
            ...data.data,
            deviceType: "mineTrain" as const
          });
        }
        break;

      case "bulldozersMessage":
        // 单个挖机数据更新
        if (data.data && data.data.code) {
          console.log("[WebSocket] Updating single bulldozer:", data.data);
          updateSingleDevice(data.data.code, {
            ...data.data,
            deviceType: "bulldozer" as const
          });
        }
        break;

      case "taskUpdate":
        // 任务状态更新
        const taskStore = getStore("taskQueue");
        taskStore?.refresh();
        break;

      case "faultUpdate":
        // 故障状态更新
        const faultStore = getStore("deviceDispatchFaults");
        faultStore?.refresh();
        break;

      default:
        console.warn("[WebSocket] Unknown message type:", data.messageType);
    }
  };
  // 创建 WebSocket 连接
  const { status, data, send, open, close } = useWebSocket(
    `ws://192.168.0.41:7010/conn/webSocket?token=${userStore.token}&type=unmannedPlatform`,
    {
      autoReconnect: {
        retries: 2,
        delay: 1000,
        onFailed() {
          console.error("[WebSocket] Reconnection failed");
        }
      },
      heartbeat: {
        message: JSON.stringify({ messageType: "ping", socketType: "unmannedPlatform" }),
        interval: 30000,
        pongTimeout: 1000
      },
      onConnected() {
        console.log("[WebSocket] Connected");
        isConnected.value = true;
        sendMessage({ messageType: "deviceInit", socketType: "unmannedPlatform" });
      },
      onDisconnected() {
        console.log("[WebSocket] Disconnected");
        isConnected.value = false;
      },
      onError(error) {
        console.error("[WebSocket] Error:", error);
      },
      onMessage(ws: WebSocket, message: MessageEvent) {
        try {
          const data = JSON.parse(message.data) as WebSocketMessage;
          handleWebSocketMessage(data);
        } catch (error) {
          console.error("[WebSocket] Failed to parse message:", error);
        }
      }
    }
  );
  // 发送消息的包装函数
  const sendMessage = (message: WebSocketMessage) => {
    if (status.value === "OPEN") {
      console.log("[WebSocket] Sending message:", message);
      send(JSON.stringify(message));
    } else {
      console.warn("[WebSocket] Not connected, message not sent:", message);
    }
  };

  // 手动重连
  const reconnect = async () => {
    console.log("[WebSocket] Manual reconnection initiated");
    await close();
    open();
  };

  return {
    isConnected,
    lastHeartbeat,
    status,
    data,
    sendMessage,
    reconnect
  };
};

// 创建全局单例
useWebSocketManager();
