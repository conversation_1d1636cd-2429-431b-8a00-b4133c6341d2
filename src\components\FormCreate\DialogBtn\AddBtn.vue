<template>
  <DialogBtn ref="dialogRef" v-bind="{ ...props, btn, dialog: { closeOnClickModal: false, ...dialog } }">
    <slot />
  </DialogBtn>
</template>

<script lang="ts" setup>
import { computed, inject, ref, nextTick } from "vue";
import { Plus } from "@element-plus/icons-vue";
import DialogBtn from "./index.vue";
import { DialogBtnProps } from "./types";
import { Emitter } from "mitt";
import { Api } from "@form-create/element-ui";

const dialogRef = ref();
const mittBus = inject<Emitter<any>>("mittBus");
const props = defineProps<DialogBtnProps>();

//  定义一个函数，处理传入的 formatter 用于打开添加弹窗前处理数据
function getHandleData(row: { [key: string]: any }) {
  // 获取传入的 formatter
  const formatter = props.formCreateInject?.rule?.props?.formatter;
  // 确保 formatter 是一个函数，如果不是，给一个默认的 formatter

  if (formatter) return typeof formatter === "function" ? formatter(row) : formatter;
  return {};
}

const btn = computed(() => {
  return {
    icon: Plus,
    content: "添加",
    ...props.btn
  } as DialogBtnProps["btn"];
});
// 接收表格操作事件
mittBus?.off(`action-${props.action}`);
mittBus?.on(`action-${props.action}`, ({ row, api }: { row: { [key: string]: any }; api: Api }) => {
  dialogRef.value.toggle();
  nextTick(() => {
    let childrenApi = api.children.at(-1);
    // @ts-ignore
    const formCreate = api.children.find(item => item.config.n === props.n);
    if (formCreate) {
      childrenApi = formCreate;
    }
    // 用法如菜单管理-新增菜单
    childrenApi?.setValue(getHandleData(row));
  });
});
defineExpose({
  toggle() {
    dialogRef.value.toggle();
  }
});
</script>
