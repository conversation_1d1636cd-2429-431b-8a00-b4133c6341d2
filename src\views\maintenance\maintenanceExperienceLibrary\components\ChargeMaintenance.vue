<template>
  <form-create v-model:api="fApi" :option="option" :rule="rule"></form-create>
</template>
<script lang="tsx" setup>
/**
 * @file 维修保养-保养经验库-充电桩页
 * <AUTHOR>
 * @date 2025/2/13
 */
import { ref } from "vue";
import { TramcarColumnMap } from "../types";
import formCreate from "@form-create/element-ui";
import {
  deleteMaintenanceExperience,
  getMaintenanceCycleList,
  getMaintenanceExperienceList,
  getRepairExperienceModel,
  saveMaintenanceExperience,
  saveRepairExperience
} from "@/api/modules/repair.ts";
import LASelect from "@/components/LASelect.tsx";
import { chargeFormCreate } from "@/views/maintenance/maintenanceExperienceLibrary/components/formCreate.ts";
import { detailFormCreate } from "@/views/maintenance/maintenanceExperienceLibrary/components/detailFormCreate.ts";
const fApi = ref();
const option = {
  form: { inline: true },
  resetBtn: false,
  submitBtn: false
};

const rule = ref([
  {
    type: "SearchFormOperation",
    field: "v:search",
    wrap: { style: "marginBottom: 0" },
    style: {
      flexWrap: "noWrap"
    },
    children: [
      {
        component: LASelect,
        field: "modelNumber",
        style: { width: "200px" },
        props: {
          fetch: getRepairExperienceModel,
          params: {
            deviceType: "deviceChargingPile"
          },
          replaceFields: { key: "code", label: "name", value: "code" },
          placeholder: "奶茶车型号"
        }
      },
      {
        component: LASelect,
        field: "cycle",
        style: { width: "200px" },
        props: {
          fetch: getMaintenanceCycleList,
          params: {
            deviceType: "deviceChargingPile"
          },
          replaceFields: { key: "cycle", label: "cycle", value: "cycle" },
          placeholder: "保养间隔"
        }
      },
      {
        type: "input",
        field: "position",
        style: { width: "200px" },
        props: {
          size: "default",
          placeholder: "保养部位"
        }
      },
      // 新增
      {
        type: "AddBtn",
        slot: "suffix",
        props: {
          btn: { content: "新增", auth: "add" },
          dialog: { title: "新增保养知识" },
          size: "default",
          submitRequest: params => {
            return saveMaintenanceExperience({ ...params, deviceType: "deviceChargingPile" });
          }
        },
        children: [chargeFormCreate]
      }
    ]
  },
  {
    type: "ProTable",
    props: {
      columns: [
        {
          prop: TramcarColumnMap.get("售卖车型号名称"),
          label: "充电桩型号"
        },
        {
          prop: TramcarColumnMap.get("保养部位"),
          label: "保养部位"
        },
        {
          prop: TramcarColumnMap.get("保养间隔(天)"),
          label: "保养间隔(天)"
        },
        {
          prop: TramcarColumnMap.get("材料准备"),
          label: "材料准备"
        },
        {
          prop: TramcarColumnMap.get("维修经验"),
          label: "维修经验",
          render: () => {
            return "...";
          }
        },
        { prop: "operation", label: "操作", style: { color: "red" }, fixed: "right" }
      ],
      fetch: params => getMaintenanceExperienceList({ ...params, deviceType: "deviceChargingPile" }),

      operations: [
        { content: "详情", action: "detail" },
        { content: "修改", action: "edit", auth: "update" },
        { content: "删除", action: "delete", auth: "delete", props: { style: { color: "rgba(242, 85, 85, 1)" } } }
      ]
    },
    children: [
      // 详情组件
      {
        type: "DetailBtn",
        props: {
          action: "detail",
          dialog: {
            title: "保养知识详情",
            class: "maintenance-knowledge-detail"
          }
        },
        children: [detailFormCreate]
      },
      {
        type: "EditBtn",
        props: {
          action: "edit",
          dialog: { title: "修改维修经验" },
          submitRequest: params => saveRepairExperience({ ...params, deviceType: "deviceChargingPile" })
        },
        children: [chargeFormCreate]
      },
      {
        type: "ConfirmDialog",
        on: {
          // 监听弹窗组件抛出的的afterSubmit事件，用于刷新页面
          afterSubmit: () => {
            // 刷新，调用组件内部请求方法
            fApi.value.exec("v:search", "onSearch");
          }
        },
        props: {
          action: "delete",
          title: "是否删除保养知识",
          message: "删除后不可恢复",
          // 请求param：参数
          submitRequest: params => {
            return deleteMaintenanceExperience({
              item: params.id
            });
          }
        }
      }
    ]
  }
]);
</script>
<style lang="scss" scoped>
:deep(.el-row) {
  height: calc(100vh - 178px) !important;
}

.el-form-item {
  margin-right: 4px !important;
}
</style>
