import { defineStore } from "pinia";
import { UserState } from "@/stores/interface";
import piniaPersistConfig from "@/stores/helper/persist";

export const useUserStore = defineStore({
  id: "geeker-user",
  state: (): UserState => ({
    token: "",
    userInfo: {},
    password: ""
  }),
  getters: {},
  actions: {
    // Set Token
    setToken(token: string) {
      this.token = token;
    },
    // Set setUserInfo
    setUserInfo(userInfo: UserState["userInfo"]) {
      this.userInfo = userInfo;
    },
    setPassword(password: string) {
      this.password = password;
    },
    // 清空数据
    resetState() {
      this.token = "";
      this.userInfo = {};
      this.password = "";
    }
  },
  // 持久化储存
  persist: piniaPersistConfig("geeker-user")
});
