<template>
  <div>
    <div class="btn-container" style="display: contents" @click="openDrawer">
      <!--详情按钮插槽-->
      <slot name="button">
        <el-button v-show="button?.buttonText" type="text" v-bind="button"> {{ button?.buttonText }}</el-button>
      </slot>
    </div>
    <!--详情弹窗内容-->
    <el-drawer v-model="drawerVisible" append-to-body destroy-on-close v-bind="dialog">
      <template #title>
        <slot name="title">
          <span>{{ dialog.title }}</span>
        </slot>
      </template>
      <slot></slot>
    </el-drawer>
  </div>
</template>

<script lang="ts" setup>
/**
 * @file 详情按钮公用组件
 * <AUTHOR>
 * @date 2025/1/24
 */
import { inject, nextTick, ref } from "vue";
import type { Emitter } from "mitt";
import { Api } from "@form-create/element-ui";

const drawerVisible = ref(false);
const props = withDefaults(
  defineProps<{
    button: {
      // 按钮显示的文本
      buttonText?: string;
    };
    dialog: {
      // 弹窗标题
      title?: string;
    };
    // 详情请求
    // fetch: (p: object) => any;
    // 详情功能权限
    authTag?: string;
    formCreateInject: any;
    mittBus?: Emitter<any>;
    // 权限
    auth?: string;
    // 操作
    action?: string;
  }>(),
  {}
);

// 定义一个函数，处理传入的 formatter 用于打开编辑弹窗前处理数据
function getHandleData(data: { [key: string]: any }) {
  // 获取formatter函数
  const formatter = props.formCreateInject?.rule?.props?.formatter;
  // 确保 formatter 是一个函数，如果不是，直接返回formatter的值
  if (formatter) return typeof formatter === "function" ? formatter(data) : formatter;
  return data;
}

const mittBus = props.mittBus || inject<Emitter<any>>("mittBus");
// 接收表格操作事件
mittBus?.off(`action-${props.action}`);
mittBus?.on(`action-${props.action}`, ({ row, api }: { row: { [key: string]: any }; api: Api }) => {
  openDrawer();
  nextTick(() => {
    // 默认api的值
    api.children[0]?.setValue(getHandleData(row));
  });
});

/**
 * 打开抽屉方法
 * <AUTHOR>
 * @date 2025/2/5
 */
const openDrawer = () => {
  drawerVisible.value = true;
};
/**
 * 关闭抽屉方法
 * <AUTHOR>
 * @date 2025/2/5
 */
const closeDrawer = () => {
  drawerVisible.value = false;
};

// 抛出openDrawer方法
defineExpose({ openDrawer, closeDrawer });
</script>
<style scoped>
.dialog-footer button:first-child {
  margin-right: 10px;
}
</style>
<style lang="scss">
.el-drawer {
  .el-row {
    display: initial;
  }
}
.el-drawer__header {
  font-weight: bold;
  font-size: 20px;
  color: #1a1a1a;
}
</style>
