<template>
  <div class="search-header">
    <div class="title">{{ title }}</div>
    <slot />
  </div>
</template>

<script lang="ts" setup>
defineProps<{ title: string }>();
</script>

<style lang="scss" scoped>
.search-header {
  display: flex;
  padding: 20px;
  font-weight: bold;
  font-size: 18px;
  justify-content: space-between;
  background: #ffffff;
  color: rgba(26, 26, 26, 1);
  border-radius: 8px 8px 0 0;
  flex: 1;
}
</style>
