<template>
  <el-empty :image="currentImage" :image-size="imageSize">
    <template #description>
      <div class="text">{{ currentDescription }}</div>
    </template>
  </el-empty>
</template>

<script lang="ts" setup>
/**
 * @file 占位图组件
 * <AUTHOR>
 * @date 2025/1/21
 */
import { computed } from "vue";
import noDataIcon from "@/assets/images/noDataIcon.png";
import noPermission from "@/assets/images/noPermission.png";
const props = defineProps({
  imageSize: {
    type: Number,
    default: 60
  },
  type: {
    type: String,
    default: "noData"
  }
});
// 默认为暂无数据
const currentDescription = computed(() => {
  return props.type === "noPermission" ? "暂无权限" : "暂无数据";
});

const currentImage = computed(() => {
  return props.type === "noPermission" ? noPermission : noDataIcon;
});
</script>

<style scoped>
:deep(.el-empty__description) {
  --el-empty-description-margin-top: 6px;
}
.el-empty {
  --el-empty-padding: 0;
}
.text {
  height: 14px;
  font-weight: 400;
  font-size: 14px;
  color: #acb2c4;
  text-align: center;
  font-style: normal;
}
</style>
