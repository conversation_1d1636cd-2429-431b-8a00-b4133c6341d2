import http from "@/api";
/**
 * @name 综合管理平台-调度管理-api接口统一出口
 */
// 调度管理-调度任务-获取调度任务列表
export const getDispatchTaskList = (params: any) => {
  return http.post("/dispatch-server/dispatch/projectDetails/list", params);
};
// 调度管理-调度任务-获取任务明细
export const getDispatchTaskListByMap = (params?: any) => {
  return http.post("/dispatch-server/dispatch/task/listTaskDetailByMap", params);
};
// 调度管理-调度任务-取消任务
export const cancelDispatchTask = (params: any) => {
  return http.post("/dispatch-server/dispatch/projectDetails/cancelProjectDetails", params);
};
// 调度管理-调度任务-结束任务
export const closeDispatchTask = (params: any) => {
  return http.post("/dispatch-server/dispatch/projectDetails/closeProjectDetails", params);
};
// 调度管理-调度任务-继续任务
export const continueDispatchTask = (params: any) => {
  return http.post("/dispatch-server/dispatch/projectDetails/continueProjectDetails", params);
};
// 调度管理-调度任务-暂停任务
export const waitDispatchTask = (params: any) => {
  return http.post("/dispatch-server/dispatch/projectDetails/waitProjectDetails", params);
};
// 调度管理-调度计划-获取调度计划列表
export const getDispatchPlanList = (params: any, options: { loading: boolean } = { loading: true }) => {
  return http.post("/dispatch-server/dispatch/project/listQueryByPage", params, options);
};
// 调度管理-调度计划-获取调度计划明细
export const getDispatchPlanListByMap = (params: any) => {
  return http.post("/dispatch-server/dispatch/projectDetails/listQueryByMap", params);
};
// 调度管理-调度计划-获取所有物料
export const getAllMaterial = () => {
  return http.post("/usercenter-server/system/material/list", undefined, { cancel: false });
};
// 调度管理-调度计划-获取所有班次（当前页面需要重复请求，设置cancel）
export const getAllFlight = () => {
  return http.post("/usercenter-server/system/flights/list", undefined, { cancel: false });
};
// 调度管理-调度计划-铲点-获取所有挖机
export const getAllBulldozers = () => {
  return http.post("/wcs-server/device/bulldozers/list", undefined, { cancel: false });
};
// 调度管理-调度计划-卸点-获取所有破碎站
export const getAllCrushingStation = () => {
  return http.post("/wcs-server/device/crushingStation/list", undefined, { cancel: false });
};
// 调度管理-调度计划-开始计划
export const startDispatchPlan = (params: any) => {
  return http.post("/dispatch-server/dispatch/project/startProject", params);
};
// 调度管理-调度计划-取消计划
export const cancelDispatchPlan = (params: any) => {
  return http.post("/dispatch-server/dispatch/project/cancelProject", params);
};
// 调度管理-调度计划-保存修改计划
export const saveDispatchPlan = (params: any) => {
  return http.post("/dispatch-server/dispatch/project/saveProject", params, {
    headers: {
      "Content-Type": "application/json"
    }
  });
};
// 调度管理-调度计划-删除计划
export const deleteDispatchTask = (params: any) => {
  return http.delete("/dispatch-server/dispatch/project/deleteById", params);
};
// 调度管理-地图管理-获取地图列表
export const getDispatchMapList = (params: any) => {
  return http.post("/dispatch-server/dispatch/dispatchMap/listQueryByPage", params);
};
// 调度管理-地图管理-设置为在用
export const setDispatchMapInUse = (params: any) => {
  return http.post("/dispatch-server/dispatch/dispatchMap/setInUse", params);
};
// 调度管理-地图管理-删除地图
export const deleteDispatchMap = (params: any) => {
  return http.delete("/dispatch-server/dispatch/dispatchMap/deleteById", params);
};
// 调度管理-地图管理-上传地图文件
export const saveDispatchMap = (params: any) => {
  return http.post("/dispatch-server/dispatch/dispatchMap/saveMapFilePath", params);
};

// 调度管理-获取最新的osm地图
export const getCurrentLatestMap = () => {
  return http.post("/dispatch-server/dispatch/dispatchMap/getCurrentLatestMap", {}, { loading: false });
};

// 调度管理-地图管理-获取地图列表(不分页)
export const getDispatchMapAllList = (params?: any) => {
  return http.post("/dispatch-server/dispatch/map/list", params, {
    loading: false
  });
};
