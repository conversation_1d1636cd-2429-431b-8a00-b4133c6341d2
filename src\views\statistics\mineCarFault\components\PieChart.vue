<template>
  <div class="pie-chart">
    <div class="chart-head">
      <div class="title-wrap">
        <div class="title">{{ config.title }}</div>
        <div class="sub-title">{{ config?.subTitle || searchParams?.date }}</div>
      </div>
      <template v-if="config.filterable">
        <el-radio-group @change="onFilterChange" v-model="currentFilter" size="large">
          <el-radio-button
            v-for="item in config.filterOptions"
            :label="item.label"
            :value="item.value"
            :key="item.value"
            size="small"
          />
        </el-radio-group>
      </template>
      <div>
        <slot name="right"></slot>
      </div>
    </div>
    <div class="chart-body">
      <ECharts class="chart" :option="chartOption" resize />
      <div class="table">
        <el-table
          :data="chartData"
          row-class-name="table-row"
          cell-class-name="table-cell"
          header-cell-class-name="table-header-cell"
        >
          <el-table-column prop="name" :label="config.tableField.name">
            <template #default="scope">
              <div class="table-item-name">
                <div class="dot" :style="{ background: ThemeColors[scope.$index] }"></div>
                <div>{{ scope.row.name }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="value" :label="config.tableField.value">
            <template #default="scope">
              {{ scope.row.value?.toLocaleString() }}
            </template>
          </el-table-column>
          <el-table-column prop="ratio" align="right" :label="config.tableField.ratio" />
        </el-table>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, computed, watch, inject, type ComputedRef } from "vue";
import ECharts from "@/components/ECharts/index.vue";
import { ECOption } from "@/components/ECharts/config";
import { SearchParams } from "../index.vue";

const searchParams = inject<ComputedRef<SearchParams>>("searchParams");
const getPercentWithPrecision = inject<Function>("getPercentWithPrecision")!;
const ThemeColors = inject("ThemeColors");

const props = defineProps({
  config: { type: Object, required: true, default: () => ({}) }
});

const currentFilter = ref<string>(props.config.filterDefault);
const onFilterChange = (value: string) => {
  getData();
};
const chartData = ref([]);
const total = ref(0);
const getData = async () => {
  try {
    const params = { ...searchParams?.value };
    if (props.config.filterable && currentFilter.value !== "all") {
      params[props.config.filterField] = currentFilter.value;
    }
    delete params.dataType;
    const res = await props?.config.api(params);
    if (!res.success) return;
    const values = res.data.map(item => +item.value);
    const percent = getPercentWithPrecision(values);

    chartData.value = res.data.map((item, idx) => ({
      ...item,
      value: +item.value,
      ratio: percent[idx] + "%"
    }));

    total.value = chartData.value.reduce((acc: number, item) => acc + item.value, 0);
  } catch (error) {
    console.error("Error", error);
  }
};

watch(
  () => searchParams?.value,
  (newP, oldP) => {
    if (newP?.dataType !== oldP?.dataType) {
      currentFilter.value = props?.config.filterDefault;
    }
    newP && getData();
  },
  { deep: true }
);

const showGap = computed((): boolean => {
  return chartData.value.filter(item => item.value > 0).length > 1;
});

const chartOption = computed(() => ({
  series: [
    {
      data: chartData.value || [],
      name: props.config.title,
      type: "pie",
      radius: ["65%", "88%"],
      label: {
        show: true,
        position: "center",
        formatter: [`{a|${props.config.pieTitle || "总计"}}`, `{b|${total.value}}{c|${props.config.pieTitleUnit || ""}}`].join(
          "\n"
        ),
        rich: {
          a: {
            color: "#656666",
            fontSize: 14
          },
          b: {
            fontWeight: "bold",
            fontSize: 28,
            color: "#1a1a1a",
            lineHeight: 50
          },
          c: {
            fontWeight: "bold",
            fontSize: 14,
            color: "#1a1a1a"
          }
        }
      },
      itemStyle: {
        borderWidth: showGap.value ? 4 : 0,
        borderColor: "#fff"
      }
    }
  ],
  tooltip: {
    trigger: "item"
  }
})) as unknown as ComputedRef<ECOption>;
</script>
<style lang="scss" scoped>
.pie-chart {
  flex: 0 0 calc(50% - 5px);
  height: 330px;
  padding: 16px;
  border: 1px solid #dde2e8;
}
.chart-head {
  display: flex;
  justify-content: space-between;
  .title {
    font-size: 14px;
    font-weight: bold;
    color: #1a1a1a;
  }
  .sub-title {
    font-size: 12px;
    color: #656666;
  }
  .el-radio-group {
    align-items: flex-start;
    margin-left: -40px;
  }
}
.chart-body {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 257px;
  .chart {
    flex: 1;
    flex-shrink: 0;
  }
  .table {
    flex: 1;
    flex-shrink: 0;
    :deep(.el-table) {
      .table-header-cell {
        font-weight: normal;
        color: #1a1a1a;
        border-bottom: none;
      }
      .el-table__header th {
        height: auto;
      }
      .table-row {
        height: auto !important;
        color: #656666;
      }
      .el-table__inner-wrapper {
        &::before {
          display: none;
        }
      }
      .el-table__cell {
        padding: 0;
      }
      td.el-table__cell {
        border-bottom: none !important;
      }
      .table-item-name {
        display: flex;
        align-items: center;
        .dot {
          width: 10px;
          height: 10px;
          margin-right: 4px;
          border-radius: 50%;
        }
      }
    }
  }
}
</style>
