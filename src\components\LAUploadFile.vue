<template>
  <el-upload
    ref="upload"
    :accept="props.accept"
    :action="props.action"
    :before-upload="beforeUpload"
    :file-list="fileList"
    :headers="props.headers || header"
    :limit="props.limit"
    :multiple="true"
    :on-error="onError"
    :on-exceed="handleExceed"
    :on-success="onSuccess"
    :show-file-list="showFileList"
    v-bind="$attrs"
  >
    <template #default>
      <!--    自定义默认内容    -->
      <slot>
        <el-button v-show="props.isShow" v-auth="auth" size="default">
          <el-icon> <Upload style="transform: scale(1.2) translateX(-2px)" /> </el-icon> 上传文件
        </el-button>
      </slot>
    </template>
    <!--   提示说明文字   -->
    <template #tip>
      <slot name="tip">
        <div class="el-upload__tip">
          {{ props.tip }}
        </div>
      </slot>
    </template>
  </el-upload>
</template>

<script lang="ts" setup>
/**
 * @file 文件上传组件
 * <AUTHOR>
 * @date 2024/12/5
 */
import { ref, defineProps, defineEmits, withDefaults, watch, computed } from "vue";
import {
  ElLoading,
  ElMessage,
  // 文件预览
  ElUpload
} from "element-plus";
import { Upload } from "@element-plus/icons-vue";

import { useUserStore } from "@/stores/modules/user";
import { ResultEnum } from "@/enums/httpEnum";

const userStore = useUserStore();

// 上传文件组件实例
const upload: any = ref(null);

const props = withDefaults(
  defineProps<{
    // 文件上传请求的地址
    action?: string;
    // 上传文件的请求头
    headers?: any;
    // 已经上传的文件列表,传逗号相隔的字符串,或者是url组成的数组，传入什么类型，则返回什么类型
    modelValue?: string | string[];
    // 是否显示上传组件
    isShow: boolean;
    // 上传的文件数量最大限制,默认为3张
    limit?: number;
    // 上传文件的大小限制,单位M,默认1M
    size?: number;
    // 上传文件的格式限制
    accept?: string;
    // 上传组件下面的提示信息
    tip?: string;
    // 是否显示文件列表
    showFileList?: boolean;
    // 是否显示全屏加载
    fullscreenLoading?: boolean;
    // 上传前的钩子函数
    beforeUploadFn?: (file: any) => Promise<boolean>;
    auth?: string;
  }>(),
  // 定义prop的默认值
  {
    // action: "api/fileManage-server/upload/smallFile",
    action: "/fileManage-server/upload/smallFile",
    modelValue: "",
    showFileList: true,
    isShow: true,
    beforeUploadFn: () => Promise.resolve(true)
  }
);
const header = computed(() => {
  return { Authorization: "Bearer " + userStore.token };
});

// 定义emits事件,用于更新使用组件时的v-model绑定的值
const emits = defineEmits(["update:modelValue", "success"]);
// 全屏加载实例
const loading: any = ref(null);
// 已经上传的文件列表
const fileList: any = ref([]);

// 对外部传入的已上传文件的列表的值进行监听,把值处理到fileList中
watch(
  () => props.modelValue,
  newV => {
    // 如果值为字符串,则处理成数组,如果值不存在,则处理成空数组.如果原本就是数组,则复制一份
    let arr: any = newV ? (typeof newV === "string" ? newV.split(",") : [...newV]) : [];
    // 将处理之后的值放入fileList中,其中的每一项需要处理成{name:xx.url:xx}的形式
    if (!fileList.value.length && arr.length) {
      fileList.value.push(
        ...arr.map((url: any) => {
          let name = url;
          if (typeof url === "string" && url.includes("oldname=")) {
            name = url.split("oldname=")[1];
          }
          return { name, url: url };
        })
      );
    }
  },
  { immediate: true }
);

// 对上传列表的变化进行监听,当上传列表有变化时,通过事件抛出
watch(fileList.value, (v: any) => {
  // 处理每一项的url进行返回
  let arr = v.map(item => item.url);
  // 当modelValue传入值为字符串或null时,返回字符串,否则返回数组的形式
  emits("update:modelValue", typeof props.modelValue === "string" || props.modelValue === null ? arr.join(",") : arr);
});

/**
 * 文件上传前的钩子函数
 * @param rawFile 当前上传的文件
 * @return {boolean} 如果都符合则返回true,否则返回false停止上传
 * <AUTHOR>
 * @date 2022/5/5
 */
const beforeUpload = async rawFile => {
  // 验证文件的类型是否符合accept中规定的类型
  const types = props.accept && props.accept.split(",");
  if (types && !types.includes(rawFile.type) && !types.some(type => rawFile.name.endsWith(type))) {
    ElMessage.warning(`请上传${props.accept}格式的文件`);
    return false;
  }
  // 验证文件的大小是否超过限制
  if (props.size && rawFile.size / 1024 / 1024 > props.size) {
    ElMessage.warning(`文件大小不超过${props.size}M`);
    return false;
  }
  if (props.beforeUploadFn) {
    const res = await props.beforeUploadFn(rawFile);
    if (!res) return false;
  }
  if (props.fullscreenLoading)
    loading.value = ElLoading.service({
      lock: true,
      text: "文件上传中,请稍后...",
      spinner: "el-icon-loading",
      background: "rgba(0, 0, 0, 0.7)"
    });
  return true;
};

/**
 * 当文件上传数量超过最大的限制数量的时候触发的钩子函数
 * <AUTHOR>
 * @date 2022/5/5
 */
const handleExceed = () => {
  ElMessage.warning(`超过最大上传数量${props.limit}`);
};

/**
 * 当文件上传发生错误的时候执行的钩子函数
 * @param error 错误信息
 * @param uploadFile 当前上传的文件
 * <AUTHOR>
 * @date 2022/5/5
 */
const onError = (error, uploadFile) => {
  console.log(error, uploadFile);

  ElMessage.error(`${uploadFile.name}上传失败`);
  if (loading.value) {
    loading.value.close();
    loading.value = null;
  }
};

/**
 * 上传请求成功之后会执行的钩子函数
 * @param response 服务器响应的数据
 * @param uploadFile 当前上传的文件
 * <AUTHOR>
 * @date 2022/5/5
 */
const onSuccess = (response, uploadFile) => {
  if (loading.value) {
    loading.value.close();
    loading.value = null;
  }
  // 如果服务器返回的状态码不是101,则表明虽然请求是成功的,但是文件文件并没有上传,则把当前上传的文件删除掉
  if (response.statusCode !== ResultEnum.SUCCESS) {
    ElMessage.error(`${uploadFile.name}上传失败,${response.message}`);
    upload.value?.handleRemove?.(uploadFile);
    return;
  }
  // 上传成功之后,根据服务器响应重新定义上床文件的url和name
  ElMessage.success(`${uploadFile.name}上传成功`);
  uploadFile.url = response.data.fullPath;
  uploadFile.name = response.data.fileName;
  emits("success", uploadFile);
};
</script>

<style scoped></style>
