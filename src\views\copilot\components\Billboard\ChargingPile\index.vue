<template>
  <div class="card-container">
    <div style="display: flex; align-items: center; gap: 4px; margin: auto">
      <SvgIcon
        name="IconCharging1"
        icon-style="width:20px; height:20px"
        :color="selectedDeviceInfo?.rawData?.isActivateTheme && 'var(--el-color-primary)'"
      />
      <el-text size="large" tag="b">充电桩-{{ selectedDeviceInfo?.rawData?.name }}</el-text>
    </div>

    <div
      style="
        overflow: hidden;
        margin-top: 14px;
        padding-top: 2px;
        display: flex;
        background-color: var(--el-bg-color);
        gap: 2px;
        flex: 1;
      "
    >
      <!-- 实时故障 -->
      <TabsCard :tabs="[{ key: 'info', title: '基本信息' }]" style="flex: 1">
        <template #info>
          <!-- <el-empty image-size="0" style="height: 245px; background-color: var(--card-bg-color)" /> -->
          <el-descriptions
            :column="2"
            size="small"
            direction="vertical"
            style="width: 100%; background-color: var(--card-bg-color); margin-top: 16px"
          >
            <el-descriptions-item
              v-for="item in infos"
              :key="item.key"
              :label="item.label"
              label-class-name="description-label"
              class-name="description-item"
            >
              {{ item.value || "-" }}
            </el-descriptions-item>
          </el-descriptions>
        </template>
      </TabsCard>
      <!-- 运行日志 -->
      <TabsCard :tabs="[{ key: 'log', title: '运行日志' }]" style="flex: 1">
        <template #log>
          <el-empty image-size="0" style="height: 245px; background-color: var(--card-bg-color)" />
        </template>
      </TabsCard>
    </div>
  </div>
</template>
<script lang="ts" setup>
import TabsCard from "../../TabsCard.vue";
import SvgIcon from "@/components/SvgIcon/index.vue";
import { useDeviceSelection } from "@/views/copilot/store";
const { selectedDeviceInfo } = useDeviceSelection();
console.log(selectedDeviceInfo.value);
interface InfoItem {
  key: string;
  label: string;
  value: string | number;
}
const infos: InfoItem[] = [
  { key: "truckId", label: "连接车辆", value: "KK-05" },
  { key: "connectionStatus", label: "连接状态", value: "已连接" },
  { key: "startChargingTime", label: "开始充电时间", value: "2024-11-20 14:25:59" },
  { key: "preChargingLevel", label: "充前电量", value: "10%" },
  { key: "currentChargingLevel", label: "当前电量", value: "20%" },
  { key: "estimatedTime", label: "预计充满剩余时长", value: "1小时8分钟" },
  { key: "current", label: "电流", value: "32A" },
  { key: "voltage", label: "电压", value: "220V" }
];
</script>

<style lang="scss" scoped>
.card-container {
  flex: 1;
  display: flex;
  height: 100%;
  flex-direction: column;
  box-sizing: border-box;
  padding: 16px;
}
:deep(.description-label) {
  background: transparent !important;
  color: var(--el-text-color-secondary) !important;
  //   padding-bottom: 0 !important;
  //   padding-top: 0 !important;
}
:deep(.description-item) {
  padding-bottom: 2px !important;
}
</style>
