import { ElMessage } from "element-plus";

/**
 * @description: 校验网络请求状态码
 * @param {Number} status
 * @return void
 */
export const checkStatus = res => {
  switch (res.status) {
    case 400:
      ElMessage.error(res.message || "请求失败！请您稍后重试");
      break;
    // case 401:
    //   ElMessage.error(res.message || "当前账号无权限访问！");
    //   break;
    case 403:
      ElMessage.error(res.message || "当前账号无权限访问！");
      break;
    case 404:
      ElMessage.error(res.message || "你所访问的资源不存在！");
      break;
    case 405:
      ElMessage.error(res.message || "请求方式错误！请您稍后重试");
      break;
    case 408:
      ElMessage.error(res.message || "请求超时！请您稍后重试");
      break;
    case 500:
      ElMessage.error(res.message || "服务异常！");
      break;
    case 502:
      ElMessage.error(res.message || "网关错误！");
      break;
    case 503:
      ElMessage.error(res.message || "服务不可用！");
      break;
    case 504:
      ElMessage.error(res.message || "网关超时！");
      break;
    default:
      break;

    // ElMessage.error(res.message || "请求失败！");
  }
};
