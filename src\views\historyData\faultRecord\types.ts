/**
 * @file 历史数据-故障记录-类型声明文件
 * <AUTHOR>
 * @date 2025/2/6
 */

// table字段声明
export const columnMap: any = new Map([
  ["矿车名称", "deviceName"],
  ["故障码", "code"],
  ["故障描述", "content"],
  ["故障时间", "createDate"],
  ["结束时间", "resolutionTime"],
  ["持续时长", "faultsTime"],
  ["状态", "status"]
]);
// form字段声明
export const formMap: any = new Map([
  ["矿车名称", "deviceId"],
  ["故障码", "faultCode"],
  ["故障描述", "faultDescription"],
  ["工单状态", "workOrderStatus"],
  ["报修人", "reportPersonId"],
  ["报修人名称", "reportPersonName"],
  ["维修责任人", "repairResponsiblePersonId"],
  ["维修责任人名称", "repairResponsiblePersonName"],
  ["故障时间", "faultTime"]
]);
// 故障记录状态枚举
export enum StatusEnum {
  /** 已结束*/
  TRAIN_FAILURE = 1,
  /** 未结束*/
  TRAIN_OFFLINE = 0
}

// 矿卡故障记录状态
export const truckFaultStatusEnum = [
  {
    bg: "rgba(101, 102, 102, 0.1)",
    status: StatusEnum.TRAIN_OFFLINE,
    color: "rgba(101, 102, 102, 1)",
    text: "已结束"
  },
  {
    bg: "rgba(242, 85, 85, 0.1)",
    status: StatusEnum.TRAIN_FAILURE,
    color: "rgba(242, 85, 85, 1)",
    text: "未结束"
  }
];
