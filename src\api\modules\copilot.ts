import http from "@/api";

// ================ 设备控制接口 ================

/**
 * 一键发车
 * @param params 查询参数
 */
export const addDispatchTask = (params: any) => {
  return http.post("/wcs-server/device/mineTrain/addDispatchTask", params, { loading: false });
}; /**
 * 多条件获取设备详情信息
 * @param params 查询参数
 */
export const getDeviceDetailByMap = (params: any) => {
  return http.post("/wcs-server/device/wingSection/getDeviceDetailByMap", params, { loading: false });
};
/**
 * 获取挖机列表
 * @param params 查询参数
 */
export const getBulldozersList = (params?: any) => {
  return http.post("/wcs-server/device/bulldozers/list", params, { loading: false });
};

/**
 * 获取无人中控台设备显示信息
 * @param params 查询参数
 */
export const getDeviceWingSection = (params: any) => {
  return http.post("/wcs-server/device/wingSection/getDeviceWingSection", params, { loading: false });
};

/**
 * 远程控制矿卡开关灯(近光灯)
 * @param params 控制参数
 */
export const changeTrainLantern = (params: any) => {
  return http.post("/wcs-server/device/wingSection/changeTrainLantern", params, { loading: false });
};
/**
 * 暂停-继续矿卡
 * @param params 控制参数
 */
export const pauseOrContinueTrain = (params: any) => {
  return http.post("/wcs-server/device/mineTrain/waitContinueTrain", params, { loading: false });
};
/**
 * 矿卡回场任务
 * @param params 控制参数
 */
export const mineTrainBackTask = (params: any) => {
  return http.post("/dispatch-server/dispatch/task/addBackTask", params, { loading: false });
};
/**
 * 矿卡充电任务
 * @param params 控制参数
 */
export const mineTrainChargeTask = (params: any) => {
  return http.post("/dispatch-server/dispatch/task/addChargingTask", params, { loading: false });
};
/**
 * 一键清除故障
 * @param params 控制参数
 */
export const mineTrainClearFault = (params: any) => {
  return http.post("/dispatch-server/dispatch/faults/removeFaultsByMap", params, { loading: false });
};
/**
 * 取消矿车任务
 * @param params 控制参数
 */
export const cancelMineTrainTask = (params: any) => {
  return http.post("/dispatch-server/dispatch/task/cancelTaskByMap", params, { loading: false });
};

// ================ 紧急控制接口 ================

/**
 * 一键紧急停止矿卡
 * @param params 停止参数
 */
export const stopAllMineTrain = (params: any) => {
  return http.post("/wcs-server/device/wingSection/stopAllMineTrain", params, { loading: false });
};

/**
 * 一键恢复矿卡
 * @param params 恢复参数
 */
export const continueAllMineTrain = (params: any) => {
  return http.post("/wcs-server/device/wingSection/continueAllMineTrain", params, { loading: false });
};

/**
 * 一键收车
 * @param params 收车参数
 */
export const finishAllProject = (params: any) => {
  return http.post("/dispatch-server/dispatch/project/finshAllProject", params, { loading: false });
};

// ================ 统计数据接口 ================

/**
 * 获取今日车次统计信息
 * @param params 查询参数
 */
export const getDispatchTaskCountStatistics = (params: any) => {
  return http.post("/dispatch-server/dispatch/task/getDispatchTaskCountStatisticsToDay", params, { loading: false });
};

/**
 * 获取今日生产量统计信息
 * @param params 查询参数
 */
export const getDispatchTaskWeightStatistics = (params: any) => {
  return http.post("/dispatch-server/dispatch/task/getDispatchTaskWeightStatisticsToDay", params, { loading: false });
};

// ================ 调度管理接口 ================

/**
 * 多条件分页获取调度故障信息
 * @param params 查询参数 {current: number, size: number, ...其他查询条件}
 */
export const getDispatchFaultsList = (params: any) => {
  return http.post("/dispatch-server/dispatch/faults/listQueryByPage", params, { loading: false });
};

/**
 * 获取障碍物信息
 * @param params 查询参数 {current: number, size: number, ...其他查询条件}
 */
export const getDispatchObstacleList = (params: any) => {
  return http.post("/dispatch-server/dispatch/obstacle/list", params, { loading: false });
};

/**
 * 清除障碍物
 * @param params 清除参数
 */
export const deleteObstacle = (params: any) => {
  return http.delete("/dispatch-server/dispatch/obstacle/deleteById", params, { loading: false });
};

/**
 * 多条件分页查询调度日志信息
 * @param params 查询参数 {current: number, size: number, ...其他查询条件}
 */
export const getDispatchLogsList = (params: any) => {
  return http.post("/dispatch-server/dispatch/log/listQueryByPage", params, { loading: false });
};

// 调度管理-调度任务-获取任务明细
export const getDispatchTaskListByMap = (params?: any) => {
  return http.post("/dispatch-server/dispatch/task/listTaskDetailByMap", params, { loading: false });
};
