<template>
  <form-create v-model:api="fApi" :option="option" :rule="rule"></form-create>
</template>
<script lang="tsx" setup>
/**
 * @file 历史数据-故障记录
 * <AUTHOR>
 * @date 2025/7/16
 */
import { ref } from "vue";
import { columnMap, truckFaultStatusEnum } from "./types";
import { useUserStore } from "@/stores/modules/user";
import formCreate from "@form-create/element-ui";
import { getFaultList } from "@/api/modules/historyData.ts";
import LASelect from "@/components/LASelect.tsx";
import { getAllMaterial, getDispatchPlanList } from "@/api/modules/dispatch.ts";
import { faultRepairForm } from "@/views/historyData/faultRecord/components/formCreate.ts";
import { deviceRepairWorkOrderSave } from "@/api/modules/repair.ts";
import { getAllMineTrainList } from "@/api/modules/device.ts";
// 获取个人信息
const userInfo = useUserStore().userInfo;
const fApi = ref();
const option = {
  form: { inline: true },
  resetBtn: false,
  submitBtn: false
};

const rule = ref([
  {
    type: "SearchFormOperation",
    field: "v:search",
    wrap: { style: "marginBottom: 0" },
    children: [
      {
        component: LASelect,
        field: "deviceId",
        style: { width: "200px", lineHeight: "initial" },
        props: {
          fetch: getAllMineTrainList,
          replaceFields: { key: "id", label: "name", value: "id" },
          placeholder: "矿卡名称"
        }
      },
      {
        component: LASelect,
        field: "status",
        style: { width: "200px", lineHeight: "initial" },
        props: {
          list: [
            {
              label: "未结束",
              value: 0
            },
            {
              label: "已结束",
              value: 1
            }
          ],
          placeholder: "状态"
        }
      },
      {
        type: "LADateTimeRangePicker",
        name: "time",
        style: { lineHeight: "initial", height: "32px" },
        props: {
          type: "daterange",
          format: "YYYY-MM-DD",
          placeholder: ["起始日期", "截止日期"]
        },

        on: {
          "update:start": val => {
            if (val) {
              fApi.value.form["startTime"] = val;
            } else {
              fApi.value.form["startTime"] = undefined;
            }
          },
          "update:end": val => {
            if (val) {
              fApi.value.form["endTime"] = val;
            } else {
              fApi.value.form["endTime"] = undefined;
            }
          }
        }
      },
      {
        type: "input",
        field: "code",
        style: { width: "200px" },
        props: {
          size: "default",
          placeholder: "故障码"
        }
      }
    ]
  },
  {
    type: "ProTable",
    props: {
      columns: [
        {
          prop: columnMap.get("矿车名称"),
          label: "矿车名称"
        },
        {
          prop: columnMap.get("故障码"),
          label: "故障码"
        },

        {
          prop: columnMap.get("故障描述"),
          label: "故障描述"
        },
        {
          prop: columnMap.get("故障时间"),
          label: "故障时间"
        },
        {
          prop: columnMap.get("结束时间"),
          label: "结束时间"
        },
        {
          prop: columnMap.get("持续时长"),
          label: "持续时长",
          render: ({ row }) => {
            const hours = Math.floor(row.faultsTime / 3600000);
            const minutes = Math.floor((row.faultsTime % 3600000) / 60000);
            const seconds = Math.floor((row.faultsTime % 60000) / 1000);

            if (hours === 0 && minutes === 0) {
              return `${seconds}秒`;
            } else if (hours !== 0) {
              if (minutes === 0) {
                return `${hours}时0分${seconds}秒`;
              } else {
                return `${hours}时${minutes}分${seconds}秒`;
              }
            } else {
              return `${minutes}分${seconds}秒`;
            }
          }
        },
        {
          prop: columnMap.get("状态"),
          label: "状态",
          tag: true,
          enum: [...truckFaultStatusEnum]
        },
        { prop: "operation", label: "操作", fixed: "right" }
      ],
      fetch: getFaultList,
      operations: [
        {
          content: "报修",
          action: "update",
          onClick: row => {
            fApi.value!.findRule({ name: "FaultRecordForm" }).props.rule[0].props.data = row;
          }
        }
      ]
    },

    children: [
      // 详情组件
      {
        type: "EditBtn",
        props: {
          action: "update",
          dialog: {
            title: "故障报修",
            style: { width: "600px" },
            class: "dialog-custom-faultRecordForm"
          },
          formatter: row => {
            return {
              faultDescription: row.content,
              faultTime: row.createDate,
              deviceId: row.id,
              reportPersonId: userInfo.id,
              reportPersonName: userInfo.employeeName
            };
          },
          submitRequest: deviceRepairWorkOrderSave
        },
        children: [faultRepairForm]
      }
    ]
  }
]);
</script>
<style lang="scss" scoped>
:deep(.el-row) {
  height: calc(100vh - 138px) !important;

  .el-select--large .el-select__wrapper {
    min-height: initial;
  }
}

.el-form-item {
  margin-right: 4px !important;
}
</style>
<style lang="scss">
.dialog-custom-faultRecordForm {
  .el-form-item--large {
    margin: 20px 40px 0;
  }
}
</style>
