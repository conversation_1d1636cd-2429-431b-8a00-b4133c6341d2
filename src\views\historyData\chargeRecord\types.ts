/**
 * @file 历史数据-充电记录-类型声明文件
 * <AUTHOR>
 * @date 2025/7/16
 */

// table字段声明
export const columnMap: any = new Map([
  ["充电桩名称", "chargingPileName"],
  ["矿车名称", "deviceName"],
  ["状态", "status"],
  ["开始充电时间", "startTime"],
  ["充前电量", "beforePower"],
  ["当前电量", "afterPower"],
  ["预计充满剩余时长", "estimatedChargingTime"],
  ["结束充电时间", "estimatedChargingTime"],
  ["充后电量", "beforePower"],
  ["充电时长", "chargingTime"]
]);
// 充电任务状态枚举
export enum StatusEnum {
  /** 充电中*/
  TRAIN_CHARGING = 0,

  /** 已结束*/
  TRAIN_OFFLINE = 1
}
// 充电任务状态
export const chargingTaskStatusEnum = [
  {
    bg: "rgba(229, 251, 251)",
    status: StatusEnum.TRAIN_CHARGING,
    color: "rgba(0, 209, 217, 1)",
    text: "充电中"
  },

  {
    bg: "rgba(239, 239, 239)",
    status: StatusEnum.TRAIN_OFFLINE,
    color: "rgba(101, 102, 102, 1)",
    text: "已结束"
  }
];
