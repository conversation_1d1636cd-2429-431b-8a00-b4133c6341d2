<template>
  <div class="tool-bar-lf">
    <Tabs v-show="tabs" />
  </div>
</template>

<script lang="ts" setup>
import Tabs from "@/layouts/components/Tabs/index.vue";
import { useGlobalStore } from "@/stores/modules/global";
import { storeToRefs } from "pinia";

const globalStore = useGlobalStore();
const { tabs } = storeToRefs(globalStore);
</script>

<style lang="scss" scoped>
.tool-bar-lf {
  flex: 1;
  display: flex;
  align-self: flex-end;
  justify-content: center;
  overflow: hidden;
  white-space: nowrap;
  :deep(.el-tabs__nav-next) {
    line-height: 36px;
  }
  :deep(.el-tabs__nav-prev) {
    line-height: 36px;
  }
}
</style>
