.el-container {
  width: 100%;
  height: 100vh;

  :deep(.el-aside) {
    width: 176px;
    background-color: var(--el-menu-bg-color);
    .aside-box {
      display: flex;
      flex-direction: column;
      height: 100%;
      transition: width 0.3s ease;
      .el-scrollbar {
        height: calc(100% - 55px);
        .el-menu {
          width: 100%;
          border-right: none;
        }
      }
      .logo {
        box-sizing: border-box;
        height: 55px;
        padding: 5px;
        .logo-img {
          max-width: 90px;
          width: 100%;
          object-fit: contain;
        }
        .logo-text {
          margin-left: 6px;
          font-size: 21.5px;
          font-weight: bold;
          color: var(--el-aside-logo-text-color);
          white-space: nowrap;
        }
      }
    }
  }
  .el-header {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 44px;
    padding: 0;
    background-color: var(--el-header-bg-color);
  }
}
