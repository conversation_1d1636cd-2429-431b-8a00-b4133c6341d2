<template>
  <div class="main-content">
    <!-- 产量图表 -->
    <div class="chart-container" style="margin-top: 10px">
      <div class="statistics">
        <div class="title">今日产量(吨)</div>
        <div class="total">
          {{ Number(dispatchStatistics?.weightStats.number || 0).toLocaleString() }}
        </div>
      </div>
      <div class="charts">
        <ECharts :option="weightOption" resize class="chart" />
      </div>
    </div>
    <!-- 车次图表 -->
    <div class="chart-container">
      <div class="statistics">
        <div class="title">今日车次(次)</div>
        <div class="total">
          {{ Number(dispatchStatistics?.taskCount.number || 0).toLocaleString() }}
        </div>
      </div>
      <div class="charts">
        <ECharts :option="tripOption" resize class="chart" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, type ComputedRef } from "vue";
import ECharts from "@/components/ECharts/index.vue";
import { ECOption } from "@/components/ECharts/config";
import { useDispatchStatistics } from "@/views/copilot/request";

const { data: dispatchStatistics } = useDispatchStatistics();

// 基础配置
const baseOption: Partial<ECOption> = {
  grid: { top: 0, right: 0, bottom: 0, left: 0 },
  xAxis: {
    type: "category",
    boundaryGap: false,
    axisLine: { show: false },
    axisTick: { show: false },
    axisLabel: { show: false }
  },
  yAxis: {
    type: "value",
    splitLine: { show: false }
  },
  tooltip: {
    trigger: "axis",
    confine: true,
    axisPointer: {
      lineStyle: {
        type: "solid",
        width: 1
      }
    },
    backgroundColor: "rgba(4, 5, 54, 1)",
    borderColor: "rgba(4, 5, 54, 1)",
    padding: [0],
    textStyle: {
      color: "#fff"
    }
  }
} as const;

// 产量图表配置
const weightOption = computed(() => ({
  ...baseOption,
  xAxis: {
    ...baseOption.xAxis,
    data: dispatchStatistics.value?.weightStats.list.map(item => item.name) || []
  },
  series: [
    {
      data: dispatchStatistics.value?.weightStats.list.map(item => item.number || 0) || [],
      name: "产量",
      type: "line",
      smooth: true,
      showSymbol: false,
      lineStyle: { color: "#00D1D9" },
      areaStyle: { opacity: 0.25, color: "rgba(0, 209, 217, 1)" }
    }
  ],
  tooltip: {
    ...baseOption.tooltip,
    formatter: params => {
      const title = params[0].name;
      const value = params[0].value === undefined || params[0].value === null ? "暂无数据" : params[0].value + "吨";
      return `<div style="border-bottom: 1px solid rgba(255, 255, 255, .1);text-align: center;padding: 5px 15px">${title}</div>
              <div style="padding:5px 15px">${params[0].marker} <span>产量：${value}</span></div>`;
    }
  }
})) as unknown as ComputedRef<ECOption>;

// 车次图表配置
const tripOption = computed(() => ({
  ...baseOption,
  xAxis: {
    ...baseOption.xAxis,
    data: dispatchStatistics.value?.taskCount.list.map(item => item.name) || []
  },
  series: [
    {
      data: dispatchStatistics.value?.taskCount.list.map(item => item.number || 0) || [],
      name: "车次",
      type: "line",
      smooth: true,
      showSymbol: false,
      lineStyle: { color: "#00D1D9" },
      areaStyle: { opacity: 0.25, color: "rgba(255, 181, 71, 1)" }
    }
  ],
  tooltip: {
    ...baseOption.tooltip,
    formatter: params => {
      const title = params[0].name;
      const value = params[0].value === undefined || params[0].value === null ? "暂无数据" : params[0].value + "次";
      return `<div style="border-bottom: 1px solid rgba(255, 255, 255, .1);text-align: center;padding: 5px 15px">${title}</div>
              <div style="padding:5px 15px">${params[0].marker} <span>车次：${value}</span></div>`;
    }
  }
})) as unknown as ComputedRef<ECOption>;
</script>

<style lang="scss" scoped>
.main-content {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.chart-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .statistics {
    display: flex;
    flex-direction: column;
    gap: 10px;
    .title {
      font-weight: bold;
      font-size: 14px;
      color: #c9cad9;
    }

    .total {
      font-size: 24px;
      line-height: 30px;
      font-weight: bold;
      color: rgba(0, 209, 217, 1);
    }
  }

  .charts {
    flex: 1;
    overflow: hidden;
  }
}
</style>
