<template>
  <div class="grant-auth-dialog" @scroll="handleScroll">
    <div class="nav-container">
      <div class="nav-container-title">功能模块</div>
      <div class="nav-container-content">
        <div
          v-for="firstLevelRoute in menuTree"
          :key="firstLevelRoute.id"
          :class="{
            'nav-item': true,
            active: activeNav === firstLevelRoute.id
          }"
          @click="handleNavClick(firstLevelRoute.id)"
        >
          {{ firstLevelRoute.name }}
        </div>
      </div>
    </div>
    <div class="auth-main-container">
      <template v-for="firstLevelRoute in menuTree" :key="firstLevelRoute.id">
        <!--    一级菜单checkBox    -->
        <div :id="firstLevelRoute.id" ref="firstLevelTitleDom" class="auth-main-title">
          <el-checkbox
            v-model="firstLevelRoute.checked"
            :indeterminate="firstLevelRoute.indeterminate"
            @change="handleChildCheckedChange($event, firstLevelRoute)"
          >
            <span style="font-weight: bold">{{ firstLevelRoute.name }}</span>
          </el-checkbox>
        </div>
        <div class="route-container">
          <!--     只有一级菜单的情况下     -->
          <div v-if="firstLevelRoute.leafFlag && firstLevelRoute.children?.length" style="display: flex">
            <!--      只有一级菜单的情况下，需要填充一个一级菜单checkbox在此处，填充的checkbox与一级菜单box相同状态     -->
            <div class="first-level-route-checkbox">
              <el-checkbox
                v-model="firstLevelRoute.checked"
                :indeterminate="firstLevelRoute.indeterminate"
                @change="handleChildCheckedChange($event, firstLevelRoute)"
              >
                {{ firstLevelRoute.name }}
              </el-checkbox>
            </div>
            <!--      一级菜单下的功能权限      -->
            <div class="first-level-route-auth-container">
              <el-checkbox
                v-for="authCheckbox in firstLevelRoute.children || []"
                :key="authCheckbox.id"
                v-model="authCheckbox.checked"
                @change="handleChildCheckedChange($event, authCheckbox, firstLevelRoute)"
              >
                {{ authCheckbox.name }}
              </el-checkbox>
            </div>
          </div>

          <!--     二级菜单    -->
          <template v-if="!firstLevelRoute.leafFlag && firstLevelRoute.children?.length">
            <div v-for="secondLevelRoute in firstLevelRoute.children || []" :key="secondLevelRoute.id" class="second-level-route">
              <!--      二级菜单checkBox      -->
              <div class="second-level-route-checkbox">
                <el-checkbox
                  v-model="secondLevelRoute.checked"
                  :indeterminate="secondLevelRoute.indeterminate"
                  @change="handleChildCheckedChange($event, secondLevelRoute, firstLevelRoute)"
                >
                  {{ secondLevelRoute.name }}
                </el-checkbox>
              </div>
              <!--      二级菜单下的功能权限      -->
              <div
                v-if="secondLevelRoute.leafFlag && secondLevelRoute.children?.length"
                class="second-level-route-auth-container"
              >
                <el-checkbox
                  v-for="authCheckbox in secondLevelRoute.children || []"
                  :key="authCheckbox.id"
                  v-model="authCheckbox.checked"
                  @change="handleChildCheckedChange($event, authCheckbox, secondLevelRoute)"
                >
                  {{ authCheckbox.name }}
                </el-checkbox>
              </div>
            </div>
          </template>
        </div>
      </template>
    </div>
  </div>
</template>

<script lang="ts" setup>
/**
 * @file 系统管理-角色管理-功能权限
 * <AUTHOR>
 * @date 2024/11/8
 */
import { ref, shallowRef, watch } from "vue";
import { ElCheckbox } from "element-plus";
import { debounce } from "lodash-es";
import { fetchRoleMenusList, getMenusTreeWithFn } from "@/api/modules";
import { ResourceType } from "@/views/system/menuMange/types";

const props = defineProps({
  // 角色id
  roleId: { type: String, default: "" }
});

const emit = defineEmits(["update:modelValue"]);

interface Route {
  id: string;
  pid: string;
  // 菜单名称
  name: string;
  // 是否是叶子节点
  leafFlag?: boolean;
  // 权限的code
  menuCode?: string;
  // 该节点是否选中
  checked?: boolean;
  // 该节点是否是半选状态
  indeterminate?: boolean;
  // 该资源是菜单还是功能权限按钮
  type: string;
  children: Route[];
}
// 左侧nav当前激活的节点
const activeNav = ref("");

// 右侧一级选项的dom
const firstLevelTitleDom = shallowRef<HTMLElement[]>();
// 所有菜单以及权限资源
const menuTree = ref<Route[]>([]);
const fetchData = async () => {
  // 获取所有的菜单以及资源权限
  let { data: treeData } = await getMenusTreeWithFn();
  // 获取该角色已经授权的数据
  let { data: authData } = await fetchRoleMenusList({ roleId: props.roleId });
  // let { data: authData } = await fetchRoleMenusList;

  menuTree.value = treeData;
  activeNav.value = treeData[0]?.id || "";
  // 处理数据，回显
  formToTree(menuTree.value, authData);
};
watch(
  () => props.roleId,
  () => {
    fetchData();
    // getUserList({ roleId: props.roleId });
  },
  { immediate: true }
);

/**
 * 将已选择的数据进行回显
 * @param menusTree 菜单树资源
 * @param authData 已授权的数据资源
 * <AUTHOR>
 * @date 2022/11/17
 */
function formToTree(menusTree, authData) {
  let auth = {};
  authData.forEach(item => {
    if (item.leafFlag) {
      auth[item.resourceId] = item.resourceFunction ? item.resourceFunction.split(",") : "";
    }
  });
  // 递归处理菜单树
  function loop(menus) {
    for (let i = 0; i < menus.length; i++) {
      // 如果不是叶子菜单节点，且有children,则遍历其children
      let curMenu = menus[i];
      if (!curMenu.leafFlag && curMenu.children && curMenu.children.length) {
        loop(curMenu.children);
      }
      //  如果是叶子菜单节点，且该节点下有已选中的权限，则回显已经勾选中的选项
      if (curMenu.leafFlag && auth[curMenu.id]) {
        curMenu.children.forEach(child => {
          // 如果该权限的code在该菜单节点的已勾选权限中
          if (auth[curMenu.id].includes(child.menuCode)) {
            child.checked = true;
          }
        });
        // 回显已勾选的选项之后，更新此叶子菜单节点及父级的勾选状态
        changeParentCheckStatus(curMenu.id, curMenu);
      }
    }
  }

  loop(menusTree);
}

/**
 * 从菜单树中通过id查找菜单数据
 * @param id 被查询的id
 * @param menus 菜单树
 * <AUTHOR>
 * @date 2022/11/16
 */
function findMenuById(id: string, menus: Route[]): Route {
  for (let i = 0; i < menus.length; i++) {
    if (menus[i].id === id) return menus[i];
    if (menus[i].children?.length) {
      let res = findMenuById(id, menus[i].children);
      if (res) return res;
    }
  }
}

/**
 * 处理子级checkBox选中状态改变的函数
 * 1、如果该级别有children，则更改所有children状态为自身的状态
 * 2、如果该级别有parent，则检查parent的状态是否需要更改
 * <AUTHOR>
 * @date 2022/11/16
 * @param value 当前选中的状态
 * @param self 选中项的数据
 * @param parent 选中项的父级数据
 */
function handleChildCheckedChange(value: boolean, self: Route, parent?: Route): void {
  /*
   * 特殊情况处理,如果当前选择权限是 查看 选项，且选中状态为false，
   * 则将其父级下所有权限的checkbox的选中状态都置为false
   * */
  if (self.type === ResourceType.BUTTON && self.menuCode === "view" && !value) {
    changeChildrenCheckStatus(parent.children, value);
  }
  /*
   * 如果当前选择权限不是查看选项，且选中状态为true
   * 则将其父级下的查看权限的选中状态置为true
   * */
  if (self.type === ResourceType.BUTTON && self.menuCode !== "view" && value) {
    let viewAuth = parent?.children.find(child => child.menuCode === "view");
    viewAuth && (viewAuth.checked = true);
  }

  self.indeterminate = false;
  // 如果有子级，则更改所有子级的状态
  if (self.children && self.children.length) {
    changeChildrenCheckStatus(self.children, value);
  }
  //  如果有父级，则检查所有父级的状态是否需要更改
  if (self.pid) {
    changeParentCheckStatus(self.pid, parent);
  }
  validateForm();
}

/**
 * 递归更改子级的选中状态
 * <AUTHOR>
 * @date 2022/11/16
 * @param menus 子级菜单
 * @param checked 选中的状态
 */
function changeChildrenCheckStatus(menus: Route[], checked: boolean): void {
  menus.forEach(menu => {
    // 更改全选中状态
    menu.checked = checked;
    // 更改半选中状态
    menu.indeterminate = false;
    if (menu.children && menu.children.length) {
      changeChildrenCheckStatus(menu.children, checked);
    }
  });
}

/**
 * 倒查更改父级的选中状态
 * @param pid 父级的id，必传
 * @param parent 父级的数据，如果传了则直接使用，没有传则使用pid进行查询
 * <AUTHOR>
 * @date 2022/11/16
 */
function changeParentCheckStatus(pid: string, parent?: Route): void {
  //  如果直接传递了parent数据，则直接使用parent数据，如果没有传递则通过pid进行查询
  parent = parent || findMenuById(pid, menuTree.value);
  // children长度
  const childrenLength = parent.children.length;
  // 全选中的children长度
  let checkedChildrenLength = 0;
  // 半选中的children长度
  let halfCheckedChildrenLength = 0;
  parent.children.forEach(child => {
    child.checked && checkedChildrenLength++;
    child.indeterminate && halfCheckedChildrenLength++;
  });
  // 如果子级全选中长度等于子级长度
  parent.checked = checkedChildrenLength === childrenLength;
  // 父级的半选状态,如果全选中的长度大于0小于children长度，或者子级中有半选的状态
  parent.indeterminate = (checkedChildrenLength > 0 && checkedChildrenLength < childrenLength) || halfCheckedChildrenLength > 0;
  // 如果还有父级，则依次向上检查父级
  if (parent.pid) {
    changeParentCheckStatus(parent.pid);
  }
  // 调用方法
  validateForm();
}

/**
 * 根据菜单树的勾选情况，处理成后端需要的数据格式进行发送
 * @param menusTree 菜单树资源
 * <AUTHOR>
 * @date 2022/11/17
 */
function treeToForm(menusTree: Route[]) {
  // 存储结果,resourceId:菜单的ID， resourceFunction：菜单勾选的权限的集合  "view,update,delete"
  let resourceList: { resourceId: string; resourceFunction: string }[] = [];
  function loop(menus: Route[]) {
    menus.forEach(menu => {
      // 如果菜单资源被勾选(全选或半选)则继续处理，不勾选则不做处理
      if (menu.checked || menu.indeterminate) {
        // 如果菜单是非叶子菜单
        if (menu.type === ResourceType.MENU && !menu.leafFlag) {
          resourceList.push({ resourceId: menu.id, resourceFunction: "" });
          loop(menu.children);
        }
        // 如果是叶子菜单
        if (menu.type === ResourceType.MENU && menu.leafFlag) {
          resourceList.push({
            resourceId: menu.id,
            // 过滤出勾选的权限并返回其code的集合
            resourceFunction: menu.children
              .filter(auth => auth.checked || auth.indeterminate)
              .map(auth => auth.menuCode)
              .join(",")
          });
        }
      }
    });
  }
  loop(menusTree);
  return resourceList;
}

/**
 * 提供验证数据的方法，点击提交时通过此方法传递数据
 * <AUTHOR>
 * @date 2022/11/16
 */
const validateForm = () => {
  const resourceList = treeToForm(menuTree.value);
  // 传出验证方法
  emit("update:modelValue", resourceList);
  return Promise.resolve({ resourceList });
};
/**
 * 处理左侧导航的点击事件
 * @param id
 * <AUTHOR>
 * @date 2022/11/17
 */
function handleNavClick(id) {
  activeNav.value = id;
  document.getElementById(id).scrollIntoView({
    behavior: "smooth"
  });
}

/**
 * 容器滚动时触发的事件
 * <AUTHOR>
 * @date 2022/11/17
 */
const handleScroll = debounce(e => {
  // 如果一级菜单数量小于2个则不处理
  if (firstLevelTitleDom.value.length < 2) return;
  let titleList = firstLevelTitleDom.value || [];
  // 当前容器滚动的距离
  let scrollTop = e.target.scrollTop;
  // 遍历一级菜单列表
  for (let i = 0; i < titleList.length - 1; i++) {
    let offsetTop = titleList[i].offsetTop;
    let offsetTopNext = titleList[i + 1].offsetTop;
    if (scrollTop > offsetTop - 50 && scrollTop < offsetTopNext - 50) {
      activeNav.value = titleList[i].id;
      return;
    }
    // 如果已经滚动到最后一个位置了
    if (i === titleList.length - 2 && scrollTop > offsetTopNext - 50) {
      activeNav.value = titleList[i + 1].id;
    }
  }
}, 200);

defineExpose({
  validateForm
});
</script>
<style lang="scss" scoped>
.grant-auth-dialog {
  width: 100%;
  display: flex;
  min-height: 45vh;
  max-height: 65vh;
  line-height: normal;
  overflow: auto;
  position: relative;
  .nav-container {
    width: 200px;
    position: sticky;
    top: 0;
    flex-shrink: 0;
    border: 1px solid rgba(221, 226, 232, 1);
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    .nav-container-title {
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
      height: 50px;
      box-sizing: border-box;
      padding: 16px 16px;
      font-weight: bold;
      color: rgba(26, 26, 26, 1);
      background-color: rgba(238, 243, 255, 1);
      border-bottom: rgba(217, 220, 220, 1);
    }
    .nav-container-content {
      border-bottom-left-radius: 8px;
      border-bottom-right-radius: 8px;
      flex: 1;
      overflow-y: auto;
      overflow-x: hidden;
      padding: 16px;
      .nav-item {
        font-size: 14px;
        box-sizing: border-box;
        margin-bottom: 4px;
        height: 32px;
        width: 168px;
        padding: 5px 0 5px 20px;
        cursor: pointer;
        transition: all ease 0.3s;
        border-radius: 4px;
        &:hover {
          background: rgba(238, 243, 255, 1);
          color: rgba(86, 137, 254, 1);
        }
        &.active {
          background: rgba(238, 243, 255, 1);
          color: rgba(86, 137, 254, 1);
        }
      }
    }
  }
  .main-container {
    flex: 1;
  }
}
.el-checkbox.el-checkbox--large {
  margin-bottom: 10px;
  color: #1a1a1a;
}
.auth-main-container {
  width: 100%;
  height: 100%;
  padding: 0 16px 0 16px;
  label span {
    line-height: initial;
  }
  .auth-main-title {
    .el-checkbox {
      display: flex;
      align-items: center;
    }
  }
}
.route-container {
  //background-color: rgba(248, 251, 252, 1);
  border: 1px solid rgba(217, 220, 220, 1);
  border-radius: 4px;
  margin-bottom: 10px;
  & .second-level-route {
    display: flex;
    height: 50px;

    &:not(:last-child) {
      border-bottom: 1px solid rgba(217, 220, 220, 1);
    }
  }

  // 二级菜单checkbox
  // 填充的一级菜单checkbox
  .first-level-route-checkbox,
  .second-level-route-checkbox {
    width: 150px;
    display: flex;
    flex-shrink: 0;
    padding: 8px 12px;
    align-items: center;
    border-right: 1px solid rgba(217, 220, 220, 1);
    //height: 50px;
    .el-checkbox {
      margin-bottom: 0;
    }
  }
  //权限checkbox
  .first-level-route-auth-container,
  .second-level-route-auth-container {
    display: flex;
    flex-wrap: nowrap;
    flex: 1;
    padding: 8px 12px;
    .el-checkbox {
      margin-bottom: 0;
    }
  }
}
</style>
