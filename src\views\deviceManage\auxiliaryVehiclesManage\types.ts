/**
 * @file 设备管理-辅助车辆管理-类型声明文件
 * <AUTHOR>
 * @date 2024/11/26
 */

// table字段声明
export const columnMap: any = new Map([
  ["名称", "name"],
  ["编码", "code"],
  ["绑定流量卡", "simCardName"],
  ["类型", "type"],
  ["OBU编码", "obuCode"]
]);
// 新增/修改form字段声明
export const formMap = new Map([
  ["辅助车辆名称", "name"],
  ["辅助车辆编码", "code"],
  ["车辆类型", "type"],
  ["OBU编码", "obuCode"]
]);
// 辅助车辆类型枚举
export const auxiliaryVehicleType = new Map([
  ["WateringCar", "洒水车"],
  ["Dustcart", "垃圾车"]
]);
