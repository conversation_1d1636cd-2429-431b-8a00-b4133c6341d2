/**
 * @file 维修保养-保养经验-表单文件
 * <AUTHOR>
 * @date 2025/2/28
 */

import LASelect from "@/components/LASelect.tsx";
import { getRepairExperienceModel } from "@/api/modules/repair.ts";
import { formMap } from "@/views/maintenance/maintenanceExperienceLibrary/types.ts";
import type { Rule, Api } from "@form-create/element-ui";
import LAEditor from "@/components/LAEditor/index.vue";

// 富文本编辑器
export const EditorFormCreate = {
  type: "form-create",
  inject: true,
  props: {
    rule: [
      {
        component: LAEditor,
        field: "repairExperienceEditor"
      }
    ],
    option: {
      submitBtn: false
    }
  }
};
// 奶茶车保养知识表单
export const CoffeeCarMaintenanceFormCreate = {
  type: "form-create",
  on: {
    change: (field: string, value: any, rule: Rule, api: Api, setFlag: boolean) => {
      // console.log("change", field, value, rule, api, setFlag);
      if (field === "position") {
        api.findRule({ name: "repairExperience" }).props.dialog.title = value;
      }
      // 表单状态更改
      if (field === "repairExperience") {
        api.validateField("repairExperience");
      }
      // validateField;
    }
  },
  props: {
    rule: [
      {
        component: LASelect,
        field: formMap.get("售卖车型号"),
        title: "奶茶车型号",
        props: {
          fetch: getRepairExperienceModel,
          params: {
            deviceType: "deviceSellCar"
          },
          replaceFields: { key: "code", label: "name", value: "code" },
          placeholder: ""
        },
        validate: [{ required: true, message: "请选择奶茶车型号" }]
      },
      {
        type: "input",
        field: formMap.get("保养部位"),
        validate: [
          { required: true, message: "请输入保养部位" },
          {
            pattern: /^.{1,200}$/,
            message: "保养部位备最多200个字符"
          }
        ],
        title: "保养部位"
      },
      {
        type: "input",
        field: formMap.get("保养间隔(天)"),
        title: "保养间隔(天)",
        children: [{ type: "div", slot: "suffix", children: ["天"] }],
        validate: [
          { required: true, message: "请输入保养间隔" },
          {
            validator: (rule, value) => !/[\u4e00-\u9fa5]/.test(value),
            message: "保养间隔不能包含中文"
          }
        ]
      },
      {
        type: "input",
        field: formMap.get("材料准备"),

        validate: [
          { required: true, message: "请输入材料准备" },
          {
            pattern: /^.{1,200}$/,
            message: "材料准备最多200个字符"
          }
        ],
        title: "材料准备"
      },
      {
        type: "EditBtn",
        name: "repairExperience",
        field: formMap.get("保养方式"),
        inject: true,
        props: {
          dialog: {
            title: "",
            style: {
              width: "1200px"
            }
          },
          btn: {
            icon: "edit",
            content: "编辑",
            style: {
              color: "#666",
              width: "100%",
              border: "1px solid var(--el-border-color)",
              height: "40px"
            },
            show: true
          },
          isCustomDialog: true,
          formatter: injectApi => {
            // 回显富文本的值
            injectApi.api.findRule({ field: formMap.get("保养方式") }).children[0].props.rule[0].value =
              injectApi.api.formData().method;
          },
          submitRequest: (formCreateInject, data) => {
            // 提交时设置富文本内容
            formCreateInject.api.setValue(formMap.get("保养方式"), data.repairExperienceEditor);
            console.log(formCreateInject.api.formData());
            // 提交的请求
            return Promise.resolve(true);
          }
        },
        validate: [{ required: true, message: "请填写保养方式" }],
        title: "保养方式",
        children: [EditorFormCreate]
      }
    ],
    option: {
      submitBtn: false,
      onSubmit(formData, api) {
        // 通知 table 搜索数据变化，刷新数据
        api.top.children[0].bus.$emit("searchFormChanged");
      }
    }
  }
};
// 充电桩保养知识表单
export const chargeFormCreate = {
  type: "form-create",
  on: {
    change: (field: string, value: any, rule: Rule, api: Api, setFlag: boolean) => {
      // console.log("change", field, value, rule, api, setFlag);
      if (field === "position") {
        api.findRule({ name: "repairExperience" }).props.dialog.title = value;
      }
      // 表单状态更改
      if (field === "repairExperience") {
        api.validateField("repairExperience");
      }
      // validateField;
    }
  },
  props: {
    rule: [
      {
        component: LASelect,
        field: formMap.get("售卖车型号"),
        title: "充电桩型号",
        props: {
          fetch: getRepairExperienceModel,
          params: {
            deviceType: "deviceChargingPile"
          },
          replaceFields: { key: "code", label: "name", value: "code" },
          placeholder: ""
        },
        validate: [{ required: true, message: "请选择充电桩型号" }]
      },
      {
        type: "input",
        field: formMap.get("保养部位"),
        validate: [
          { required: true, message: "请输入保养部位" },
          {
            pattern: /^.{1,200}$/,
            message: "保养部位备最多200个字符"
          }
        ],
        title: "保养部位"
      },
      {
        type: "input",
        field: formMap.get("保养间隔(天)"),
        title: "保养间隔(天)",
        validate: [
          { required: true, message: "请输入保养间隔" },
          {
            validator: (rule, value) => !/[\u4e00-\u9fa5]/.test(value),
            message: "保养间隔不能包含中文"
          }
        ]
      },
      {
        type: "input",
        field: formMap.get("材料准备"),

        validate: [
          { required: true, message: "请输入材料准备" },
          {
            pattern: /^.{1,200}$/,
            message: "材料准备最多200个字符"
          }
        ],
        title: "材料准备"
      },
      {
        type: "EditBtn",
        name: "repairExperience",
        field: formMap.get("保养方式"),
        inject: true,
        props: {
          dialog: {
            title: "",
            style: {
              width: "1200px"
            }
          },
          btn: {
            icon: "edit",
            content: "编辑",
            style: {
              color: "#666",
              width: "100%",
              border: "1px solid var(--el-border-color)",
              height: "40px"
            },
            show: true
          },
          isCustomDialog: true,
          formatter: injectApi => {
            // injectApi.api.findRule({field: formMap.get("保养方式")}).title = injectApi.api.formData().faultDescription;
            // console.log(88, injectApi.api.findRule({field: formMap.get("保养方式")}), injectApi.api.formData());
            // 回显富文本的值
            injectApi.api.findRule({ field: formMap.get("保养方式") }).children[0].props.rule[0].value =
              injectApi.api.formData().method;
          },
          submitRequest: (formCreateInject, data) => {
            // 提交时设置富文本内容
            formCreateInject.api.setValue(formMap.get("保养方式"), data.repairExperienceEditor);
            // 提交的请求
            return Promise.resolve(true);
          }
        },
        validate: [{ required: true, message: "请填写保养方式" }],
        title: "保养方式",
        children: [EditorFormCreate]
      }
    ],
    option: {
      submitBtn: false,
      onSubmit(formData, api) {
        // 通知 table 搜索数据变化，刷新数据
        api.top.children[0].bus.$emit("searchFormChanged");
      }
    }
  }
};
