<template>
  <el-image
    :src="src || IconDefaultGoods"
    :preview-teleported="previewTeleported"
    :preview-src-list="[src]"
    :style="{ width: width, height: height, borderRadius: '12px' }"
  >
    <template #error>
      <el-image :src="IconDefaultGoods" :style="{ width: width, height: height, borderRadius: '12px' }" />
    </template>
  </el-image>
</template>

<script lang="ts" setup>
import IconDefaultGoods from "@/assets/images/IconDefaultGoods.png";

withDefaults(defineProps<{ src: string; width?: string; height?: string; previewTeleported?: boolean }>(), {
  previewTeleported: true,
  width: "80px",
  height: "80px"
});
</script>
<style>
.el-image-viewer__wrapper {
  z-index: 10001 !important;
}
</style>
