<template>
  <vc-entity :position="cartesianPosition" description="Hello VueCesium" @click="onEntityEvt">
    <vc-graphics-model ref="model" :uri="modelPath" />
  </vc-entity>
  <OverLayerHtmlLoader ref="overLayerHtmlLoader" :position="cartesianPosition" v-if="showDialog">
    <div class="model-popup__container">
      <div>速度: {{ speed }}km/h</div>
      <div>电量: {{ electricQuantity }}%</div>
      <div>模式: {{ driveMode === 0 ? "自动" : "手动" }}</div>
    </div>
  </OverLayerHtmlLoader>
</template>

<script lang="ts" setup>
import { VcEntity, VcGraphicsModel } from "vue-cesium";
import { ref, computed, onMounted } from "vue";
import OverLayerHtmlLoader from "./OverLayerHtmlLoader.vue";
import * as Cesium from "cesium";

const model = ref<typeof VcGraphicsModel>();
const overLayerHtmlLoader = ref<InstanceType<typeof OverLayerHtmlLoader>>();

const props = withDefaults(
  defineProps<{
    showDialog: boolean;
    modelPath: string;
    currentLocation: string;
    electricQuantity: number;
    speed: number;
    driveMode: 0 | 1;
    zoomTo: boolean;
  }>(),
  {
    modelPath: "https://zouyaoji.top/vue-cesium/SampleData/models/GroundVehicle/GroundVehicle.glb",
    zoomTo: true,
    showDialog: true,
    currentLocation: "",
    electricQuantity: 0,
    speed: 0,
    driveMode: 0
  }
);

const cartesianPosition = computed(() => {
  if (!props.currentLocation) return new Cesium.Cartesian3(0, 0, 0);
  const [lon, lat, height] = props.currentLocation.split(",").map(Number);
  return Cesium.Cartesian3.fromDegrees(lon, lat, height || 0);
});

const onEntityEvt = (evt: any) => {
  if (props.showDialog && overLayerHtmlLoader.value) {
    overLayerHtmlLoader.value.toggle();
  }
};

onMounted(() => {
  model.value!.creatingPromise.then(({ Cesium, viewer, cesiumObject }) => {
    if (props.zoomTo) {
      viewer.zoomTo(cesiumObject._vcParent);
    }
  });
});
</script>

<style scoped>
.model-popup__container {
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 10px;
  border-radius: 4px;
  min-width: 150px;
}
</style>
