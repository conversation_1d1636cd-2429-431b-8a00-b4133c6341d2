<template>
  <form-create v-model:api="fApi" :option="option" :rule="rule"></form-create>
</template>
<script lang="tsx" setup>
/**
 * @file 设备管理-辅助车辆管理
 * <AUTHOR>
 * @date 2024/11/26
 */
import { ref } from "vue";
import { auxiliaryVehicleType, columnMap } from "./types";
import { auxiliaryVehiclesFormCreate } from "./components/formCreate";
import formCreate from "@form-create/element-ui";
import { deleteAncillaryCar, getAncillaryCarList, saveAncillaryCar } from "@/api/modules/device";

const fApi = ref();
const option = {
  form: { inline: true },
  resetBtn: false,
  submitBtn: false
};

const rule = ref([
  {
    type: "SearchFormOperation",
    field: "v:search",
    wrap: { style: "marginBottom: 0" },
    children: [
      {
        type: "input",
        field: "search",
        props: {
          size: "default",
          placeholder: "辅助车辆名称/编码"
        }
      },
      // 新增
      {
        type: "AddBtn",
        slot: "suffix",
        props: {
          btn: { content: "新增辅助车辆" },
          dialog: {
            title: "新增辅助车辆" // 绑定到弹窗根节点的样式
          },
          size: "default",
          submitRequest: saveAncillaryCar
        },
        children: [auxiliaryVehiclesFormCreate]
      }
    ]
  },
  {
    type: "ProTable",
    props: {
      columns: [
        {
          prop: columnMap.get("名称"),
          label: "名称"
        },
        {
          prop: columnMap.get("编码"),
          label: "编码"
        },
        {
          prop: columnMap.get("类型"),
          label: "类型",
          render: data => {
            if (!data.row.type) return "";
            return auxiliaryVehicleType.get(data.row.type);
          }
        },
        {
          prop: columnMap.get("OBU编码"),
          label: "OBU编码"
        },
        {
          prop: columnMap.get("绑定流量卡"),
          label: "绑定流量卡"
        },

        { prop: "operation", label: "操作", fixed: "right" }
      ],
      fetch: getAncillaryCarList,
      operations: [
        { content: "修改", action: "edit", auth: "update" },
        { content: "删除", action: "delete", auth: "delete", props: { style: { color: "rgba(242, 85, 85, 1)" } } }
      ]
    },

    children: [
      // 修改
      {
        type: "EditBtn",
        props: {
          action: "edit",
          dialog: {
            title: "修改辅助车辆"
          },
          submitRequest: saveAncillaryCar
        },
        children: [auxiliaryVehiclesFormCreate]
      },
      // 删除
      {
        type: "ConfirmDialog",
        on: {
          // 监听弹窗组件抛出的的afterSubmit事件，用于刷新页面
          afterSubmit: () => {
            // 刷新，调用组件内部请求方法
            fApi.value.exec("v:search", "onSearch");
          }
        },
        props: {
          action: "delete",
          subtitle: row => {
            return row.name;
          },
          title: "是否删除辅助车辆",
          message: "删除后不可恢复",
          // 模拟请求param：参数
          submitRequest: deleteAncillaryCar
        }
      }
    ]
  }
]);
</script>
<style lang="scss" scoped>
:deep(.el-row) {
  height: var(--page-height);
}

.el-form-item {
  margin-right: 4px !important;
}
</style>
