<template>
  <form-create v-model:api="fApi" :option="option" :rule="rule"></form-create>
</template>

<script lang="ts" setup>
/**
 * @file 历史数据-任务记录
 * <AUTHOR>
 * @date 2025/7/16
 */
import { ref } from "vue";
import { columnMap, truckTaskStatusEnum } from "./types";
import formCreate from "@form-create/element-ui";
import { getTaskList } from "@/api/modules/historyData.ts";
import LASelect from "@/components/LASelect.tsx";
import { getAllMaterial } from "@/api/modules/dispatch.ts";
import { detailFormCreate } from "@/views/historyData/taskRecord/components/formCreate.ts";
import { getMineTrainList } from "@/api/modules/device.ts";
const fApi = ref();
const option = {
  form: { inline: true },
  resetBtn: false,
  submitBtn: false
};

const rule = ref([
  {
    type: "SearchFormOperation",
    field: "v:search",
    wrap: { style: "marginBottom: 0" },
    children: [
      {
        component: LASelect,
        field: "name",
        style: { width: "180px", lineHeight: "initial" },
        props: {
          fetch: getAllMaterial,
          replaceFields: { key: "id", label: "name", value: "id" },
          placeholder: "矿卡名称"
        }
      },
      {
        component: LASelect,
        field: "type",
        style: { width: "180px", lineHeight: "initial" },
        props: {
          fetch: getAllMaterial,
          replaceFields: { key: "id", label: "name", value: "id" },
          placeholder: "任务类型"
        }
      },
      {
        component: LASelect,
        field: "type",
        style: { width: "180px", lineHeight: "initial" },
        props: {
          fetch: getAllMaterial,
          replaceFields: { key: "id", label: "name", value: "id" },
          placeholder: "物料类型"
        }
      },
      {
        type: "LADateTimeRangePicker",
        name: "time",
        style: { lineHeight: "initial", height: "32px" },
        props: {
          type: "daterange",
          format: "YYYY-MM-DD",
          placeholder: ["起始日期", "截止日期"]
        },

        on: {
          "update:start": val => {
            if (val) {
              fApi.value.form["startTime"] = val;
            } else {
              fApi.value.form["startTime"] = undefined;
            }
          },
          "update:end": val => {
            if (val) {
              fApi.value.form["endTime"] = val;
            } else {
              fApi.value.form["endTime"] = undefined;
            }
          }
        }
      },
      {
        component: LASelect,
        field: "type",
        style: { width: "180px", lineHeight: "initial" },
        props: {
          fetch: getAllMaterial,
          replaceFields: { key: "id", label: "name", value: "id" },
          placeholder: "铲点"
        }
      },
      {
        component: LASelect,
        field: "type",
        style: { width: "180px", lineHeight: "initial" },
        props: {
          fetch: getAllMaterial,
          replaceFields: { key: "id", label: "name", value: "id" },
          placeholder: "卸点"
        }
      },
      {
        type: "input",
        field: "search",
        props: {
          size: "default",
          placeholder: "计划编号"
        }
      }
    ]
  },
  {
    type: "ProTable",
    props: {
      columns: [
        {
          prop: columnMap.get("矿车名称"),
          label: "矿车名称"
        },
        {
          prop: columnMap.get("任务类型"),
          label: "任务类型"
        },
        {
          prop: columnMap.get("物料类型"),
          label: "物料类型"
        },
        {
          prop: columnMap.get("铲点"),
          label: "铲点"
        },
        {
          prop: columnMap.get("挖机师傅"),
          label: "挖机师傅"
        },
        {
          prop: columnMap.get("地磅"),
          label: "地磅"
        },
        {
          prop: columnMap.get("卸点"),
          label: "卸点"
        },
        {
          prop: columnMap.get("起点时间"),
          label: "起点时间"
        },
        {
          prop: columnMap.get("完成时间"),
          label: "完成时间"
        },
        {
          prop: columnMap.get("重量"),
          label: "重量(t)"
        },
        {
          prop: columnMap.get("里程"),
          label: "里程(km)"
        },
        {
          prop: columnMap.get("计划编号"),
          label: "计划编号"
        },
        { prop: "operation", label: "操作", fixed: "right" }
      ],
      // fetch: getTaskList,
      fetch: getMineTrainList,
      operations: [{ content: "详情", action: "detail" }]
    },
    children: [
      // 详情组件
      {
        type: "DetailBtn",
        props: {
          action: "detail",
          dialog: {
            title: "任务详情",
            class: "task-record-detail"
          }
        },
        children: [detailFormCreate]
      }
    ]
  }
]);
</script>

<style lang="scss" scoped>
:deep(.el-row) {
  height: calc(100vh - 138px) !important;
  .el-select--large .el-select__wrapper {
    min-height: initial;
  }
}

.el-form-item {
  margin-right: 4px !important;
}
</style>
<style lang="scss">
.task-record-detail {
  width: 400px !important;
  .el-form {
    height: 100%;
    .el-row {
      display: inherit;
      height: 100%;
    }
  }
}
</style>
