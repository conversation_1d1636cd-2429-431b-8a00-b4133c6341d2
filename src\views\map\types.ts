/**
 * 矿卡数据消息接口定义
 */
export interface TruckMessage {
  /** 当前位置 */
  currentLocation: string;

  /** 矿卡电量 */
  electricQuantity: number;

  /** 油门 */
  gasPedal: number;

  /** 是否载重 */
  loadMode: boolean;

  /** 减速度 */
  deceleration: number;

  /** 档位 */
  gearLevel: number;

  /** 速度 */
  speed: number;

  /** 充电状态(0:否,1:是) */
  chargingFlag: 0 | 1;

  /** 驾驶模式(0:自动,1:手动) */
  driveMode: 0 | 1;

  /** 转向角度 */
  redirect: number;

  /** 举升角度 */
  liftNumber: number;

  /** 近光灯(0:关闭,1:开启) */
  lowBeam: 0 | 1;

  /** 远光灯(0:关闭,1:开启) */
  highBeam: 0 | 1;

  /** 手刹(0:关闭,1:开启) */
  handbrake: 0 | 1;

  /** 转向灯(0:关闭,1:左转向灯开启,2:右转向灯开启) */
  turnSignalLamp: 0 | 1 | 2;

  /** 雾灯(0:关闭,1:开启) */
  fogLamp: 0 | 1;
}

export interface TruckChannelMessage {
  type: "trainMessage";
  deviceId: string;
  data: TruckMessage;
}
