/**
 * @file 设备管理-摄像头管理-表单创建文件
 * <AUTHOR>
 * @date 2024/11/18
 */
import { formMap } from "../types";

export const cameraMangeFormCreate = {
  type: "form-create",
  props: {
    option: {
      row: { gutter: 24 },
      global: { "*": { col: { span: 12 } } },
      submitBtn: false,
      onSubmit(formData, api) {
        console.log("formData", formData);
        // 通知 table 搜索数据变化，刷新数据
        api.top.bus.$emit("searchFormChanged");
      }
      // wrap: {
      //   labelPosition: "top"
      // }
    },
    rule: [
      {
        type: "input",
        field: formMap.get("名称"),
        title: "摄像头名称",
        validate: [
          { required: true, message: "请输入摄像头名称" },
          {
            pattern: /^.{1,20}$/,
            message: "字符限长20位"
          }
        ]
      },
      {
        type: "input",
        field: formMap.get("编码"),
        title: "编码",
        validate: [{ required: true, message: "请输入编码" }]
      },
      {
        type: "input",
        field: formMap.get("x坐标"),
        title: "x坐标"
      },
      {
        type: "input",
        field: formMap.get("y坐标"),
        title: "y坐标"
      },
      {
        type: "input",
        field: formMap.get("z坐标"),
        title: "z坐标"
      },
      {
        type: "input",
        field: formMap.get("朝向"),
        title: "朝向"
      },
      {
        type: "input",
        field: formMap.get("访问地址"),
        title: "访问地址"
      },
      {
        type: "input",
        field: formMap.get("登录账号"),
        title: "登录账号"
      },
      {
        type: "input",
        field: formMap.get("登录密码"),
        title: "登录密码"
      }
    ]
  }
};
