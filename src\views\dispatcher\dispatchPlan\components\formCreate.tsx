import LASelect from "@/components/LASelect";
import { formMap } from "../types";
import PlanStartConfirm from "@/views/dispatcher/dispatchPlan/components/PlanStartConfirm.vue";
import formCreate from "@form-create/element-ui";
import { getAllBulldozers, getAllCrushingStation, getAllFlight, getAllMaterial } from "@/api/modules/dispatch";
import { dayjs } from "element-plus";
import { computed, ref, VNode } from "vue";

formCreate.component("PlanStartConfirm", PlanStartConfirm);
// 设置日期禁用时间
const startDisabledDate = (api, time: Date) => {
  const projectEndTimeValue = api.api.getValue("projectEndTime");
  if (projectEndTimeValue) {
    const projectEndTime = dayjs(projectEndTimeValue);
    return time.getTime() > projectEndTime.valueOf();
  }
};
// 储存数据用于验证表单
const planData: any = ref();
// 保存已选数据用于禁用下拉,防止再次选择
const selectedData = ref<string[]>([]);
// 新增计划表单
export const planFormCreate = {
  type: "form-create",
  props: {
    option: {
      row: { gutter: 20 },
      global: { "*": { col: { span: 12 } } },
      submitBtn: false,
      onSubmit(formData, api) {
        // 通知 table 搜索数据变化，刷新数据
        api.top.bus.$emit("searchFormChanged");
      },
      wrap: {
        labelPosition: "top"
      }
    },
    rule: [
      {
        type: "input",
        field: formMap.get("计划编号"),
        title: "计划编号",
        props: { placeholder: "保存时自动生成", disabled: true }
      },
      {
        type: "select",
        field: formMap.get("任务类型"),
        title: "任务类型",
        value: "LoadingTasks",
        options: [{ value: "LoadingTasks", label: "装卸" }],
        validate: [{ required: true, message: "请选择任务类型" }]
      },
      {
        component: LASelect,
        field: formMap.get("班次"),
        props: {
          replaceFields: { key: "id", label: "name", value: "id" },
          fetch: getAllFlight
        },
        on: {
          change: (val, option, api) => {
            // option.startTime="10:30:00" 获取当天年月日为例如2030-12-11拼接option.startTime 赋值给projectStartTime
            api.api.setValue("projectStartTime", dayjs().format("YYYY-MM-DD") + " " + option.startTime);
            // 如果夜班的时间21:00:00-次日04:00:00，则结束时间加一天
            if (option.startTime > option.endTime) {
              api.api.setValue("projectEndTime", dayjs().add(1, "day").format("YYYY-MM-DD") + " " + option.endTime);
            } else {
              api.api.setValue("projectEndTime", dayjs().format("YYYY-MM-DD") + " " + option.endTime);
            }
            // 刷新验证状态
            api.api.validate();
          }
        },
        title: "班次",
        validate: [{ required: true, message: "请选择班次" }]
      },
      {
        type: "ElDatePicker",
        field: formMap.get("计划开始时间"),
        title: "计划开始时间",
        value: "",
        inject: true,
        props: {
          disabledDate: startDisabledDate,
          type: "datetime",
          placeholder: "请选择任务时间",
          format: "YYYY-MM-DD HH:mm:ss"
        },
        validate: [
          {
            required: true,
            validator(rule, value, callback) {
              // 判断当前计划开始时间value="2024-12-18 12:00:00"是否小于计划结束时间planData.value.projectEndTime 小于通过大于则触发验证
              if (value) {
                if (planData.value.projectEndTime && dayjs(value) >= dayjs(planData.value.projectEndTime)) {
                  callback("计划开始时间不能大于计划结束时间");
                } else {
                  callback();
                }
              } else {
                callback("请选择计划开始时间");
              }
            }
          }
        ]
      },
      {
        type: "DatePicker",
        field: formMap.get("计划结束时间"),
        title: "计划结束时间",
        value: "",
        // 注入inject,在方法中第一个参数会变成api
        inject: true,
        on: {
          change: (api, val) => {
            // 触发验证开始时间
            api.api.validateField("projectStartTime");
          }
        },
        props: {
          type: "datetime",
          placeholder: "请选择任务时间",
          format: "YYYY-MM-DD HH:mm"
        },
        validate: [{ required: true, message: "请选择班次" }]
      },
      {
        type: "group",
        field: "dataInfos",
        col: { span: 24 },
        inject: true,
        update: val => {
          // 取val数组里有值的loadingPoint放入selectedData中
          selectedData.value = val
            ?.filter(item => item.loadingPoint)
            .map(item => item.loadingPoint)
            .flat();
        },
        props: {
          min: 1,
          rule: [
            {
              col: { span: 8 },
              component: LASelect,
              field: formMap.get("铲点"),
              title: "铲点",
              props: {
                replaceFields: { key: "id", label: "name", value: "id" },
                multiple: true,
                afterChangeFetch: computed(() => {
                  return selectedData.value?.length;
                }),
                fetch: getAllBulldozers,
                filterFetch: (row, val, list, api) => {
                  // console.log("表单数据", api);
                  // 获取selectedData.value是数组=>['2323','234'],过滤条件为在selectedData.value里不是val里的值
                  // 过滤 list，条件是 selectedDataValues 中存在，但不在 val 中
                  // const filteredList = list.filter(item => selectedData.value.includes(item));
                  // 手动过滤 list，条件是 selectedDataValues 中存在，但不在 val 中
                  const filteredList: any = [];
                  for (let i = 0; i < list.length; i++) {
                    const item: any = list[i];
                    if (!selectedData.value.includes(item.id) || val?.includes(item.id)) {
                      filteredList.push(item);
                    }
                  }
                  return filteredList;
                },
                /**
                 * <AUTHOR>
                 * @date 2025/1/14
                 * @description LASelect处理数据的方法
                 * @param {Object} row 当前行的数据
                 * @param {String} label 当前输入的值
                 * @param {String} replaceFieldsLabel 当前下拉列表的数据
                 * @return {VNode | string} 处理后的数据
                 */
                renderLabel: (row: object, label: string, replaceFieldsLabel: string): VNode => {
                  return <div>挖机-{label}</div>;
                }
              },
              validate: [{ required: true, message: "请选择铲点" }]
            },
            {
              col: { span: 8 },
              component: LASelect,
              field: formMap.get("卸点"),
              props: {
                replaceFields: { key: "id", label: "name", value: "id" },
                multiple: true,
                fetch: getAllCrushingStation,
                renderLabel: (row: object, label: string, replaceFieldsLabel: string): VNode => {
                  return <div>破碎站-{label}</div>;
                }
              },
              title: "卸点",
              validate: [{ required: true, message: "请选择卸点" }]
            },
            {
              col: { span: 8 },
              component: LASelect,
              field: formMap.get("物料类型"),
              props: {
                replaceFields: { key: "id", label: "name", value: "id" },
                fetch: getAllMaterial
              },
              title: "物料类型",
              validate: [{ required: true, message: "请选择物料类型" }]
            }
          ]
        }
      }
    ]
  },
  on: {
    "update:modelValue"(val) {
      planData.value = val;
    }
  }
};
// 开始计划表单
export const startPlanForm = {
  type: "form-create",
  props: {
    option: {
      submitBtn: false,
      onSubmit(formData, api) {
        // 通知 table 搜索数据变化，刷新数据
        api.top.bus.$emit("searchFormChanged");
      }
    },
    rule: [
      {
        type: "PlanStartConfirm",
        props: {
          data: {}
        }
      }
    ]
  }
};
