/**
 * @file 设备管理-停车位管理-类型声明文件
 * <AUTHOR>
 * @date 2024/11/19
 */

// table字段声明
export const columnMap: any = new Map([
  ["名称", "name"],
  ["编码", "code"],
  ["经度", "lon"],
  ["纬度", "lat"],
  ["海拔", "ele"],
  ["对应充电桩", "chargingPileName"],
  ["朝向", "orientations"],
  ["状态", "status"]
]);
// 新增/修改form字段声明
export const formMap = new Map([
  ["停车位名称", "name"],
  ["停车位编码", "code"],
  ["对应充电桩", "chargingPileId"],
  ["经度", "lon"],
  ["纬度", "lat"],
  ["海拔", "ele"],
  ["朝向", "orientations"]
]);
export enum Status {
  // 未占用
  PARK = 0,
  // 已占用
  NOPARK = 1
}

export const statusEnum = [
  {
    bg: "rgba(0, 194, 144, 0.1)",
    status: Status.PARK,
    color: "rgba(0, 194, 144, 1)",
    text: "未占用"
  },
  {
    bg: "rgba(53, 106, 253, 0.1)",
    status: Status.NOPARK,
    color: "rgba(53, 106, 253, 1)",
    text: "已占用"
  }
];
