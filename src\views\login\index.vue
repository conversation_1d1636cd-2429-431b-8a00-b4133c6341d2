<template>
  <div class="login-container">
    <div class="login-box">
      <div style="position: fixed; left: 60px; top: 40px">
        <img alt="logo" src="./image/logo.png" style="width: 200px" />
        <div style="color: white; margin-top: 10px; font-weight: bold; font-size: 36px">蓝奥无人矿车调度系统</div>
      </div>
      <div style="position: fixed; left: 60px; bottom: 80px; font-size: 20px; color: white">
        驾驭未来矿业，无人矿卡，智领高效开采。
      </div>
      <div :style="{ background: imageUrl }" class="img-container"></div>
      <div class="form-container">
        <!--    登录表单组件    -->
        <LoginForm v-model:oldPassword="oldPassword" v-model:visible="dialogVisible" />
      </div>
    </div>
    <!--  重置密码框  -->
    <ResetPassword v-model:visible="dialogVisible" :old-password="oldPassword" />
  </div>
</template>
<script lang="ts" name="login" setup>
import LoginForm from "./component/LoginForm.vue";
import { ref } from "vue";
import ResetPassword from "@/views/login/component/ResetPassword.vue";
const imageUrl = "";
// 控制密码重置框的显示
const dialogVisible = ref(false);
// 旧密码,如果需要重置密码,此值会由登录表单传递出来,然后传递给重置密码弹窗组件
const oldPassword = ref("");
// const imageUrl = ref(`url(${loginImage}) no-repeat left top/cover`);
</script>

<style lang="scss" scoped>
@import "./index.scss";
</style>
