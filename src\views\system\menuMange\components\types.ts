/**
 * @file 系统管理-菜单管理-新增编辑表单 字段定义
 * <AUTHOR>
 * @date 2024/11/18
 */

// 资源类型
export enum ResourceType {
  /* 菜单*/
  MENU = "ResourceMenu",
  BUTTON = "ResourceButton"
}
// 资源类型名称
export const ResourceTypeName = {
  [ResourceType.MENU]: "菜单",
  [ResourceType.BUTTON]: "按钮"
};

// 详情字段
export const detailMap = new Map([
  ["名称", "name"],
  ["类型", "type"],
  ["上级菜单", "pname"],
  ["icon", "image"],
  ["路径", "url"],
  ["序号", "sort"]
]);
