<template>
  <form-create v-model:api="formApi" :option="option" :rule="rule" />
</template>
<script lang="tsx" setup>
/**
 * @file 调度管理-调度计划
 * <AUTHOR>
 * @date 2024/12/9
 */
import { ref } from "vue";
import { Api } from "@form-create/element-ui";
import formCreate from "@form-create/element-ui";
import { planFormCreate, startPlanForm } from "./components/formCreate.tsx";
import { columnMap, detailColumnMap, PlanStatusEnum, PlanStatusEnumMap } from "@/views/dispatcher/dispatchPlan/types";
import {
  getDispatchPlanList,
  startDispatchPlan,
  saveDispatchPlan,
  deleteDispatchTask,
  getDispatchPlanListByMap
} from "@/api/modules/dispatch";
import { TaskStatusEnum } from "@/views/dispatcher/dispatchTask/types";
import NoData from "./components/NoData.vue";

// 表格组件
// 激活项
const option = {
  resetBtn: false,
  submitBtn: false,
  form: { inline: true }
};
const formApi = ref<Api>();
const rule = ref([
  {
    type: "SearchFormOperation",
    field: "v:search",
    style: { "flex-wrap": "nowrap" },
    wrap: { style: "marginBottom: 0" },
    children: [
      {
        type: "input",
        field: "search",
        props: {
          size: "default",
          placeholder: "计划编号"
        }
      },
      {
        type: "AddBtn",
        slot: "suffix",
        props: {
          btn: { content: "新增计划", auth: "add" },
          action: "add",
          dialog: { title: "新增调度计划", style: { width: "1080px" }, class: "dialog-custom-plan" },
          size: "default",
          isCustomDialog: true,
          formatter: (): any => {
            // 初始化一个默认项
            return {
              dataInfos: [{ loadingPoint: "", unloadingPoint: "", materialId: "" }]
            };
          },
          submitRequest: params => {
            const newParams = {
              ...params,
              dataInfos: params.dataInfos.map(item => {
                return {
                  ...item,
                  loadingPoint: item.loadingPoint.join(","),
                  unloadingPoint: item.unloadingPoint.join(",")
                };
              })
            };
            return saveDispatchPlan(newParams);
          }
        },
        children: [planFormCreate]
      }
    ],
    on: {
      // 重置时清除选中，隐藏数据
      reset: () => {
        const api = formApi.value!;
        // 获取到表格实例设置选中为空
        api.el("v:table").element.setCurrentRow();
        api.hidden(false, "noData");
        api.hidden(true, "sub-table");
        const subTableTitle = api.findRule({ name: "sub-table-title" });
        // 修改明细标题
        subTableTitle.children[0] = "计划明细";
      }
    }
  },
  {
    type: "ProTable",
    field: "v:table",
    wrap: {
      style: { height: "46%", margin: 0 }
    },
    props: {
      highlightCurrentRow: true,
      columns: [
        { prop: columnMap.get("计划编号"), label: "计划编号" },
        { prop: columnMap.get("任务类型名称"), label: "任务类型" },
        { prop: columnMap.get("班次名称"), label: "班次" },
        {
          prop: detailColumnMap.get("状态"),
          label: "状态",
          tag: true,
          enum: [...PlanStatusEnumMap]
        },
        { prop: columnMap.get("计划开始时间"), label: "计划开始时间" },
        { prop: columnMap.get("计划结束时间"), label: "计划结束时间" },
        { prop: columnMap.get("地磅"), label: "地磅" },
        { prop: "operation", label: "操作", fixed: "right" }
      ],
      operationDisabled: row => {
        return row.status !== PlanStatusEnum.PROJECT_PENDING_EXECUTION;
      },
      operations: [
        {
          content: "修改",
          action: "edit",
          auth: "update"
        },
        {
          content: "开始计划",
          action: "start",
          auth: "update",
          onClick: row => {
            formApi.value!.findRule({ name: "PlanStartForm" }).children[0].props.rule[0].props.data = row;
          }
        },
        {
          content: "删除计划",
          action: "delete",
          auth: "delete",
          props: { type: "danger" }
        }
      ],
      fetch: getDispatchPlanList
    },
    children: [
      {
        type: "EditBtn",
        props: {
          action: "edit",
          formatter: (data: any) => {
            return {
              ...data,
              dataInfos: JSON.parse(data.dataInfo).map(item => {
                return {
                  ...item,
                  loadingPoint: item.loadingPoint.split(","),
                  unloadingPoint: item.unloadingPoint.split(",")
                };
              })
            };
          },
          dialog: { title: "修改调度计划", style: { width: "1080px" }, class: "dialog-custom-plan" },
          submitRequest: (params: any) => {
            const newParams = {
              ...params,
              dataInfos: params.dataInfos.map(item => {
                return {
                  ...item,
                  loadingPoint: item.loadingPoint.join(","),
                  unloadingPoint: item.unloadingPoint.join(",")
                };
              })
            };
            return saveDispatchPlan(newParams);
          }
        },
        children: [planFormCreate]
      },
      {
        type: "EditBtn",
        name: "PlanStartForm",
        props: {
          action: "start",
          dialog: {
            title: "开始计划",
            style: { width: "600px" },
            class: "dialog-custom-startForm"
          },
          submitRequest: param => startDispatchPlan({ projectId: param.id })
        },
        children: [startPlanForm]
      },
      {
        type: "ConfirmDialog",
        on: {
          // 监听弹窗组件抛出的的afterSubmit事件，用于刷新页面
          afterSubmit: () => {
            // 刷新，调用组件内部请求方法
            formApi.value!.exec("v:search", "onSearch");
          }
        },
        props: {
          title: "是否删除计划",
          message: "未开始的调度计划删除后不可恢复",
          action: "delete",
          // 模拟请求param：参数
          submitRequest: param => {
            return deleteDispatchTask({ id: param.id });
          }
        }
      }
    ],
    on: {
      currentChange: row => {
        const api = formApi.value!;
        const subTable = api.findRule({ name: "sub-table" });
        const subTableTitle = api.findRule({ name: "sub-table-title" });
        if (row) {
          api.hidden(true, "noData");
          api.hidden(false, "sub-table");
          api.nextTick(() => {
            // 渲染完成后的操作
            // 修改明细标题
            subTableTitle.children[0] = `计划明细${row?.projectNumber ? "-" + row?.projectNumber : ""}`;
            subTable.props.params = { projectId: row?.id };
          });
        } else {
          api.hidden(false, "noData");
          api.hidden(true, "sub-table");
          // 修改明细标题
          subTableTitle.children[0] = "计划明细";
        }

        // console.log("subTable", subTable);
      }
    }
  },
  {
    component: NoData,
    wrap: {
      style: {
        flex: 1,
        margin: "4px 0 0 0"
      }
    },
    field: "noData",
    hidden: false
  },
  {
    type: "ProTable",
    name: "sub-table",
    hidden: true,
    style: {
      flex: 1,
      marginTop: "4px"
    },
    props: {
      // 自定义处理params的方法,消除上方空search
      paramsFormatter: params => {
        return { projectId: params?.projectId };
      },
      // 禁用自动请求
      requestAuto: false,
      params: {},
      pagination: false,
      columns: [
        {
          label: "编号",
          prop: detailColumnMap.get("编号")
        },
        {
          prop: detailColumnMap.get("状态"),
          label: "状态",
          tag: true,
          enum: [...TaskStatusEnum]
        },
        { prop: detailColumnMap.get("铲点"), label: "铲点" },
        { prop: detailColumnMap.get("卸点"), label: "卸点" },
        { prop: detailColumnMap.get("物料类型"), label: "物料类型" },
        { prop: detailColumnMap.get("开始时间"), label: "开始时间" }
      ],
      fetch: getDispatchPlanListByMap
    },
    children: [
      {
        type: "div",
        slot: "headerTable",
        name: "sub-table-title",
        // field: "sub-table-title",
        children: ["计划明细"],
        style: {
          padding: "10px 10px 0 20px",
          fontWeight: "bold",
          "border-top-left-radius": "10px",
          "border-top-right-radius": "10px",
          width: "100%",
          fontSize: "18px",
          backgroundColor: "#fff",
          color: "#303133"
        }
      }
    ]
  }
]);
</script>
<style lang="scss" scoped>
:deep(.el-row) {
  flex-flow: column nowrap;
  justify-content: space-between;
  height: 100%;
  overflow: hidden;

  //.table-box {
  //  height: 45%;
  //}
  //.el-form-item__content {
  //  width: 100%;
  //}
  .table-main {
    flex: 1;
  }
}
</style>
<style lang="scss">
.dialog-custom-plan .el-dialog__body {
  max-height: calc(100vh - 300px);
  padding-top: 0;
  overflow: hidden;
  overflow-y: scroll;
  .el-col-12 {
    .el-form-item--label-top {
      margin-bottom: 0;
    }
    label {
      height: 30px;
      line-height: 0 !important;
      span {
        line-height: 50px;
      }
    }
  }
  ._fc-group-container {
    padding: 20px 20px 22px;
    .el-form-item {
      margin-bottom: 0 !important;
    }
    .el-form-item__label {
      margin-bottom: 10px !important;
    }
    .el-form-item--label-top {
      height: inherit;
      line-height: 0;
      span {
        line-height: normal;
      }
    }
  }

  //隐藏收起箭头
  ._fc-group-arrow {
    display: none;
  }

  //任务组样式
  ._fc-group-container {
    margin: 25px 0 0;
    background: #f5f6f8;
    border: none;
  }
  ._fc-group-handle {
    padding: 3px;
    border: 1px solid #d9d9d9;
  }

  //任务分组数字小标
  ._fc-group-idx {
    display: none;
  }
  .el-date-editor {
    width: 100%;
  }
}
</style>
