<template>
  <div :class="['table-box', props.tableClass]" :style="$attrs.style as string">
    <!-- 查询表单 -->
    <el-config-provider size="default">
      <!-- 表格主体 -->
      <div :class="{ 'no-padding': true }" class="table-main">
        <!-- 默认插槽 -->
        <slot name="headerTable" />
        <!-- 表格主体 -->
        <el-table
          :id="uuid"
          ref="tableRef"
          :border="border"
          :data="processTableData"
          :row-key="rowKey"
          v-bind="$attrs"
          @selection-change="selectionChange"
        >
          <!-- 默认插槽 -->
          <slot />
          <template v-for="item in tableColumns" :key="item">
            <!-- selection || radio || index || expand || sort -->
            <el-table-column
              v-if="item.type && columnTypes.includes(item.type)"
              :align="item.align ?? 'left'"
              :reserve-selection="item.type == 'selection'"
              v-bind="item"
            />
            <el-table-column v-else-if="item.prop === 'operation'" :align="item.align ?? 'left'" v-bind="item">
              <template #default="scope">
                <Operation
                  :disabled="operationDisabled?.(scope.row)"
                  :form-create-inject="formCreateInject"
                  :mitt-bus="mittBus"
                  :operations="typeof operations === 'function' ? operations(scope.row) : operations"
                  :row="scope.row"
                />
              </template>
            </el-table-column>

            <!-- other -->
            <TableColumn v-else :column="item">
              <template v-for="slot in Object.keys(slots)" #[slot]="scope">
                <slot :name="slot" v-bind="scope" />
              </template>
            </TableColumn>
          </template>
          <!-- 无数据 -->
          <template #empty>
            <div class="table-empty">
              <slot name="empty">
                <img alt="notData" src="@/assets/images/noData.png" />
              </slot>
            </div>
          </template>
        </el-table>
        <!-- 分页组件 -->
        <slot name="pagination">
          <Pagination
            v-if="pagination"
            :handle-current-change="handleCurrentChange"
            :handle-size-change="handleSizeChange"
            :pageable="pageable"
          />
        </slot>
      </div>
    </el-config-provider>
  </div>
  <slot />
</template>

<script lang="ts" setup>
import { ref, watch, provide, onMounted, unref, computed, reactive, useSlots } from "vue";
import { ElTable } from "element-plus";
import { useTable } from "@/hooks/useTable";
import { useSelection } from "@/hooks/useSelection";
import { ColumnProps, TypeProps } from "@/components/ProTable/interface";
import { generateUUID, handleProp } from "@/utils";
import Pagination from "./components/Pagination.vue";
import TableColumn from "./components/TableColumn.vue";
import Operation from "./components/operation/index.vue";
import { Api } from "@form-create/element-ui";
import mitt from "mitt";

const mittBus = mitt();
const slots = useSlots();
provide("mittBus", mittBus);

export interface ProTableProps {
  columns: ColumnProps[]; // 列配置项  ==> 必传
  operations: (row) =>
    | {}
    | [
        {
          content?: string;
          onClick: (row: { [key: string]: any }, api: Api) => void;
          key: any;
        }
      ];

  operationDisabled?: (row: any) => boolean;
  data?: any[]; // 静态 table data 数据，若存在则不会使用 fetch 返回的 data ==> 非必传
  fetch?: (params: any) => Promise<any>; // 请求表格数据的 api ==> 非必传
  requestAuto?: boolean; // 是否自动执行请求 api ==> 非必传（默认为true）
  requestError?: (params: any) => void; // 表格 api 请求错误监听 ==> 非必传
  dataCallback?: (data: any) => any; // 返回数据的回调函数，可以对数据进行处理 ==> 非必传
  title?: string; // 表格标题 ==> 非必传
  pagination?: boolean; // 是否需要分页组件 ==> 非必传（默认为true）
  params?: any; // 初始化请求参数 ==> 非必传（默认为{}）
  border?: boolean; // 是否带有纵向边框 ==> 非必传（默认为true）
  rowKey?: string; // 行数据的 Key，用来优化 Table 的渲染，当表格数据多选时，所指定的 id ==> 非必传（默认为 id）
  formCreateInject: { api: Api };
  paramsFormatter?: (params: any) => any; //处理表格更新的参数=>非必传
  // 自定义根节点class
  tableClass?: string;
  // 自动接收bus
  busAuto?: boolean; //主要处理表格联动时,是否自动接收bus触发搜索 ==> 非必传（默认为true）
  // highlight-current-row //高亮显示当前行
  // :indent="20"
  // :row-class-name // 行的 class的回调方法
}

// { type: "radio", label: "单选", width: 80 },
// { type: "index", label: "#", width: 80 },

// 接受父组件参数，配置默认值
const props = withDefaults(defineProps<ProTableProps>(), {
  columns: () => [],
  operations: () => [],
  operationDisabled: () => false,
  requestAuto: true,
  busAuto: true,
  pagination: true,
  params: {},
  border: false,
  rowKey: "id"
});
// table 实例
const tableRef = ref<InstanceType<typeof ElTable>>();

// 生成组件唯一id
const uuid = ref("id-" + generateUUID());

// column 列类型
const columnTypes: TypeProps[] = ["selection", "radio", "index", "expand", "sort"];
// 单选值
const radio = ref("");

// 表格多选 Hooks
const { selectionChange, selectedList, selectedListIds, isSelected } = useSelection(props.rowKey);
// 表格操作 Hooks
const { tableData, pageable, searchParam, searchInitParam, getTableList, search, reset, handleSizeChange, handleCurrentChange } =
  useTable(
    props.fetch,
    computed(() => props.params),
    props.pagination,
    props.dataCallback,
    props.requestError,
    props.paramsFormatter
  );

props?.formCreateInject?.api.bus.$on("searchFormChanged", (params = {}) => {
  if (!props.busAuto) {
    return;
  }
  Object.assign(searchParam.value, params);
  Object.assign(searchInitParam.value, params);
  getTableList();
});
// 清空选中数据列表
const clearSelection = () => tableRef.value!.clearSelection();

// 初始化表格数据 && 拖拽排序
onMounted(() => {
  props.requestAuto && getTableList();
  props.data && (pageable.value.total = props.data.length);
});

// 处理表格数据
const processTableData = computed(() => {
  // 判断tableData.value是不是数组
  if (!props.data) return Array.isArray(tableData.value) ? tableData.value : tableData.value.records;

  if (!props.pagination) return props.data;
  return props.data.slice((pageable.value.current - 1) * pageable.value.size, pageable.value.size * pageable.value.current);
});

// 监听页面 params 改化，重新获取表格数据
watch(() => props.params, getTableList, { deep: true });

// 接收 columns 并设置为响应式
const tableColumns = reactive<ColumnProps[]>(props.columns);

// 扁平化 columns
const flatColumns = computed(() => flatColumnsFunc(tableColumns));

// 定义 enumMap 存储 enum 值（避免异步请求无法格式化单元格内容 || 无法填充搜索下拉选择）
const enumMap = ref(new Map<string, { [key: string]: any }[]>());
const setEnumMap = async ({ prop, enum: enumValue }: ColumnProps) => {
  if (!enumValue) return;

  // 如果当前 enumMap 存在相同的值 return
  if (enumMap.value.has(prop!) && (typeof enumValue === "function" || enumMap.value.get(prop!) === enumValue)) return;

  // 当前 enum 为静态数据，则直接存储到 enumMap
  if (typeof enumValue !== "function") return enumMap.value.set(prop!, unref(enumValue!));

  // 为了防止接口执行慢，而存储慢，导致重复请求，所以预先存储为[]，接口返回后再二次存储
  enumMap.value.set(prop!, []);

  // 当前 enum 为后台数据需要请求数据，则调用该请求接口，并存储到 enumMap
  const { data } = await enumValue();
  enumMap.value.set(prop!, data);
};

// 注入 enumMap
provide("enumMap", enumMap);
// 注入刷新table表格的方法，当表格编辑、删除之后，刷新表格
provide("refreshTable", getTableList);

// 扁平化 columns 的方法
const flatColumnsFunc = (columns: ColumnProps[], flatArr: ColumnProps[] = []) => {
  columns.forEach(async col => {
    if (col._children?.length) flatArr.push(...flatColumnsFunc(col._children));
    flatArr.push(col);

    // column 添加默认 isShow && isSetting && isFilterEnum 属性值
    col.isShow = col.isShow ?? true;
    col.isSetting = col.isSetting ?? true;
    col.isFilterEnum = col.isFilterEnum ?? true;

    // 设置 enumMap
    await setEnumMap(col);
  });
  return flatArr.filter(item => !item._children?.length);
};

// 过滤需要搜索的配置项 && 排序
const searchColumns = computed(() => {
  return flatColumns.value
    ?.filter(item => item.search?.el || item.search?.render)
    .sort((a, b) => a.search!.order! - b.search!.order!);
});

// 设置 搜索表单默认排序 && 搜索表单项的默认值
searchColumns.value?.forEach((column, index) => {
  column.search!.order = column.search?.order ?? index + 2;
  const key = column.search?.key ?? handleProp(column.prop!);
  const defaultValue = column.search?.defaultValue;
  if (defaultValue !== undefined && defaultValue !== null) {
    searchParam.value[key] = defaultValue;
    searchInitParam.value[key] = defaultValue;
  }
});

// 定义 emit 事件
const emit = defineEmits<{ search: []; reset: [] }>();

// 暴露给父组件的参数和方法 (外部需要什么，都可以从这里暴露出去)
defineExpose({
  element: tableRef,
  tableData: processTableData,
  radio,
  pageable,
  searchParam,
  searchInitParam,
  isSelected,
  selectedList,
  selectedListIds,
  // 下面为 function
  getTableList,
  search,
  reset,
  handleSizeChange,
  handleCurrentChange,
  clearSelection,
  selectionChange,
  enumMap
});
</script>
<style lang="scss">
.no-padding {
  padding: 0;
}

.table-box {
  border-radius: 10px;
}

.table-main {
  overflow: hidden;
  background-color: #fff;
  border-radius: 10px;
}

th {
  background: #fff !important;
}
</style>
