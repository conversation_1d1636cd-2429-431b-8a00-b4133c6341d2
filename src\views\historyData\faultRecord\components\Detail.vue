<template>
  <div class="container">
    <el-descriptions :column="1">
      <el-descriptions-item v-for="item of labels" :key="item.field" :label="item.label" :label-width="160">
        {{ showValue(props.data?.[item.field]) }}
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script lang="ts" setup>
import { ElDescriptions, ElDescriptionsItem } from "element-plus";
import { showValue } from "@/utils/helpers";
import { columnMap } from "@/views/historyData/faultRecord/types.ts";

const props = defineProps<{ data: { [key: string]: any } }>();
const labels = [
  { label: "矿车名称", field: columnMap.get("矿车名称") },
  { label: "故障码", field: columnMap.get("故障码") },
  { label: "故障描述", field: columnMap.get("故障描述") },
  { label: "故障时间", field: columnMap.get("故障时间") }
];
</script>
<style lang="scss" scoped>
.container {
  width: 100%;
  height: 100%;
  padding: 20px 20px 4px 20px;
  background: rgba(238, 243, 255, 1);
  --el-fill-color-blank: rgba(238, 243, 255, 1);
  border-radius: 8px;

  :deep(.el-descriptions__cell) {
    display: flex;
    align-items: center;
    line-height: 100%;
  }
  :deep(.el-descriptions__label) {
    display: inline-block;
    line-height: 100%;
    width: 60px;
    font-weight: bold;
    margin-right: 10px;
  }
}
</style>
