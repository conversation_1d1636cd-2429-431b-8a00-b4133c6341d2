export const ThemeColors = [
  "#356AFD",
  "#FFA200",
  "#00C290",
  "#7766F9",
  "#00D1D9",
  "#29B1FF",
  "#B57562",
  "#F9744B",
  "#C5A436",
  "#DB84FF",
  "#6482C8",
  "#A0CB3D"
];

export const tooltipDarkStyle = (params: any, unit = []) => {
  let html = `<div class="tooltip-dark-inner">
      <div class="head">${params[0].name}</div>
      <div class="tooltip-dark-inner-body">
      `;
  params.forEach((x, idx) => {
    html += `<div class="data-item">
              ${x.marker}
              <span style="margin-right:20px">${x.seriesName}</span>
              <span style="margin-left:auto">${x.value + (unit[idx] || "")}</span>
        </div>`;
  });
  html += `</div></div>`;
  return html;
};
