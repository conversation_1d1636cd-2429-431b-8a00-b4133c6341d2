<template>
  <div class="detail">
    <el-steps align-center class="task-car" style="max-width: 600px">
      <el-step>
        <template #title>
          <div class="name">挖机</div>
        </template>
        <template #icon>
          <div class="title">WJ-01</div>
        </template>
      </el-step>
      <el-step>
        <template #title>
          <div class="step-name">WJ-01</div>
        </template>
        <template #icon>
          <img alt="" class="step-img" src="../imgs/mineCarImg.png" />
        </template>
      </el-step>
      <el-step>
        <template #title>
          <div class="name">破碎站</div>
        </template>
        <template #icon>
          <div class="title">PZS-01</div>
        </template>
      </el-step>
    </el-steps>
    <el-steps :active="2" class="process-steps" direction="vertical" finish-status="success">
      <el-step>
        <template #title>
          <div class="description">
            <div class="name">挖机</div>
            <div class="time">2024-8-15 10:06:58</div>
          </div>
        </template>
      </el-step>
      <el-step>
        <template #title>
          <div class="description">
            <div class="name">挖机</div>
            <div class="time">2024-8-15 10:06:58</div>
          </div>
        </template>
        <template #icon>
          <img alt="" class="step-img" src="../imgs/mineCarImg.png" />
        </template>
      </el-step>
      <el-step>
        <template #title>
          <div class="description">
            <div class="name">挖机</div>
            <div class="time">2024-8-15 10:06:58</div>
          </div>
        </template>
        <template #icon>
          <div class="unfinished"></div>
        </template>
      </el-step>
    </el-steps>
  </div>
</template>

<script lang="ts" setup></script>

<style lang="scss" scoped>
.detail {
  --el-color-success: #00c290;
  height: 100%;
  display: flex;
  flex-direction: column;
  :deep(.task-car) {
    .title {
      font-size: 18px;
      padding: 0 10px;
      color: #1a1a1a;
      font-weight: bold;
    }
    .name {
      font-size: 14px;
      color: #979998;
    }
    .step-name {
      font-size: 14px;
      color: #00c290;
    }
    .el-step__head {
      .el-step__icon {
        width: 75px !important;
      }
      .el-step__line {
        background: #dde2e8;
      }
    }
  }
  :deep(.process-steps) {
    margin-top: 20px;
    padding: 0 10px 0 30px;
    .is-process {
      font-weight: inherit;
    }
    .description {
      display: flex;
      justify-content: space-between;
      .name {
        color: #1a1a1a;
      }
      .time {
        color: #656666;
      }
    }

    .unfinished {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      border: 1px solid #dde2e8;
    }
  }
}
</style>
