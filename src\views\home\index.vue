<template>
  <div class="home-container">
    <!--实时数据-->
    <RealTimeData />
    <!--调度计划-->
    <DispatchPlan />
    <!--调度任务-->
    <DispatchTask />
    <!--维修保养-->
    <DispatchMaintenance />
    <!--实时故障-->
    <RealTimeFault />
    <!--快捷入口-->
    <QuickLinks />
    <!--待保养矿车-->
    <MaintenanceTruck />
    <!--任务队列-->
    <TaskQueue />
    <!--出勤设备-->
    <InService />
  </div>
</template>
<script lang="ts" setup>
/**
 * @file 首页
 * <AUTHOR>
 * @date 2025/1/20
 */
import RealTimeFault from "@/views/home/<USER>/RealTimeFault/index.vue";
import RealTimeData from "@/views/home/<USER>/RealTimeData/index.vue";
import DispatchPlan from "@/views/home/<USER>/DispatchPlan/index.vue";
import DispatchTask from "@/views/home/<USER>/DispatchTask/index.vue";
import DispatchMaintenance from "@/views/home/<USER>/DispatchMaintenance/index.vue";
import QuickLinks from "@/views/home/<USER>/QuickLinks/index.vue";
import MaintenanceTruck from "@/views/home/<USER>/MaintenanceTruck/index.vue";
import TaskQueue from "@/views/home/<USER>/TaskQueue/index.vue";
import InService from "@/views/home/<USER>/InService/index.vue";
import { useAsyncStoreManager } from "@/views/home/<USER>";
const { resumeAll } = useAsyncStoreManager();
resumeAll();
</script>

<style lang="scss" scoped>
.home-container {
  width: 100%;
  display: grid;
  grid-template-columns: repeat(6, minmax(110px, 1fr)) 160px 160px;
  grid-template-rows: repeat(20, 1fr); //行
  grid-gap: 20px;
  height: 100%;
  :deep(.el-card__body) {
    height: 100%;
    flex-direction: column;
    display: flex;
    .el-empty {
      flex: 1;
    }
  }
  div {
    line-height: 100%;
  }
  //调增占比
  //实时数据
  .real-time-data {
    grid-column: 1 / 7;
    grid-row: 1 / 5;
  }
  //实时故障
  .real-time-fault {
    grid-column: 7 / 9;
    grid-row: 1 / 9;
  }
  //调度计划
  .dispatch-plan {
    grid-row: 5 / 9;
    grid-column: 1 / 3;
  }
  //调度任务
  .dispatch-task {
    grid-row: 5 / 9;
    grid-column: 3 / 5;
  }
  //维修保养
  .dispatch-maintenance {
    grid-row: 5 / 9;
    grid-column: 5 / 7;
  }
  //快捷入口
  .quick-links {
    grid-row: 9 / 13;
    grid-column: 1 / 7;
  }
  //待保养矿车
  .maintenance-truck {
    grid-row: 9 / 13;
    grid-column: 7 / 9;
  }
  //任务队列
  .task-queue {
    grid-row: 13 / 21;
    grid-column: 1 / 7;
  }
  //出勤设备
  .in-service {
    grid-row: 13 / 21;
    grid-column: 7 / 9;
  }
}
</style>
