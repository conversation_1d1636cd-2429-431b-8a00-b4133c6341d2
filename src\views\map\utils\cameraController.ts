import type { VcViewerProvider, VcCamera } from "vue-cesium/es/utils/types";
import { Camera } from "cesium";
import type * as TCesium from "cesium";
/**
 * 相机飞行配置接口
 */
export interface FlyToOptions {
  /** 目标位置的经度 */
  longitude: number;
  /** 目标位置的纬度 */
  latitude: number;
  /** 目标位置的高度（可选，默认为0） */
  height?: number;
  /** 相机距离目标点的距离（米）（可选） */
  distance?: number;
  /** 相机朝向角度（弧度）（可选） */
  heading?: number;
  /** 相机俯仰角度（弧度）（可选） */
  pitch?: number;
  /** 飞行持续时间（秒）（可选） */
  duration?: number;
}

/**
 * 相机控制器类
 */
export class CameraController {
  private camera: Camera;
  private Cesium: typeof TCesium;

  constructor(camera: Camera, Cesium: typeof TCesium) {
    this.camera = camera;
    this.Cesium = Cesium;
  }

  /**
   * 飞行到指定位置
   * @param options 飞行配置选项
   */
  public flyToPosition(options: FlyToOptions): void {
    const { longitude, latitude, height = 0, distance = 1000, heading = 0, pitch = -Math.PI / 4, duration = 3 } = options;

    // 创建目标位置的笛卡尔坐标
    const destination = this.Cesium.Cartesian3.fromDegrees(longitude, latitude, height);

    // 执行飞行
    this.camera.flyTo({
      destination,
      orientation: {
        heading: heading,
        pitch: pitch,
        roll: 0
      },
      duration: duration,
      complete: () => {
        console.log("飞行完成");
      },
      cancel: () => {
        console.log("飞行取消");
      }
    });
  }

  /**
   * 根据目标点自动计算合适的观察距离
   * @param latitude 纬度
   * @param targetSize 目标物体的大致尺寸（米）
   * @returns 建议的观察距离（米）
   */
  public calculateOptimalDistance(latitude: number, targetSize: number = 100): number {
    // 基于目标大小计算合适的观察距离
    const baseDistance = targetSize * 4;

    // 考虑纬度影响，在高纬度地区可能需要略微调整距离
    const latitudeFactor = Math.cos(this.Cesium.Math.toRadians(Math.abs(latitude)));

    return baseDistance / latitudeFactor;
  }

  /**
   * 智能飞行到目标点
   * @param longitude 经度
   * @param latitude 纬度
   * @param targetSize 目标物体的大致尺寸（米）
   */
  public smartFlyTo(longitude: number, latitude: number, targetSize?: number): void {
    const distance = this.calculateOptimalDistance(latitude, targetSize);

    this.flyToPosition({
      longitude,
      latitude,
      distance,
      // 设置一个稍微俯视的角度
      pitch: -Math.PI / 6,
      // 添加一些动态性，让相机略微旋转
      heading: Math.PI / 4,
      duration: 2
    });
  }

  /**
   * 环绕目标点观察
   * @param longitude 经度
   * @param latitude 纬度
   * @param height 高度
   * @param radius 环绕半径（米）
   * @param duration 环绕一周的时间（秒）
   */
  public orbitLocation(
    longitude: number,
    latitude: number,
    height: number = 0,
    radius: number = 1000,
    duration: number = 20
  ): void {
    const center = this.Cesium.Cartesian3.fromDegrees(longitude, latitude, height);
    const startHeading = this.camera.heading;

    // 创建环绕动画
    const orbitAnimation = (timestamp: number) => {
      // 计算当前时间下的朝向角度
      const angle = startHeading + ((timestamp % (duration * 1000)) / (duration * 1000)) * (2 * Math.PI);

      this.camera.lookAt(center, new this.Cesium.HeadingPitchRange(angle, -Math.PI / 4, radius));

      //   if (this.viewer.scene.mode !== undefined) {
      //     requestAnimationFrame(orbitAnimation);
      //   }
    };

    requestAnimationFrame(orbitAnimation);
  }

  /**
   * 停止当前的相机动画
   */
  public stopCameraMovement(): void {
    this.camera.cancelFlight();
    // this.viewer.scene.preRender.removeAll();
  }
}
