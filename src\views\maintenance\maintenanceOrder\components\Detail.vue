<template>
  <div>
    <LADetailCard
      :columns="2"
      :detail-data="detailData"
      :detail-labels="detailLabels"
      :tag-icon="tag"
      :title="detailData?.number"
    >
      <template #tag="{ data }">
        <StatusTag
          v-if="data.status === StatusEnum.TO_MAINTAIN && data.overdueDays > 0"
          :enum="tagIcon"
          :prop="'status'"
          :style="{ height: '20px' }"
          :value="data.status"
        />
      </template>
      <template #extra-btn="{ data }">
        <!--派工-->
        <form-create
          v-if="data.status === StatusEnum.NOT_ASSIGNED"
          v-model:api="dispatchWorkFormApi"
          :option="dispatchWorkOption"
          :rule="dispatchWorkRule"
        ></form-create>
        <!--转让工单-->
        <form-create
          v-if="data.status === StatusEnum.TO_MAINTAIN"
          v-model:api="transferFormApi"
          :option="transferOption"
          :rule="transferRule"
        ></form-create>
        <!--填写保养结果-->
        <form-create
          v-if="data.status === StatusEnum.TO_MAINTAIN"
          v-model:api="resultFormApi"
          :option="resultOption"
          :rule="resultRule"
        ></form-create>
      </template>
    </LADetailCard>
    <MaintenanceProject :data="maintenanceProjectList" />
    <MaintenanceRecord :maintenance-record-list="maintenanceRecordList" />
  </div>
</template>

<script lang="ts" setup>
/**
 * @file 维修保养-保养工单-详情组件
 * <AUTHOR>
 * @date 2025/2/18
 */
import LADetailCard from "@/components/LADetailCard.vue";
import { computed, nextTick, ref } from "vue";
import {
  assignPerson,
  getMaintenancePositionList,
  getMaintenanceRecordList,
  getMaintenanceWorkOrderDetail,
  maintenanceResultOrder,
  maintenanceTransferWorkOrder
} from "@/api/modules/repair.ts";
import { columnMap, maintenanceStatusInfo, StatusEnum } from "@/views/maintenance/maintenanceOrder/types.ts";
import {
  dispatchWorkFormCreate,
  repairResultFormCreate,
  transferFormCreate
} from "@/views/maintenance/maintenanceOrder/components/formCreate.ts";
import StatusTag from "@/components/StatusTag.vue";
import MaintenanceProject from "@/views/maintenance/maintenanceOrder/components/MaintenanceProject.vue";
import MaintenanceRecord from "@/views/maintenance/maintenanceOrder/components/MaintenanceRecord.vue";

const props = defineProps({
  formCreateInject: {
    type: Object,
    default: () => {}
  }
});
// 逾期天数的tag
const tagIcon = computed(() => {
  return [
    {
      bg: "rgba(242, 85, 85, 0.1)",
      color: "rgba(242, 85, 85, 1)",
      status: StatusEnum.TO_MAINTAIN,
      text: "逾期" + detailData.value.overdueDays + "天"
    }
  ];
});
// 储存详情方法
const detailData = ref();
// 保养记录
const maintenanceRecordList = ref();
const detailId = ref();
// 保养项目
const maintenanceProjectList = ref();
// 转让工单配置
const transferFormApi = ref();
const transferOption = {
  resetBtn: false,
  submitBtn: false,
  row: {
    justify: "center"
  }
};
const transferRule = ref([
  {
    type: "AddBtn",
    props: {
      btn: {
        icon: "",
        style: {
          height: "32px",
          borderRadius: "6px",
          color: "#000",
          border: "1px solid #DDE2E8",
          background: "#fff"
        },
        content: "转让工单"
      },
      // 是否是自定义表单弹窗 未嵌入表格只是显示弹窗form
      isCustomDialog: true,
      formatter: () => {
        // console.log("个人资料", model.value);
        return {
          id: detailData.value.id,
          newResponsiblePersonId: detailData.value.repairResponsiblePersonId,
          newResponsiblePersonName: detailData.value.repairResponsiblePersonName
        };
      },
      dialog: { title: "转让工单" },
      submitRequest: params => {
        return maintenanceTransferWorkOrder(params).then(res => {
          refresh();
          return res;
        });
      }
    },
    children: [transferFormCreate]
  }
]);
// 派工配置
const dispatchWorkFormApi = ref();
const dispatchWorkOption = {
  resetBtn: false,
  submitBtn: false,
  row: {
    justify: "center"
  }
};
const dispatchWorkRule = ref([
  {
    type: "AddBtn",
    props: {
      btn: {
        icon: "",
        style: {
          height: "32px",
          borderRadius: "6px",
          color: "#000",
          border: "1px solid #DDE2E8",
          background: "#fff"
        },
        content: "派工"
      },
      // 是否是自定义表单弹窗 未嵌入表格只是显示弹窗form
      isCustomDialog: true,
      formatter: () => {
        // console.log("个人资料", model.value);
        return {
          workOrderId: detailData.value.id
        };
      },
      dialog: { title: "派工" },
      submitRequest: params => {
        return assignPerson(params).then(res => {
          refresh();
          return res;
        });
      }
    },
    children: [dispatchWorkFormCreate]
  }
]);
// 填写保养结果配置
const resultFormApi = ref();
const resultOption = {
  resetBtn: false,
  submitBtn: false,
  row: {
    justify: "center"
  }
};
const resultRule = ref([
  {
    type: "AddBtn",
    props: {
      btn: {
        icon: "",
        style: {
          height: "32px",
          borderRadius: "6px",
          color: "#fff",
          border: "1px solid #DDE2E8",
          background: "rgba(53, 106, 253, 1)"
        },
        content: "填写保养结果"
      },
      isCustomDialog: true,
      formatter: () => {
        return { id: detailData.value.id, maintenanceResults: "completed" };
      },
      dialog: { title: "填写保养结果" },
      submitRequest: params => {
        return maintenanceResultOrder(params).then(res => {
          refresh();
          return res;
        });
      }
    },
    children: [repairResultFormCreate]
  }
]);
const tag = computed(() => {
  return {
    enum: maintenanceStatusInfo,
    prop: "status",
    value: detailData?.value?.status
  };
});

// 刷新/获取数据的方法
const refresh = async () => {
  await getMaintenanceWorkOrderDetail({ id: detailId.value }).then(res => {
    detailData.value = res.data;
  });
  // // 获取保养项目
  await getMaintenancePositionList({ workOrderId: detailId.value }).then(res => {
    maintenanceProjectList.value = res.data;
  });
  // 获取保养记录
  await getMaintenanceRecordList({ workOrderId: detailId.value }).then(res => {
    maintenanceRecordList.value = res.data;
  });
};
const detailLabels = {
  [columnMap.get("矿车名称")]: {
    label: "矿车名称",
    hiddenColon: true
  },
  [columnMap.get("矿卡型号")]: {
    label: "矿卡型号",
    hiddenColon: true
  },
  [columnMap.get("保养间隔")]: {
    label: "保养间隔",
    hiddenColon: true
  },
  [columnMap.get("工单生成时间")]: {
    label: "工单生成时间",
    hiddenColon: true
  },
  [columnMap.get("保养负责人")]: {
    label: "保养负责人",
    hiddenColon: true
  },
  [columnMap.get("保养完成时间")]: {
    label: "保养完成时间",
    hiddenColon: true
  }
};
nextTick(() => {
  detailData.value = props.formCreateInject.api.formData();
  // 储存id信息
  detailId.value = props.formCreateInject.api.formData().id;
  refresh();
});
</script>

<style scoped></style>
