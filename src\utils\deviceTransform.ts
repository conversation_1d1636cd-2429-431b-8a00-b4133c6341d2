/**
 * 设备数据转换工具
 * 将新的设备数据结构转换为组件需要的格式
 * 遵循DRY原则，复用现有的状态处理逻辑
 */

import type { Device, MineTrainDevice, BulldozerDevice, ChargingPileDevice, CrushingStationDevice, CockpitDevice } from '@/stores/deviceTypes'
import { getStatusText, getTruckDriveMode } from '@/views/copilot/shared'
import { StatusEnum as TruckStatusEnum } from "@/views/deviceManage/miningTruckManage/types"
import { StatusEnum as ChargingPileStatusEnum } from "@/views/deviceManage/chargeManage/types"
import { StatusEnum as BulldozerStatusEnum } from "@/views/deviceManage/diggingMachineManage/types"
import { StatusEnum as CrusherStatusEnum } from "@/views/deviceManage/crushingStationManage/types"
import { StatusEnum as CockpitStatusEnum } from "@/views/deviceManage/cockpitManage/types"

// 组件需要的设备数据格式（保持与原有格式一致）
export interface DeviceSpaceData {
  name: string
  code: string
  modeText: string
  battery: number
  status: number
  statusText: string
  isFault: boolean
  lowBattery: boolean
  showMode: boolean
  showBattery: boolean
  deviceType: string
  rawData: {
    id: string
    name: string
    code: string
    status: number
    isActivateTheme: boolean
    [key: string]: any
  }
}

/**
 * 将矿车设备转换为显示格式
 */
export const transformMineTrainToDisplay = (device: MineTrainDevice): DeviceSpaceData => {
  return {
    deviceType: "deviceMineTrain",
    name: device.name || "",
    code: device.code || "",
    modeText: getTruckDriveMode(+(device.driveMode || 0)),
    battery: device.electricQuantity || 0,
    status: device.status,
    statusText: device.errorLevel || device.taskCurrentStep || getStatusText(device.status?.toString(), "deviceMineTrain") || "",
    isFault: device.errorFlag?.toString() === "1" || device.errorFlag === true,
    lowBattery: (device.electricQuantity || 0) < 20,
    showMode: true,
    showBattery: true,
    rawData: {
      ...device,
      isActivateTheme: device.status?.toString() !== TruckStatusEnum.TRAIN_OFFLINE.toString()
    }
  }
}

/**
 * 将挖机设备转换为显示格式
 */
export const transformBulldozerToDisplay = (device: BulldozerDevice): DeviceSpaceData => {
  return {
    deviceType: "deviceBulldozers",
    name: device.name || "",
    code: device.code || "",
    modeText: "自动",
    battery: 100,
    status: device.status || 0,
    statusText: getStatusText(device.status?.toString(), "deviceBulldozers") || "",
    isFault: false,
    lowBattery: false,
    showMode: false,
    showBattery: false,
    rawData: {
      ...device,
      isActivateTheme: device.status?.toString() !== BulldozerStatusEnum.OFFLINE.toString()
    }
  }
}

/**
 * 将充电桩设备转换为显示格式
 */
export const transformChargingPileToDisplay = (device: ChargingPileDevice): DeviceSpaceData => {
  return {
    deviceType: "deviceChargingPile",
    name: device.name || "",
    code: device.code || "",
    modeText: "自动",
    battery: 100,
    status: device.status || 0,
    statusText: getStatusText(device.status?.toString(), "deviceChargingPile") || "",
    isFault: false,
    lowBattery: false,
    showMode: true,
    showBattery: true,
    rawData: {
      ...device,
      isActivateTheme: device.status?.toString() !== ChargingPileStatusEnum.CHARGING_PILE_OFFLINE
    }
  }
}

/**
 * 将破碎站设备转换为显示格式
 */
export const transformCrushingStationToDisplay = (device: CrushingStationDevice): DeviceSpaceData => {
  return {
    deviceType: "deviceCrushingStation",
    name: device.name || "",
    code: device.code || "",
    modeText: "自动",
    battery: 100,
    status: device.status || 0,
    statusText: "正常",
    isFault: false,
    lowBattery: false,
    showMode: true,
    showBattery: false,
    rawData: {
      ...device,
      isActivateTheme: device.status?.toString() !== CrusherStatusEnum.OFFLINE.toString()
    }
  }
}

/**
 * 将驾驶舱设备转换为显示格式
 */
export const transformCockpitToDisplay = (device: CockpitDevice): DeviceSpaceData => {
  return {
    deviceType: "deviceCockpits",
    name: device.name || "",
    code: device.code || "",
    modeText: "自动",
    battery: 100,
    status: device.status || 0,
    statusText: getStatusText(device.status?.toString(), "deviceCockpits") || "",
    isFault: false,
    lowBattery: false,
    showMode: false,
    showBattery: false,
    rawData: {
      ...device,
      isActivateTheme: device.status?.toString() !== CockpitStatusEnum.OFFLINE.toString()
    }
  }
}

/**
 * 通用设备转换函数
 */
export const transformDeviceToDisplay = (device: Device): DeviceSpaceData => {
  switch (device.deviceType) {
    case 'mineTrain':
      return transformMineTrainToDisplay(device as MineTrainDevice)
    case 'bulldozer':
      return transformBulldozerToDisplay(device as BulldozerDevice)
    case 'chargingPile':
      return transformChargingPileToDisplay(device as ChargingPileDevice)
    case 'crushingStation':
      return transformCrushingStationToDisplay(device as CrushingStationDevice)
    case 'cockpit':
      return transformCockpitToDisplay(device as CockpitDevice)
    default:
      throw new Error(`Unknown device type: ${(device as any).deviceType}`)
  }
}
