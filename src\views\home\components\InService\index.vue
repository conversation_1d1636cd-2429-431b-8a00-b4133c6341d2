<template>
  <div class="in-service">
    <LACard :showHeader="false" shadow="never">
      <LATitle title="出勤设备" />
      <div v-if="!hasPermission" class="in-service-content">
        <div
          v-for="(item, i) of inServiceData"
          :key="item?.title"
          :style="{ backgroundColor: item?.bgColor }"
          class="in-service-item"
        >
          <span class="item-title">{{ item?.title }}</span>
          <span :style="{ color: item?.color ?? 'initial' }" class="item-value">{{ data?.[i] || 0 }}</span>
        </div>
      </div>
      <PlaceholderImage v-else type="noPermission" />
    </LACard>
  </div>
</template>

<script lang="ts" setup>
/**
 * @file 首页-出勤设备
 * <AUTHOR>
 * @date 2025/1/22
 */
import LATitle from "@/components/LATitle.vue";
import LACard from "@/components/LACard/index.vue";
import { computed } from "vue";
import PlaceholderImage from "@/components/PlaceholderImage.vue";
import { useAllDeviceAttendance } from "@/views/home/<USER>";

const { data, error } = useAllDeviceAttendance();

//  401则为没有权限
const hasPermission = computed(() => {
  return error.value?.status === 401;
});
interface ServiceData {
  title: string;
  bgColor: string;
  color?: string; // 可选属性
}
// 六个模块每个模块都有数据和标题
// 1. 出勤矿车 2. 故障矿车 3. 空闲矿车 4. 充电矿车 5. 出勤挖机 6. 出勤破碎站
const inServiceData: Record<string, ServiceData> = {
  mineTrainOnlineCount: { title: "出勤矿车", bgColor: "rgba(0, 194, 144, 0.04)" },
  mineTrainErrorCount: { title: "故障矿车", bgColor: "rgba(242, 85, 85, 0.04)", color: "rgba(242, 85, 85, 1)" },
  mineTrainLeisureCount: { title: "空闲矿车", bgColor: "rgba(41, 177, 255, 0.04)" },
  mineTrainChargingCount: { title: "充电矿车", bgColor: "rgba(0, 209, 217, 0.04)" },
  bulldozersOnlineCount: { title: "出勤挖机", bgColor: "rgba(0, 194, 144, 0.04)" },
  crushStationOnlineCount: { title: "出勤破碎站", bgColor: "rgba(0, 194, 144, 0.04)" }
};
</script>

<style lang="scss" scoped>
.in-service-content {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;

  .in-service-item {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 10px;

    .item-title {
      font-size: 14px;
      color: #656666;
    }

    .item-value {
      font-size: 28px;
      font-weight: bold;
      color: #1a1a1a;
    }
  }
}
</style>
