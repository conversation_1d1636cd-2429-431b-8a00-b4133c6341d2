<template>
  <div class="container-tabs">
    <form-create v-model:api="fApi" :option="option" :rule="rule"></form-create>
  </div>
</template>

<script lang="tsx" setup>
/**
 * @file 系统管理-系统日志入口页
 * <AUTHOR>
 * @date 2024/11/14
 */
import { ref } from "vue";
import RecordsOperations from "@/views/system/systemLog/components/RecordsOperations.vue";
import LoginLogs from "@/views/system/systemLog/components/LoginLogs.vue";
import formCreate, { Api } from "@form-create/element-ui";
formCreate.component("RecordsOperations", RecordsOperations);
formCreate.component("LoginLogs", LoginLogs);
const fApi = ref();
const option = {
  form: { inline: true },
  resetBtn: false,
  submitBtn: false
};

// 当前选中的筛选的状态

const rule = ref([
  {
    type: "LAShadowButtonGroup",
    name: "LAShadowButtonGroup",
    wrap: { style: "marginBottom: 0" },
    style: { "margin-bottom": "10px" },
    value: "RecordsOperation",
    props: {
      list: [
        { label: "操作记录", value: "RecordsOperations" },
        { label: "登录日志", value: "LoginLogs" }
      ]
    },
    on: {
      "update:modelValue"(val) {
        // val="RecordsOperations"时，显示RecordsOperations组件，否则显示LoginLogs组件
        // 获取组件的list列表
        const componentsList = fApi.value.findRule({ name: "LAShadowButtonGroup" }).props.list;
        // 使用componentsList和val完成逻辑点击val时显示val对应的组件
        const show = val => {
          // 对componentsList里的每一的元素进行操作，如果componentsList的value等于val就显示对应的组件
          componentsList.forEach(item => {
            if (item.value == val) {
              fApi.value.hidden(false, item.value);
            } else {
              fApi.value.hidden(true, item.value);
            }
          });
        };
        show(val);
      }
    }
  },
  {
    type: "RecordsOperations",
    on: {
      mounted(api: Api) {
        api.exec("v:search", "onReset");
      }
    },
    wrap: {
      style: {
        width: "100%"
      }
    },
    field: "RecordsOperations",
    key: "1"
  },
  {
    type: "LoginLogs",
    on: {
      mounted(api: Api) {
        api.exec("v:search", "onReset");
      }
    },
    wrap: {
      style: {
        width: "100%"
      }
    },
    field: "LoginLogs",
    key: "2"
  }
]);
</script>

<style lang="scss" scoped>
.container-tabs {
  width: 100%;
  .el-tabs__content {
    padding: 32px;
    color: #6b778c;
    font-size: 12px;
    font-weight: 600;
  }
  .table-box {
    width: 100%;
  }

  :deep(.el-tabs__header) {
    display: none;
  }
  :deep(.el-radio-button--large .el-radio-button__inner) {
    padding: 10px 20px;
  }
}

:deep(.LAbtn) {
  height: 32px;
}
.el-tabs--right .el-tabs__content,
.el-tabs--left .el-tabs__content {
  height: 100%;
}
:deep(.el-form-item .el-form-item) {
  margin-bottom: 0;
}
:deep(.el-form-item) {
  margin-right: 0;
}
:deep(.search-form__operator) {
  margin-bottom: 10px;
  width: 100%;
  .el-select--large .el-select__wrapper {
    min-height: initial;
    padding: 9px 16px;
  }
  .el-date-editor {
    min-height: 32px;
  }
}
</style>
