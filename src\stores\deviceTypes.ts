/**
 * 设备数据类型定义
 * 遵循KISS原则，只定义核心必要的类型
 */

// 设备类型枚举
export type DeviceType = 'mineTrain' | 'bulldozer' | 'chargingPile' | 'crushingStation' | 'cockpit'

// 基础设备信息接口
export interface BaseDevice {
  id: string
  name: string
  code: string
  status: number
  deviceType: DeviceType
}

// 矿车设备信息
export interface MineTrainDevice extends BaseDevice {
  deviceType: 'mineTrain'
  driveMode?: string
  electricQuantity?: number
  taskFlag?: boolean
  taskCurrentStep?: string
  errorFlag?: boolean | number
  errorLevel?: string
}

// 挖机设备信息
export interface BulldozerDevice extends BaseDevice {
  deviceType: 'bulldozer'
  posture?: string
}

// 充电桩设备信息
export interface ChargingPileDevice extends BaseDevice {
  deviceType: 'chargingPile'
}

// 破碎站设备信息
export interface CrushingStationDevice extends BaseDevice {
  deviceType: 'crushingStation'
}

// 驾驶舱设备信息
export interface CockpitDevice extends BaseDevice {
  deviceType: 'cockpit'
}

// 联合设备类型
export type Device = MineTrainDevice | BulldozerDevice | ChargingPileDevice | CrushingStationDevice | CockpitDevice

// WebSocket消息中的设备数据结构
export interface WSDeviceInitData {
  bulldozersList?: Array<{
    code: string
    name?: string
    id?: string
    posture?: string
    [key: string]: any
  }>
  mineTrainList?: Array<{
    code: string
    name?: string
    id?: string
    currentLocation?: string
    electricQuantity?: number
    driveMode?: number
    loadMode?: number
    speed?: number
    [key: string]: any
  }>
}

// HTTP API返回的设备数据结构（来自useDeviceWingSection）
export interface APIDeviceData {
  attendanceNumber?: number
  leisureNumber?: number
  rechargeBatteriesNumber?: number
  faultsNumber?: number
  mineTrainDtoList?: Array<{
    id: string
    name: string
    code: string
    status: number
    driveMode?: string
    electricQuantity?: number
    taskFlag?: boolean
    taskCurrentStep?: string
    errorFlag?: boolean | number
    errorLevel?: string
    [key: string]: any
  }>
  bulldozersList?: Array<{
    id: string
    name: string
    code: string
    status: number
    [key: string]: any
  }>
  chargingPileList?: Array<{
    id: string
    name: string
    code: string
    status: number
    [key: string]: any
  }>
  crushingStationList?: Array<{
    id: string
    name: string
    code: string
    status: number
    [key: string]: any
  }>
  cockpitsList?: Array<{
    id: string
    name: string
    code: string
    status: number
    [key: string]: any
  }>
}

// 设备统计信息
export interface DeviceStatistics {
  attendanceNumber: number
  leisureNumber: number
  rechargeBatteriesNumber: number
  faultsNumber: number
}
