<template>
  <vc-entity v-if="showConnectionPolyline" v-for="(polyline, index) in polylineData" :key="index">
    <vc-graphics-polyline :positions="polyline.positions" :material="material" :width="1" :clamp-to-ground="false" />
  </vc-entity>
</template>
<script lang="ts" setup>
/*
  虚线连接线加载
*/
import { ref, watch } from "vue";
import { VcGraphicsPolyline, VcEntity } from "vue-cesium";
import { useMapActionChannel } from "@/views/map/channel";
// 连接线的样式
const material = {
  fabric: {
    type: "PolylineDash",
    uniforms: { color: "white", dashLength: 12.0, dashPattern: 255.0 }
  }
};
const showConnectionPolyline = ref(true);
// 虚线连接线数据
const polylineData = ref([
  {
    positions: [
      { lng: 139.93206611147403, lat: 35.902441613803354, height: 1 },
      { lng: 139.93297171291795, lat: 35.903447721624815, height: 1 }
    ],
    id: "dashed-connection-polyline"
  }
]);

interface Position {
  lng: number;
  lat: number;
  height?: number;
}
// 更新虚线连接线
const updatePolyline = (id: string, { start, end }: { start?: Position; end?: Position }) => {
  const index = polylineData.value.findIndex(polyline => polyline.id === id);
  if (index !== -1) {
    polylineData.value[index].positions = [
      { ...polylineData.value[index].positions[0], ...start, height: 1 },
      { ...polylineData.value[index].positions[1], ...end, height: 1 }
    ];
  }
};
// 移除虚线连接线
const removePolyline = (id: string) => {
  const index = polylineData.value.findIndex(polyline => polyline.id === id);
  if (index !== -1) {
    polylineData.value.splice(index, 1);
  }
};

// 显示/隐藏连接线
watch(useMapActionChannel().data, data => {
  switch (data.type) {
    case "dashed":
      const { active } = data.data;
      showConnectionPolyline.value = active;
      break;
  }
});
// todo 监听websocket数据，更新 删除 添加连接线
</script>
