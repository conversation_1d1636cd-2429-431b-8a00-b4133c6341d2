<template>
  <div class="quick-links">
    <LACard :showHeader="false" shadow="never">
      <LATitle title="快捷入口" />
      <div class="links-container">
        <div v-for="item in showLinks" class="links">
          <router-link :to="links[item.name].path" class="jump-path">
            <img :src="imgSrc(links[item.name].img)" alt="" />
            <span class="path-title">
              {{ links[item.name].title }}
            </span>
          </router-link>
        </div>
      </div>
    </LACard>
  </div>
</template>

<script lang="ts" setup>
/**
 * @file 首页-快捷入口
 * <AUTHOR>
 * @date 2025/1/21
 */
import { computed, ref } from "vue";
import LATitle from "@/components/LATitle.vue";
import LACard from "@/components/LACard/index.vue";
import { useAuthStore } from "@/stores/modules/auth.ts";
import * as prefixIcon from "../../imgs/index.ts";

const authStore = useAuthStore();
/**
 * 利用计算属性动态获取用户有权限显示的快捷链接列表。
 * - 计算属性会监听authStore.flatMenuListGet的变化。
 * - 根据item.name判断该链接是否需要展示。
 * - 只选择存在于links对象中的项目来展示。
 */
const showLinks = computed(() => {
  return authStore.flatMenuListGet.filter(item => {
    return links[item.name];
  });
});
/**
 * 动态获取图标路径的方法。
 * - 根据传入的iconName参数返回对应的图片路径。
 * - 如果iconName为空，则返回undefined。
 *
 * @param iconName - 图标的名称，根据此名称从prefixIcon对象中查找对应的图片路径。
 * @returns 图片路径
 */
const imgSrc = (iconName: string) => {
  if (!iconName) return;
  return prefixIcon[iconName];
};

// 配置需要显示的入口列表
const links = {
  // 调度计划
  dispatchPlan: {
    img: "quickTaskImg",
    title: "调度计划",
    path: "/dispatcher/dispatchPlan/index"
  },
  // 调度任务
  dispatchTask: {
    img: "quickPlanImg",
    title: "调度任务",
    path: "/dispatcher/dispatchTask/index"
  },
  // 维修工单
  repairOrder: {
    img: "quickRepairImg",
    title: "维修工单",
    path: "/maintenance/repairOrder/index"
  },
  // 保养工单
  maintenanceOrder: {
    img: "quickMaintenanceImg",
    title: "保养工单",
    path: "/maintenance/maintenanceOrder/index"
  },
  // 保养知识库
  knowledgeBase: {
    img: "quickKnowledgeBaseImg",
    title: "保养知识库",
    path: "/maintenance/knowledgeBase/index"
  },
  // 矿卡管理
  miningTruckManage: {
    img: "quickMineTruckImg",
    title: "矿卡管理",
    path: "/deviceManage/miningTruckManage/index"
  },
  // 充电桩管理
  chargeManage: {
    img: "quickChargingImg",
    title: "充电桩管理",
    path: "/deviceManage/chargeManage/index"
  },
  // 挖机管理
  diggingMachineManage: {
    img: "quickDiggingMachineImg",
    title: "挖掘机管理",
    path: "/deviceManage/diggingMachineManage/index"
  },
  // 破碎站管理
  crushingStationManage: {
    img: "quickCrushingStationImg",
    title: "破碎站管理",
    path: "/deviceManage/crushingStationManage/index"
  },
  // 驾驶舱管理
  cockpitManage: {
    img: "quickCockpitImg",
    title: "驾驶舱管理",
    path: "/deviceManage/cockpitManage/index"
  }
};
</script>

<style scoped>
.links-container {
  display: flex;
  align-items: center;
  overflow-x: auto;
  overflow-y: hidden;
  gap: 20px;

  .links {
    padding: 12px 18px;
    cursor: pointer;
    .jump-path {
      font-size: 14px;
      color: #1a1a1a;
      display: flex;
      align-items: center;
      flex-direction: column;
      gap: 10px;
      .path-title {
        white-space: nowrap;
      }
    }
  }
}
</style>
