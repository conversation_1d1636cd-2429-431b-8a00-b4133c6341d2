import { PORT1 } from "@/api/config/servicePort";
import http from "@/api";
/**
 * @name 测试用户管理
 */
// 获取功能权限
// 获取用户状态字典
// export const getUserStatus = () => {
//     return http.get<User.ResStatus[]>(PORT1 + `/user/status`);
// };
// export const getUserList = (params: User.ReqUserParams) => {
//   return http.post<ResPage<User.ResUserList>>(PORT1 + `/user/list`, params);
// };
// 获取功能权限
export const getRoleList = params => {
  return http.post(`/usercenter-server/system/role/listQueryByPage`, params);
};
// 获取树形用户列表
export const getUserTreeList = params => {
  return http.post(PORT1 + `/user/tree/list`, params);
};
