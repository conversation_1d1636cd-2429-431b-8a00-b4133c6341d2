import dayjs, { Dayjs } from "dayjs";
import isoWeek from "dayjs/plugin/isoWeek";
import dayOfYear from "dayjs/plugin/dayOfYear";
import isSameOrAfter from "dayjs/plugin/isSameOrAfter";
import isSameOrBefore from "dayjs/plugin/isSameOrBefore";

dayjs.extend(isoWeek);
dayjs.extend(dayOfYear);
dayjs.extend(isSameOrAfter);
dayjs.extend(isSameOrBefore);

/**
 * 描述ISO周的起止日期范围
 * @interface WeekRange
 * @property {string} start - 周起始日期（周一），格式：YYYY-MM-DD
 * @property {string} end - 周结束日期（周日），格式：YYYY-MM-DD
 */
interface WeekRange {
  start: string;
  end: string;
}

/**
 * 周范围计算结果的完整类型定义
 * @interface WeekRangesResult
 * @property {string} targetDate - 输入日期的标准化格式（YYYY-MM-DD）
 * @property {number} currentYear - 输入日期所在年份
 * @property {number} currentWeek - 输入日期所在ISO周数（1-53）
 * @property {WeekRange} currentWeekRange - 输入日期所在周的起止日期
 * @property {number} lastYear - 上一年的年份
 * @property {WeekRange} lastYearWeekRange - 上一年同周数的起止日期
 */
export interface WeekRangesResult {
  targetDate: string;
  currentYear: number;
  currentWeek: number;
  currentWeekRange: WeekRange;
  lastYear: number;
  lastYearWeekRange: WeekRange;
}

/**
 * 计算指定日期所在ISO周（周一为起始）及上一年同周的完整日期范围信息
 * 遵循ISO 8601标准：每年最多53周，每周从周一开始，周日结束
 * @param {string | number | Date | Dayjs} [date] - 可选输入日期，支持多种格式
 *   - 字符串：'2023-12-31'、'2023/12/31'等
 *   - 数字：时间戳（毫秒）
 *   - Date对象：JavaScript原生Date
 *   - Dayjs对象：dayjs实例
 *   - 未提供时默认使用当前日期
 * @returns {WeekRangesResult} 包含周信息的完整对象
 * @example
 * // 获取当前周信息
 * const result = getWeekRanges();
 * // 获取指定日期周信息
 * const result = getWeekRanges('2023-12-31');
 * // 结果格式
 * {
 *   targetDate: '2023-12-31',
 *   currentYear: 2023,
 *   currentWeek: 52,
 *   currentWeekRange: { start: '2023-12-25', end: '2023-12-31' },
 *   lastYear: 2022,
 *   lastYearWeekRange: { start: '2022-12-26', end: '2023-01-01' }
 * }
 */
export function getWeekRanges(date?: string | number | Date | Dayjs): WeekRangesResult {
  // 解析输入日期，无效则默认当前日期
  const targetDate: Dayjs = dayjs(date).isValid() ? dayjs(date) : dayjs();

  // 获取ISO标准的周数和年份
  const currentYear: number = targetDate.year();
  const currentWeek: number = targetDate.isoWeek(); // ISO周数（1-53，周一为起始）
  const lastYear: number = currentYear - 1;

  /**
   * 计算指定年份+ISO周数的起止日期（周一至周日）
   * @param {number} year - 目标年份
   * @param {number} weekNum - 目标ISO周数（1-53）
   * @returns {WeekRange} 包含起止日期的对象
   * @throws {Error} 当周数超出有效范围（1-53）时抛出错误
   */
  const getISOWeekRange = (year: number, weekNum: number): WeekRange => {
    if (weekNum < 1 || weekNum > 53) {
      throw new Error(`Invalid ISO week number: ${weekNum}. Must be between 1 and 53.`);
    }

    // 定位到该年第N个ISO周的周一（startOf('isoWeek')直接返回周一）
    const weekStart: string = dayjs()
      .year(year)
      .isoWeek(weekNum)
      .startOf("isoWeek") // 强制返回当周周一（ISO标准）
      .format("YYYY-MM-DD");

    // 周日 = 周一 + 6天
    const weekEnd: string = dayjs(weekStart).add(6, "day").format("YYYY-MM-DD");

    return { start: weekStart, end: weekEnd };
  };

  // 计算目标日期所在周及上一年同周的范围
  const currentWeekRange: WeekRange = getISOWeekRange(currentYear, currentWeek);
  const lastYearWeekRange: WeekRange = getISOWeekRange(lastYear, currentWeek);

  return {
    targetDate: targetDate.format("YYYY-MM-DD"), // 输入日期标准化
    currentYear,
    currentWeek,
    currentWeekRange,
    lastYear,
    lastYearWeekRange
  };
}

/**
 * 日期位置信息接口定义
 * @interface IDayPositions
 * @property {number} dayOfWeek - 本周第几天（1-7，周一为1）
 * @property {number} dayOfMonth - 本月第几天（1-31）
 * @property {number} dayOfYear - 本年第几天（1-366）
 */
interface IDayPositions {
  dayOfWeek: number;
  dayOfMonth: number;
  dayOfYear: number;
}

/**
 * 计算指定日期是本周、本月、本年中的第几天
 * @param {string|number|Date} date - 输入日期（支持'YYYY-MM-DD'、时间戳等格式）
 * @returns {IDayPositions} 包含周内、月内、年内位置的对象
 */
export function getDayPositions(date: string | number | Date): IDayPositions {
  const targetDate = dayjs(date);

  // 本周第几天（周一为第1天，周日为第7天）
  const dayOfWeek = targetDate.day() || 7; // 0=周日 -> 7，1-6保持不变

  // 本月第几天（1-31）
  const dayOfMonth = targetDate.date();

  // 本年第几天（1-366）
  const dayOfYear = targetDate.dayOfYear();

  return {
    dayOfWeek, // 本周第几天（1-7，周一为1）
    dayOfMonth, // 本月第几天（1-31）
    dayOfYear // 本年第几天（1-366）
  };
}

/**
 * 根据日期字符串格式获取日期在对应周期中的位置或天数
 * @param {string} dateStr - 日期字符串，支持格式：YYYY、YYYY-MM、YYYY-MM-DD
 * @returns {number} 不同格式返回值含义：
 * - YYYY: 返回当年已过天数（如2023返回365或366）
 * - YYYY-MM: 返回当月天数（如2023-02返回28或29）
 * - YYYY-MM-DD: 返回当周第几天（1-7，周一为1，周日为7）
 * @example
 * // 返回2023年已过天数
 * getDatePositionByFormat('2023')
 * // 返回2023年2月天数
 * getDatePositionByFormat('2023-02')
 * // 返回2023-06-15是当周第4天（周四）
 * getDatePositionByFormat('2023-06-15')
 */
export function getDatePositionByFormat(dateStr: string): number {
  const today = dayjs();
  // 自动检测日期格式（YYYY、YYYY-MM、YYYY-MM-DD）
  const format = dateStr.includes('-') 
    ? dateStr.split('-').length === 3 ? 'YYYY-MM-DD' : 'YYYY-MM'
    : 'YYYY';

  switch (format) {
    case 'YYYY':
      // 如果是当前年份，返回今天是当年第几天，否则返回该年总天数
      return dateStr === today.format('YYYY') 
        ? today.dayOfYear() 
        : dayjs(`${dateStr}-12-31`).dayOfYear();

    case 'YYYY-MM':
      const targetMonth = dayjs(dateStr);
      // 如果是当前月份，返回今天日期，否则返回该月总天数
      return targetMonth.isSame(today, 'month') && targetMonth.isSame(today, 'year') 
        ? today.date() 
        : targetMonth.daysInMonth();

    case 'YYYY-MM-DD':
      const targetDate = dayjs(dateStr);
      const weekStart = today.startOf('week');
      const weekEnd = today.endOf('week');
      // 如果日期在当前周内，返回周内位置（1-7），否则返回7
      return targetDate.isSameOrAfter(weekStart) && targetDate.isSameOrBefore(weekEnd) 
        ? targetDate.day() || 7  // 转换周日(0)为7
        : 7;
  }
}
