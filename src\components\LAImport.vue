<template>
  <el-button v-auth="'upload'" size="default" @click="showImportDialog = true"> 导入 </el-button>
  <el-dialog
    v-model="showImportDialog"
    append-to-body
    class="import-dialog"
    v-bind="{ destroyOnClose: true, ...$attrs }"
    width="598px"
    @close="uploaded = false"
  >
    <template v-if="!uploaded">
      <div class="btn-group">
        <el-upload
          :action="uploadUrl"
          :before-upload="onBeforeUpload"
          :data="params"
          :headers="headers"
          :show-file-list="false"
          accept=".xlsx,.csv,.xls"
          @success="onSuccess"
        >
          <el-button size="large" style="border-radius: 8px; font-weight: 400" type="primary">
            {{ buttonText || $attrs.title }}
          </el-button>
        </el-upload>

        <el-button
          class="download-class"
          plain
          size="large"
          style="background-color: #fff; border-radius: 8px; font-weight: 400"
          type="primary"
          @click="downloadTpl"
        >
          下载模板
        </el-button>
      </div>
      <ul class="description">
        <li v-for="row of description" :key="row" style="margin-bottom: 10px">·&nbsp;&nbsp;{{ row }}</li>
      </ul>
    </template>
    <div v-else class="result">
      <div v-for="item of resultTpl" :key="item.label" :style="{ backgroundColor: item.bgColor }" class="card">
        <span class="label">{{ item.label }}</span>
        <span :style="{ color: item.color }" class="value">
          {{ responseData[item.key] || 0 }}
        </span>
      </div>
      <div v-if="responseData.errorNumber > 0" class="tip">
        <span> 导入错误数据已生成xlsx文件 </span>
        <span>请修改后再次导入</span>
      </div>
    </div>
  </el-dialog>
</template>

<script lang="ts" setup>
/**
 * @file 导入模板
 * <AUTHOR>
 * @date 2024/11/14
 */
import { ElButton, ElDialog, ElUpload, ElLoading } from "element-plus";
import JsFileDownloader from "js-file-downloader";
import { ref, useAttrs } from "vue";
import { useUserStore } from "@/stores/modules/user";
import { utils, writeFileXLSX } from "xlsx";
const showImportDialog = ref(false);
const userStore = useUserStore();
const attrs = useAttrs();
const loading = ref<any>(null);
const uploaded = ref(false);
const emit = defineEmits(["onSuccess"]);
const props = withDefaults(
  defineProps<{
    // 导入按钮内容
    buttonText?: string;
    // 导入描述
    description: string[];
    // 模板下载连接
    downloadUrl: string;
    // 导入上传连接
    uploadUrl: string;
    // 导入需要的参数
    params: object;
  }>(),
  {
    description: () => [
      "Excel表格中每一行会被解析为一条数据",
      "支持导入格式为xlsx的文件",
      "模版中的表头不可更改，不可删除",
      "数据记录不要超过500条"
    ],
    params: () => ({})
  }
);

// 上传情况反馈内容
const resultTpl = [
  {
    label: "总条数",
    color: "#356AFDFF",
    bgColor: "#eaf0ff",
    key: "count"
  },
  {
    label: "成功条数",
    color: "#00C290FF",
    bgColor: "#e5f9f4",
    key: "successNumber"
  },
  {
    label: "失败条数",
    color: "#F25555FF",
    bgColor: "#feeded",
    key: "errorNumber"
  }
];
// 上传结果数据
const responseData: any = ref({});

const headers = {
  Authorization: "Bearer " + userStore.token
};
/** 上传前要执行的钩子*/
const onBeforeUpload = () => {
  loading.value = ElLoading.service({
    lock: true,
    text: "Loading",
    background: "rgba(0, 0, 0, 0.7)"
  });
  return true;
};
/** 数据上传成功*/
const onSuccess = response => {
  if (response.success) {
    const { count, successNumber, errorNumber, errorMessages, tabs } = response.data;

    responseData.value = { count, successNumber, errorNumber };
    if (errorNumber) exportXlsx(errorMessages, tabs);
  }
  emit("onSuccess", response);
  uploaded.value = true;
  loading.value.close();
};
/** 错误数据导出 xlsx*/
const exportXlsx = (data, header) => {
  const worksheet = utils.json_to_sheet(data);
  utils.sheet_add_aoa(worksheet, [header], { origin: "A1" });
  const wb = utils.book_new();
  utils.book_append_sheet(wb, worksheet, "Sheet 1");
  writeFileXLSX(wb, `${attrs.title}失败数据.xlsx`);
};
// 下载模板
const downloadTpl = () => {
  new JsFileDownloader({
    url: props.downloadUrl,
    method: "POST",
    filename: `${attrs.title}.xlsx`,
    headers: [{ name: "Authorization", value: headers.Authorization }]
  });
};
</script>

<style lang="scss" scoped>
.description {
  margin-top: 20px;
  margin-bottom: 50px;
  padding-left: 0;
  list-style-type: none;
  color: rgba(101, 102, 102, 1);
}
.btn-group {
  display: flex;
  gap: 8px;
}
.result {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  .card {
    overflow-x: initial;
    border: none;
    box-shadow: none;
    font-weight: bold;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    height: 80px;
    border-radius: 8px;
    .label {
      color: rgba(26, 26, 26, 1);
    }
    .value {
      font-size: 20px;
    }
  }
  .tip {
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    height: 78px;
    color: rgba(242, 85, 85, 1);
    border: 1px dashed #cad9fc;
    border-radius: 8px;
    grid-column: span 3;
    gap: 4px;
  }
}
</style>
<style lang="scss">
.import-dialog {
  .download-class {
    --el-color-white: var(--el-color-primary);
  }
  border-radius: 16px !important;
  .el-dialog__headerbtn {
    height: 58px;
  }
  .el-dialog__title {
    font-weight: bold;
  }
  .el-dialog__header {
    border-bottom: 1px solid #ebedf1;
  }
}
</style>
