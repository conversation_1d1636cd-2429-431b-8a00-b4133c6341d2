import { dayjs } from "element-plus/es";
// 禁用今天之前的日期
export const disabledBeforeDay = (date: Date) => {
  return dayjs(date).isBefore(dayjs().startOf("day"));
};
// 公斤转吨
export const kgToTon = (kg: number, frac = 3) => (kg / 1000).toFixed(frac);

// 对象合并子对象
export const mergeNestObj = (obj: { [key: string]: any }, nestObjKey: string, useParentKeys = []) => {
  if (!nestObjKey || !obj[nestObjKey]) return obj;
  const newObj = { ...obj };
  try {
    let nestObj = {};
    if (typeof obj[nestObjKey] === "string") {
      nestObj = JSON.parse(obj[nestObjKey]);
    }
    Object.assign(newObj, nestObj);
    for (const key of useParentKeys) {
      newObj[key] = obj[key];
    }
    return newObj;
  } catch (e) {
    console.error("数据异常：", e);
    return obj;
  }
};

// 对象合并子对象
export const mergeNestObject = (obj: { [key: string]: any }, nestObjKeys: string[] | string, useParentKeys = []) => {
  if (!nestObjKeys || nestObjKeys.length === 0) return obj;
  nestObjKeys = Array.isArray(nestObjKeys) ? nestObjKeys : [nestObjKeys];

  const newObj = { ...obj };
  try {
    let nestObj = {};
    for (const nestObjKey of nestObjKeys) {
      if (typeof obj[nestObjKey] === "string") {
        nestObj = JSON.parse(obj[nestObjKey]);
      }
    }
    Object.assign(newObj, nestObj);
    for (const key of useParentKeys) {
      newObj[key] = obj[key];
    }
    return newObj;
  } catch (e) {
    console.error("数据异常：", e);
    return obj;
  }
};

// 显示值
export const showValue = (value: any, enums = {}, defaultValue = "-") => {
  if (value === undefined || value === null) return defaultValue;
  return enums[value] || value;
};

// 显示值后缀
export const showValueSuffix = (value: any, suffix: string) => {
  const result = showValue(value);
  if (result === "-") return result;
  return `${result}${suffix}`;
};
