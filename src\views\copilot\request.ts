import { useAsyncStoreManager, useDeviceSelection } from "./store";
import type { Ref } from "vue";
import {
  // 设备控制接口
  getDeviceDetailByMap,
  getDeviceWingSection,
  changeTrainLantern,
  pauseOrContinueTrain,
  mineTrainClearFault,
  mineTrainBackTask,
  mineTrainChargeTask,
  cancelMineTrainTask,
  // 紧急控制接口
  stopAllMineTrain,
  continueAllMineTrain,
  finishAllProject,
  // 统计数据接口
  getDispatchTaskCountStatistics,
  getDispatchTaskWeightStatistics,
  // 调度管理接口
  getDispatchFaultsList,
  getDispatchObstacleList,
  getDispatchLogsList,
  getDispatchTaskListByMap
} from "@/api/modules/copilot";

const { register, getStore, refreshAll } = useAsyncStoreManager();

// 创建store key生成器
const createStoreKey = (prefix: string, params: Record<string, any>) => {
  const paramsKey = JSON.stringify(params);
  return `${prefix}:${paramsKey}`;
};

// ================ 设备信息数据源 ================
export const useDeviceDetail = <T = any>(params: Record<string, any> = {}) => {
  const storeKey = createStoreKey("deviceDetail", params);
  const { selectedDeviceId, selectedDeviceType, updateDeviceInfo } = useDeviceSelection();

  if (!getStore(storeKey, false)) {
    register(storeKey, {
      fetcher: async () => {
        // 如果未选中设备，则返回空
        if (!selectedDeviceId.value) {
          return null;
        }
        const response = await getDeviceDetailByMap({
          ...params,
          deviceId: selectedDeviceId.value,
          deviceType: selectedDeviceType.value
        });
        // 自动更新设备信息
        if (response && selectedDeviceId.value) {
          updateDeviceInfo({
            id: selectedDeviceId.value,
            ...response
          });
        }
        return (response as ResultData<T>).data;
      },
      refreshInterval: 0,
      immediate: true,
      deviceId: selectedDeviceId.value,
      deviceType: selectedDeviceType.value
    });
  }

  return getStore(storeKey) as {
    data: Ref<T>;
    error: Ref<Error | null>;
    isLoading: Ref<boolean>;
    isStale: Ref<boolean>;
    lastUpdated: Ref<Date | null>;
    isEnabled: Ref<boolean>;
    refresh: () => Promise<void>;
    pauseAutoRefresh?: () => void;
    resumeAutoRefresh?: () => void;
  };
};

// 获取任务队列
export const useTaskQueue = <T = any>(params: Record<string, any> = {}) => {
  const storeKey = createStoreKey("taskQueue", params);

  if (!getStore(storeKey, false)) {
    register(storeKey, {
      fetcher: async () => {
        const response = await getDispatchTaskListByMap(params);
        return (response as ResultData<{ data: T[] }>).data;
      },
      refreshInterval: 30000,
      immediate: true
    });
  }

  return getStore(storeKey) as {
    data: Ref<{ data: T[] }>;
    error: Ref<Error | null>;
    isLoading: Ref<boolean>;
    isStale: Ref<boolean>;
    lastUpdated: Ref<Date | null>;
    isEnabled: Ref<boolean>;
    refresh: () => Promise<void>;
    pauseAutoRefresh?: () => void;
    resumeAutoRefresh?: () => void;
  };
};

export const useDeviceWingSection = <T = any>(params: Record<string, any> = {}) => {
  const storeKey = createStoreKey("deviceWingSection", params);

  if (!getStore(storeKey, false)) {
    register(storeKey, {
      fetcher: async () => {
        const response = await getDeviceWingSection(params);
        return (response as ResultData<T>).data;
      },
      refreshInterval: 5000,
      immediate: true
    });
  }

  return getStore(storeKey) as {
    data: Ref<T>;
    error: Ref<Error | null>;
    isLoading: Ref<boolean>;
    isStale: Ref<boolean>;
    lastUpdated: Ref<Date | null>;
    isEnabled: Ref<boolean>;
    refresh: () => Promise<void>;
    pauseAutoRefresh?: () => void;
    resumeAutoRefresh?: () => void;
  };
};

// ================ 统计数据源 ================
export const useDispatchStatistics = <T = any>(params: Record<string, any> = {}) => {
  const storeKey = createStoreKey("dispatchStatistics", params);
  if (!getStore(storeKey, false)) {
    register(storeKey, {
      fetcher: async () => {
        const [taskCountResponse, weightStatsResponse] = await Promise.all([
          getDispatchTaskCountStatistics(params),
          getDispatchTaskWeightStatistics(params)
        ]);
        return {
          taskCount: (taskCountResponse as ResultData<any>).data,
          weightStats: (weightStatsResponse as ResultData<any>).data
        } as T;
      },
      refreshInterval: 60000,
      immediate: true
    });
  }

  return getStore(storeKey) as {
    data: Ref<T>;
    error: Ref<Error | null>;
    isLoading: Ref<boolean>;
    isStale: Ref<boolean>;
    lastUpdated: Ref<Date | null>;
    isEnabled: Ref<boolean>;
    refresh: () => Promise<void>;
    pauseAutoRefresh?: () => void;
    resumeAutoRefresh?: () => void;
  };
};

// ================ 调度管理数据源 ================
interface PaginationParams {
  current?: number;
  size?: number;

  [key: string]: any;
}

interface ResultData<T> {
  success: boolean;
  data: T;
  statusCode: number;
  message: string;
  jwtToken: string | null;
}

interface PaginationResponse<T> {
  records: T[];
  total: number;
  size: number;
  current: number;
  pages: number;
}

// 获取指定设备的故障列表
export const useDeviceDispatchFaults = <T = any>(params: PaginationParams = { current: 1, size: 100 }) => {
  const storeKey = createStoreKey("deviceDispatchFaults", params);
  const { selectedDeviceId, selectedDeviceType } = useDeviceSelection();

  if (!getStore(storeKey, false)) {
    register(storeKey, {
      fetcher: async () => {
        const response = await getDispatchFaultsList({
          ...params,
          deviceId: selectedDeviceId.value
        });
        return (response as ResultData<PaginationResponse<T>>).data;
      },
      refreshInterval: 30000,
      immediate: true,
      deviceId: selectedDeviceId.value
    });
  }

  return getStore(storeKey) as {
    data: Ref<PaginationResponse<T>>;
    error: Ref<Error | null>;
    isLoading: Ref<boolean>;
    isStale: Ref<boolean>;
    lastUpdated: Ref<Date | null>;
    isEnabled: Ref<boolean>;
    refresh: () => Promise<void>;
    pauseAutoRefresh?: () => void;
    resumeAutoRefresh?: () => void;
  };
};

// 获取所有设备的故障列表
export const useAllDispatchFaults = <T = any>(params: PaginationParams = { current: 1, size: 10 }) => {
  const storeKey = createStoreKey("allDispatchFaults", params);

  if (!getStore(storeKey, false)) {
    register(storeKey, {
      fetcher: async () => {
        const response = await getDispatchFaultsList(params);
        return (response as ResultData<PaginationResponse<T>>).data;
      },
      refreshInterval: 30000,
      immediate: true
    });
  }

  return getStore(storeKey) as {
    data: Ref<PaginationResponse<T>>;
    error: Ref<Error | null>;
    isLoading: Ref<boolean>;
    isStale: Ref<boolean>;
    lastUpdated: Ref<Date | null>;
    isEnabled: Ref<boolean>;
    refresh: () => Promise<void>;
    pauseAutoRefresh?: () => void;
    resumeAutoRefresh?: () => void;
  };
};

// 获取障碍物信息
export const useDispatchObstacle = <T = any>() => {
  const params = { status: 0 };
  const storeKey = createStoreKey("dispatchObstacle", params);

  if (!getStore(storeKey, false)) {
    register(storeKey, {
      fetcher: async () => {
        const response = await getDispatchObstacleList(params);
        return (response as ResultData<PaginationResponse<T>>).data;
      },
      refreshInterval: 30000,
      immediate: true
    });
  }

  return getStore(storeKey) as {
    data: Ref<T>;
    error: Ref<Error | null>;
    isLoading: Ref<boolean>;
    isStale: Ref<boolean>;
    lastUpdated: Ref<Date | null>;
    isEnabled: Ref<boolean>;
    refresh: () => Promise<void>;
    pauseAutoRefresh?: () => void;
    resumeAutoRefresh?: () => void;
  };
};

// 获取指定设备的日志列表
export const useDeviceDispatchLogs = <T = any>(params: PaginationParams = { current: 1, size: 10 }) => {
  const storeKey = createStoreKey("deviceDispatchLogs", params);
  const { selectedDeviceId } = useDeviceSelection();

  if (!getStore(storeKey, false)) {
    register(storeKey, {
      fetcher: async () => {
        const response = await getDispatchLogsList({
          ...params,
          deviceId: selectedDeviceId.value
        });
        return (response as ResultData<PaginationResponse<T>>).data;
      },
      refreshInterval: 30000,
      immediate: true,
      deviceId: selectedDeviceId.value
    });
  }

  return getStore(storeKey) as {
    data: Ref<PaginationResponse<T>>;
    error: Ref<Error | null>;
    isLoading: Ref<boolean>;
    isStale: Ref<boolean>;
    lastUpdated: Ref<Date | null>;
    isEnabled: Ref<boolean>;
    refresh: () => Promise<void>;
    pauseAutoRefresh?: () => void;
    resumeAutoRefresh?: () => void;
  };
};

// 导出控制方法
export const useDeviceControl = () => {
  const { selectedDeviceId } = useDeviceSelection();
  return {
    // 远程控制矿卡开关灯
    changeLantern: (params?: any) =>
      changeTrainLantern({
        ...params,
        trainId: selectedDeviceId.value
      }),
    // 一键紧急停止
    stopAll: (params: any) => stopAllMineTrain({ ...params }),
    // 一键恢复
    continueAll: (params?: any) => continueAllMineTrain({ ...params }),
    // 一键收车
    finishAll: (params?: any) => finishAllProject({ ...params }),
    // 暂停-继续矿卡
    pauseOrContinue: (params?: any) => pauseOrContinueTrain({ ...params, trainId: selectedDeviceId.value }),
    // 一键清除故障
    clearFault: (params?: any) => mineTrainClearFault({ ...params, deviceId: selectedDeviceId.value }),
    // 一键回场
    backTask: (params?: any) => mineTrainBackTask({ ...params, trainId: selectedDeviceId.value }),
    // 一键充电
    chargeTask: (params?: any) => mineTrainChargeTask({ ...params, trainId: selectedDeviceId.value }),
    // 取消矿车任务
    cancelTask: (params?: any) => cancelMineTrainTask({ ...params, trainId: selectedDeviceId.value })
  };
};

// 导出设备选择方法
export { useDeviceSelection };

// 导出刷新方法
export { refreshAll };
