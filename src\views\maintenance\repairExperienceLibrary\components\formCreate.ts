/**
 * @file 维修保养-维修经验-表单文件
 * <AUTHOR>
 * @date 2025/2/12
 */

import LASelect from "@/components/LASelect.tsx";
import { getRepairExperienceModel } from "@/api/modules/repair.ts";
import { formMap } from "@/views/maintenance/repairExperienceLibrary/types.ts";
import type { Rule, Api } from "@form-create/element-ui"; // TypeScript 类型导入
import LAEditor from "@/components/LAEditor/index.vue";

// 富文本编辑器
export const EditorFormCreate = {
  type: "form-create",
  inject: true,
  props: {
    rule: [
      {
        component: LAEditor,
        field: "repairExperienceEditor"
      }
    ],
    option: {
      submitBtn: false
    }
  }
};
// 矿车维修经验表单
export const repairFormCreate = {
  type: "form-create",
  on: {
    change: (field: string, value: any, rule: Rule, api: Api, setFlag: boolean) => {
      // console.log("change", field, value, rule, api, setFlag);
      if (field === "faultDescription") {
        api.findRule({ name: "repairExperience" }).props.dialog.title = value;
      }
      // 表单状态更改
      if (field === "repairExperience") {
        api.validateField("repairExperience");
      }
      // validateField;
    }
  },
  props: {
    rule: [
      {
        component: LASelect,
        field: formMap.get("设备型号"),
        title: "矿卡型号",
        props: {
          fetch: getRepairExperienceModel,
          params: {
            deviceType: "deviceMineTrain"
          },
          replaceFields: { key: "code", label: "name", value: "code" },
          placeholder: ""
        },
        validate: [{ required: true, message: "请选择矿卡型号" }]
      },
      {
        type: "input",
        field: formMap.get("故障码"),
        title: "故障码",
        validate: [
          { required: true, message: "请输入故障码" },
          {
            validator: (rule, value) => !/[\u4e00-\u9fa5]/.test(value),
            message: "故障码不能包含中文"
          }
        ]
      },
      {
        type: "input",
        field: formMap.get("故障描述"),

        validate: [
          { required: true, message: "请输入故障描述" },
          {
            pattern: /^.{1,200}$/,
            message: "故障描述最多200个字符"
          }
        ],
        title: "故障描述"
      },
      {
        type: "EditBtn",
        name: "repairExperience",
        field: formMap.get("维修经验"),
        inject: true,
        props: {
          dialog: {
            title: "电流温度传稿器",
            style: {
              width: "1200px"
            }
          },
          btn: {
            icon: "edit",
            content: "编辑",
            style: {
              color: "#666",
              width: "100%",
              border: "1px solid var(--el-border-color)",
              height: "40px"
            },
            show: true
          },
          isCustomDialog: true,
          formatter: injectApi => {
            // injectApi.api.findRule({field: formMap.get("维修经验")}).title = injectApi.api.formData().faultDescription;
            // console.log(88, injectApi.api.findRule({field: formMap.get("维修经验")}), injectApi.api.formData());
            // 回显富文本的值
            injectApi.api.findRule({ field: formMap.get("维修经验") }).children[0].props.rule[0].value =
              injectApi.api.formData().repairExperience;
            console.log(7777777777, injectApi.api.formData());
          },
          submitRequest: (formCreateInject, data) => {
            console.log("维修经验", data.repairExperienceEditor);
            // 提交时设置富文本内容
            formCreateInject.api.setValue(formMap.get("维修经验"), data.repairExperienceEditor);
            // 提交的请求
            return Promise.resolve(true);
          }
        },
        validate: [{ required: true, message: "请填写维修经验" }],
        title: "维修经验",
        children: [EditorFormCreate]
      }
    ],
    option: {
      submitBtn: false,
      onSubmit(formData, api) {
        // 通知 table 搜索数据变化，刷新数据
        api.top.children[0].bus.$emit("searchFormChanged");
      }
    }
  }
};
// 充电桩维修经验表单
export const chargeFormCreate = {
  type: "form-create",
  on: {
    change: (field: string, value: any, rule: Rule, api: Api, setFlag: boolean) => {
      // console.log("change", field, value, rule, api, setFlag);
      if (field === "faultDescription") {
        api.findRule({ name: "repairExperience" }).props.dialog.title = value;
      }
      // 表单状态更改
      if (field === "repairExperience") {
        api.validateField("repairExperience");
      }
      // validateField;
    }
  },
  props: {
    rule: [
      {
        component: LASelect,
        field: formMap.get("设备型号"),
        title: "充电桩型号",
        props: {
          fetch: getRepairExperienceModel,
          params: {
            deviceType: "deviceChargingPile"
          },
          replaceFields: { key: "code", label: "name", value: "code" },
          placeholder: ""
        },
        validate: [{ required: true, message: "请选择充电桩型号" }]
      },
      {
        type: "input",
        field: formMap.get("故障码"),
        title: "故障码",
        validate: [{ required: true, message: "请输入故障码" }]
      },
      {
        type: "input",
        field: formMap.get("故障描述"),
        validate: [{ required: true, message: "请输入故障描述" }],
        title: "故障描述"
      },
      {
        type: "EditBtn",
        field: formMap.get("维修经验"),
        name: "repairExperience",
        inject: true,
        props: {
          dialog: {
            title: "电流温度传稿器",
            style: {
              width: "1200px"
            }
          },
          btn: {
            icon: "edit",
            content: "编辑",
            style: {
              color: "#666",
              width: "100%",
              border: "1px solid var(--el-border-color)",
              height: "40px"
            },
            show: true
          },
          isCustomDialog: true,
          formatter: injectApi => {
            // 回显富文本的值
            injectApi.api.findRule({ field: formMap.get("维修经验") }).children[0].props.rule[0].value =
              injectApi.api.formData().repairExperience;
          },
          submitRequest: (formCreateInject, data) => {
            // 提交时设置富文本内容
            formCreateInject.api.setValue(formMap.get("维修经验"), data.repairExperienceEditor);
            // 提交的请求
            return Promise.resolve(true);
          }
        },
        validate: [{ required: true, message: "请填写维修经验" }],
        title: "维修经验",
        children: [EditorFormCreate]
      }
    ],
    option: {
      submitBtn: false,
      onSubmit(formData, api) {
        // 通知 table 搜索数据变化，刷新数据
        api.top.children[0].bus.$emit("searchFormChanged");
      }
    }
  }
};

// 新增用户经验记录
export const userExperienceFormCreate = {
  type: "form-create",
  props: {
    option: {
      submitBtn: false,
      onSubmit(formData, api) {
        // 通知 table 搜索数据变化，刷新数据
        // api.top.bus.$emit("searchFormChanged");
      }
    },
    rule: [
      {
        type: "input",
        field: "record",
        title: "维修经验",
        props: {
          type: "textarea"
        },
        validate: [{ required: true, message: "请输入维修经验" }]
      }
    ]
  }
};
// 编辑用户经验记录
export const userEditExperienceFormCreate = {
  type: "form-create",

  props: {
    option: {
      submitBtn: false,
      onSubmit(formData, api) {
        // console.log(formData);
        // 通知 table 搜索数据变化，刷新数据
        // api.top.bus.$emit("searchFormChanged");
      }
    },
    rule: [
      {
        type: "input",
        field: "record",
        title: "维修经验",
        props: {
          type: "textarea"
        },
        validate: [{ required: true, message: "请输入维修经验" }]
      }
    ]
  }
};
