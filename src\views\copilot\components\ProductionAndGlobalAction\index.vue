<template>
  <div class="production-global-action">
    <div class="card-container production">
      <el-text size="large" tag="b">生产信息</el-text>
      <LineChart />
    </div>
    <div class="grid">
      <div
        class="card-container button"
        v-for="action of actions"
        :key="action.key"
        :style="action.style"
        @click="action.onClick"
      >
        {{ action.text }}
      </div>
    </div>
  </div>
  <MessageBox
    ref="messageBoxRef"
    v-bind="messageProps"
    :beforeClose="handleBeforeClose"
    @confirm="handleConfirm"
    @cancel="handleCancel"
  />
</template>

<script lang="ts" setup>
import MessageBox from "@/views/copilot/components/MessageBox.vue";
import { ref } from "vue";
import { useDeviceControl } from "@/views/copilot/request";
import LineChart from "./LineChart.vue";
const messageBoxRef = ref();
const messageProps = ref({ title: "", message: "" });
const { stopAll, continueAll, finishAll } = useDeviceControl();

// 当前选中的操作
const currentAction = ref<string>("");

const actions = [
  {
    key: "finish",
    text: "一键全部收车",
    style: "grid-column: span 2",
    onClick() {
      currentAction.value = "finish";
      messageProps.value = {
        title: "是否一键全部收车",
        message: "一键全部收车后，所有执行中的任务将变成已结束状态。所有正在执行任务的矿卡执行完当次任务后，会回到停车位。"
      };
      showMessageBox();
    }
  },
  {
    key: "stop",
    text: "一键紧急停车",
    onClick() {
      currentAction.value = "stop";
      messageProps.value = {
        title: "是否一键紧急停车",
        message: "一键紧急停车后，所有车辆将停止行驶"
      };
      showMessageBox();
    }
  },
  {
    key: "continue",
    text: "一键全部恢复",
    onClick() {
      currentAction.value = "continue";
      messageProps.value = {
        title: "是否一键全部恢复",
        message: "一键全部恢复后，所有车辆将继续行驶"
      };
      showMessageBox();
    }
  }
];

// 打开对话框
const showMessageBox = () => {
  messageBoxRef.value.show();
};

// 关闭前检查
const handleBeforeClose = async () => {
  return true;
};

// 确认回调
const handleConfirm = async () => {
  try {
    switch (currentAction.value) {
      case "finish":
        await finishAll({});
        break;
      case "stop":
        await stopAll({});
        break;
      case "continue":
        await continueAll({});
        break;
    }
  } catch (error) {
    console.error("操作失败:", error);
  }
};

// 取消回调
const handleCancel = () => {
  currentAction.value = "";
};
</script>

<style lang="scss" scoped>
.production-global-action {
  width: 272px;
  height: 100%;
  display: flex;
  flex-direction: column;
  flex: none;
  gap: 8px;
}
.production {
  box-sizing: border-box;
  padding: 16px;
  display: flex;
  flex: 1;
  flex-direction: column;
}
.grid {
  display: grid;
  gap: 4px;
  grid-template-columns: repeat(2, 1fr);
  grid-auto-flow: dense;
}
.button {
  line-height: 32px;
  height: 32px;
  text-align: center;
  cursor: pointer;
}
</style>
