<template>
  <el-dropdown trigger="click">
    <div class="tool-bar-ri">
      <div class="avatar">
        <img alt="avatar" src="@/assets/images/avatar.gif" />
      </div>
      <!--    <div class="header-icon">-->
      <span class="username">{{ employeeName }}</span>
      <el-icon class="arrow-down"> <ArrowDown /></el-icon>
      <!--    </div>-->
    </div>
    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item @click="push({ name: 'AccountCenter' })">
          <el-icon><User /></el-icon>账号中心
        </el-dropdown-item>
        <el-dropdown-item @click="logout">
          <el-icon><SwitchButton /></el-icon>退出登录
        </el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
  <!-- infoDialog -->
  <InfoDialog ref="infoRef"></InfoDialog>
  <!-- passwordDialog -->
  <PasswordDialog ref="passwordRef"></PasswordDialog>
</template>

<script lang="ts" setup>
import { computed, ref } from "vue";
import { useUserStore } from "@/stores/modules/user";
import { LOGIN_URL } from "@/config";
import { useRouter } from "vue-router";
import { logoutApi } from "@/api/modules/login";
import { ElMessageBox, ElMessage } from "element-plus";
import InfoDialog from "./components/InfoDialog.vue";
import PasswordDialog from "./components/PasswordDialog.vue";
import { ArrowDown } from "@element-plus/icons-vue";

const router = useRouter();
const { push } = useRouter();

const userStore = useUserStore();
// 获取个人名称
const employeeName = computed(() => userStore.userInfo.employeeName);
// 退出登录
const logout = () => {
  ElMessageBox.confirm("您是否确认退出登录?", "温馨提示", {
    confirmButtonText: "确定",
    buttonSize: "default",
    cancelButtonText: "取消",
    center: true,
    customClass: "confirmMessageStyle",
    type: "warning"
  }).then(async () => {
    // 1.执行退出登录接口
    await logoutApi();

    // 2.清除 Token、清除信息
    userStore.resetState();
    // 3.重定向到登陆页
    await router.replace(LOGIN_URL);
    ElMessage.success("退出登录成功！");
  });
};
// 打开修改密码和个人信息弹窗
const infoRef = ref<InstanceType<typeof InfoDialog> | null>(null);
const passwordRef = ref<InstanceType<typeof PasswordDialog> | null>(null);
</script>

<style lang="scss" scoped>
.tool-bar-ri {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-right: 25px;
  //鼠标手指
  cursor: pointer;
  .header-icon {
    display: flex;
    align-items: center;
    & > * {
      margin-left: 21px;
      color: var(--el-header-text-color);
    }
  }
  .arrow-down {
    color: var(--el-header-text-color);
  }
  .username {
    margin: 0 4px;
    font-size: 14px;
    color: var(--el-header-text-color);
  }
}
.avatar {
  width: 24px;
  height: 24px;
  overflow: hidden;
  cursor: pointer;
  border-radius: 50%;
  img {
    width: 100%;
    height: 100%;
  }
}
</style>
