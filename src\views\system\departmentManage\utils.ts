export const departmentFormCreate = {
  type: "form-create",
  props: {
    rule: [
      {
        type: "input",
        field: "pname",
        title: "上级部门",
        props: {
          disabled: true
        }
      },
      {
        type: "input",
        field: "fullName",
        title: "部门名称",
        validate: [
          { required: true, message: "请输入部门名称" },
          {
            pattern: /^.{1,20}$/,
            message: "字符限长20位"
          }
        ]
      }
    ],
    option: {
      submitBtn: false,
      form: {
        labelWidth: "85px"
      },
      onSubmit(formData, api) {
        // 通知 tree 搜索数据变化，刷新数据
        api.top.exec("v:tree", "fetchData");
      }
    }
  }
};
