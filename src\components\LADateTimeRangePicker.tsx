/**
 * @file 项目table搜索日期范围组件
 * <AUTHOR>
 * @date 2024/11/15
 */
import { defineComponent, ref, watch } from "vue";
import { dayjs, ElDatePicker } from "element-plus";

// 选中日期后默认时间：当天00：00-23：59
const defaultTime = [new Date(0, 0, 0, 0, 0, 0), new Date(0, 0, 0, 23, 59, 59)];
const shortcuts = [
  // 近一周快捷选择日期
  {
    text: "近一周",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    }
  },
  // 近一个月快捷选择日期
  {
    text: "近一个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    }
  },
  // 近三个月快捷选择日期
  {
    text: "近三个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    }
  },
  // 近半年快捷选择日期
  {
    text: "近半年",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 180);
      return [start, end];
    }
  }
];

export default defineComponent({
  name: "LADateTimeRangePicker",
  props: {
    // 开始时间
    start: {
      type: String
    },
    // 结束时间
    end: {
      type: String
    },
    // 提示文本
    placeholder: {
      type: Array,
      default: () => ["开始日期", "结束日期"]
    },
    // 快捷禁用日期 before: 今天前包含今天， 今天及今天以后，不做限制
    disabledRange: {
      type: String,
      default: "none"
    },
    formCreateInject: {
      type: Object
    }
  },
  // 发送开始时间、结束时间的事件
  emits: ["update:start", "update:end"],
  setup(props, { emit, attrs }) {
    // 表格重置时,重置时间
    props?.formCreateInject?.api.bus.$on("resetForm", () => {
      reset();
    });

    const model = ref([props.start, props.end]);
    // 选中的日期改变，发送开始时间、结束时间的事件
    watch(model, time => {
      emit("update:start", time?.[0]);
      emit("update:end", time?.[1]);
    });
    // 清空时间的方法
    const reset = () => {
      model.value = ["", ""];
    };
    // 外部提供了开始结束时间，重新设置日期组件显示的日期时间
    watch(
      () => [props.start, props.end],
      time => (model.value = time)
    );

    const disabledDate = (time: Date) => {
      switch (props.disabledRange) {
        case "none":
          return false;
        case "before":
          return time.getTime() < +dayjs().startOf("day");
        case "after":
          return time.getTime() > Date.now();
      }
    };
    attrs = Object.assign({}, attrs, { disabledDate });
    return () => (
      <div class="la-date-time-range-picker">
        <ElDatePicker
          v-model={model.value}
          editable={false}
          start-placeholder={props.placeholder[0]}
          end-placeholder={props.placeholder[1]}
          range-separator="-"
          default-time={defaultTime}
          shortcuts={shortcuts}
          type="datetimerange"
          size="large"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          {...attrs}
        />
      </div>
    );
  }
});
