<template>
  <TabsCard :tabs="tabs">
    <template #info>
      <div class="container info">
        <el-descriptions :column="4" size="small" direction="vertical" style="width: 100%">
          <el-descriptions-item
            v-for="item in infos"
            :key="item.key"
            :label="item.label"
            label-class-name="description-label"
            class-name="description-item"
          >
            {{ item.value || "-" }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </template>
    <template #action>
      <div class="container">
        <TruckAction />
        <!-- <div
          class="button"
          v-for="item of actions"
          :key="item.key"
          v-show="item?.visible?.() ?? true"
          :class="{ disabled: item.disabled?.() }"
        >
          {{ item.title }}
        </div> -->
      </div>
    </template>
  </TabsCard>
</template>

<script lang="ts" setup>
/**
 * @file 矿卡信息 矿卡操作
 * <AUTHOR>
 * @date 2024/12/17
 */
import TabsCard from "../../TabsCard.vue";
import TruckAction from "./TruckAction.vue";
import { useDeviceSelection } from "@/views/copilot/store";
import { computed } from "vue";
const props = defineProps<{ data: any }>();
const { selectedDeviceInfo } = useDeviceSelection();

const tabs = computed(() => {
  if (selectedDeviceInfo.value?.rawData?.isActivateTheme) {
    return [{ title: "矿卡信息", key: "info" }];
  }
  return [
    { title: "矿卡信息", key: "info" },
    { title: "矿卡操作", key: "action" }
  ];
});

interface InfoItem {
  key: string;
  label: string;
  value: string | number;
}

// 获取设备数据并添加单位
const getDeviceValue = (key: string, unit: string = ""): string => {
  const value = props.data?.[key];
  if (value === null || value === undefined) return "-";
  return `${value}${unit}`;
};

// 驾驶模式转换
const getDriveMode = (): string => {
  const mode = props.data?.driveMode;
  return mode === 1 ? "手动" : "自动";
};

// 载重模式转换
const getLoadMode = (): string => {
  const mode = props.data?.loadMode;
  return mode === 1 ? "重车" : "空车";
};

// 开关状态转换
const getSwitchStatus = (value: number | null | undefined): string => {
  if (value === null || value === undefined) return "-";
  return value === 1 ? "开启" : "关闭";
};

const infos = computed<InfoItem[]>(() => [
  { key: "drivingMode", label: "驾驶模式", value: getDriveMode() },
  { key: "loadMode", label: "载重模式", value: getLoadMode() },
  { key: "oilDoor", label: "油门", value: `${getDeviceValue("gasPedal", "%")}` },
  { key: "battery", label: "剩余电量", value: `${getDeviceValue("electricQuantity", "%")}` },
  { key: "deceleration", label: "减速度", value: `${getDeviceValue("deceleration", "m/s²")}` },
  { key: "gear", label: "档位", value: getDeviceValue("gearLevel") },
  { key: "brake", label: "手刹", value: getSwitchStatus(props.data?.handbrake) },
  { key: "lift", label: "举升", value: `${getDeviceValue("liftNumber", "°")}` },
  { key: "steering", label: "转向", value: `${getDeviceValue("redirect", "°")}` },
  { key: "speed", label: "速度", value: `${getDeviceValue("speed", "km/h")}` },
  { key: "speed", label: "当日车次", value: `${getDeviceValue("speed", "km/h")}` },
  { key: "speed", label: "当日产量", value: `${getDeviceValue("speed", "km/h")}` },
  { key: "speed", label: "当日充电量", value: `${getDeviceValue("speed", "km/h")}` },
  { key: "speed", label: "累计里程", value: `${getDeviceValue("speed", "km/h")}` },
  { key: "speed", label: "累计运行时间", value: `${getDeviceValue("speed", "km/h")}` }
]);
</script>

<style lang="scss" scoped>
:deep(.description-label) {
  background: transparent !important;
  color: var(--el-text-color-secondary) !important;
  padding-bottom: 0 !important;
  padding-top: 0 !important;
}
:deep(.description-item) {
  padding-bottom: 2px !important;
}
.container {
  padding-top: 16px;
  padding-right: 16px;
  padding-bottom: 40px;
  display: flex;
  align-items: center;
  background: var(--card-bg-color);
  gap: 8px;
}
</style>
