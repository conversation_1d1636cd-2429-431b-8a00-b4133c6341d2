/**
 * @file 对表单的验证规则的设置方法
 * <AUTHOR>
 * @date 2024/10/28
 * */

// 类型说明
type SetRuleRequired = (
  label,
  trigger?
) => {
  required: true;
  message: string;
  trigger: string;
};

/**
 * 设置表单项为必填
 * @param label 表单的label名称
 * @param trigger 验证时机，默认失焦时
 * @return {{trigger: string, message: string, required: true}}
 * <AUTHOR>
 * @date 2024/10/28
 */
export const setRuleRequired: SetRuleRequired = (label, trigger = "blur") => {
  return {
    // 是否必填
    required: true,
    // 不符合规范时的提示信息
    message: `${label}是必填的`,
    // 验证时机 blur为失焦的时候进行验证
    trigger: trigger
  };
};

/**
 * 验证表单项的值为某种固定类型
 * @param label 表单的label名称
 * @param type 可以传入的类型的值 string|number|boolean|method|regexp|integer|float|array|object|date|url|hex|email|any
 * @param customMsg 符合规范时的自定义提示信息，如果不传则使用默认的
 * @returns {{trigger: string, type: any, message: string}}
 * <AUTHOR>
 * @date 2024/10/28
 */
export const setRuleType = (label, type, customMsg = "") => {
  return {
    // 表单值为某种类型
    type: type,
    // 不符合规范时的提示信息
    message: customMsg || `${label}不符合规范`,
    // 验证时机 blur为失焦的时候进行验证
    trigger: "blur"
  };
};

/**
 * 验证表单项的值符合某个正则表达式
 * @param label 表单的label名称
 * @param pattern 正则表达式
 * @param customMsg 不符合规范时的自定义提示信息，如果不传则使用默认的
 * @return {{pattern: any, trigger: string, message: string}}
 * <AUTHOR>
 * @date 2024/10/28
 */
export const setRulePattern = (label, pattern, customMsg = "") => {
  return {
    // 正则表达式
    pattern,
    // 不符合规范时的提示信息
    message: customMsg || `${label}不符合规范`,
    // 验证时机 blur为失焦的时候进行验证
    trigger: "blur"
  };
};

/**
 * 验证表单项的值为某个范围内的数字(允许小数),且不能包含空格
 * @param label 表单的label名称
 * @param customMsg 自定义提示信息
 * @param min 最小值
 * @param max 最大值
 * <AUTHOR>
 * @date 2024/10/28
 * @param isInteger 整数验证
 * @return {{validator: (rule: any, value: any, cb: any) => void, trigger: string}}
 */
export const setRuleNumber = (label, customMsg = "", min = -Infinity, max = Infinity, isInteger = false) => {
  return {
    // 自定义验证函数
    validator: (rule, value, cb) => {
      if (!value) {
        cb();
      } else {
        if (
          String(value).indexOf(" ") !== -1 ||
          isNaN(Number(value)) ||
          Number(value) <= min ||
          Number(value) > max ||
          (isInteger && !Number.isInteger(Number(value)))
        ) {
          cb(customMsg || `${label}不符合规范`);
        } else {
          cb();
        }
      }
    },
    // 验证时机 blur为失焦的时候进行验证
    trigger: "blur"
  };
};
