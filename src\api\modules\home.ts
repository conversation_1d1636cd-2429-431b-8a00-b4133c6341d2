/**
 * @name 综合管理平台-首页-api接口统一出口
 */
import http from "@/api";
// 首页-任务队列
export const getProjectDetailsQueue = () => {
  return http.post("/dispatch-server/dispatch/projectDetails/getProjectDetailsQueue", {}, { loading: false });
};
// 首页-调度任务统计
export const getProjectDetailStatisticsCount = () => {
  return http.post("/dispatch-server/dispatch/projectDetails/getProjectDetailStatisticsCount", {}, { loading: false });
};
// 首页-调度计划状态统计
export const getProjectStatusCount = () => {
  return http.post("/dispatch-server/dispatch/project/getProjectStatusCount", {}, { loading: false });
};
// 首页-查询所有待保养的矿车
export const getAllMaintenanceTrain = () => {
  return http.post("/wcs-server/wcs/deviceMaintenanceWorkOrder/getAllMaintenanceTrain", {}, { loading: false });
};
// 首页-查询所有待保养的工单
export const getAllMaintenanceCount = () => {
  return http.post("/wcs-server/wcs/deviceMaintenanceWorkOrder/getAllMaintenanceCount", {}, { loading: false });
};
// 首页-查询所有待维修的工单
export const getAllRepairCount = () => {
  return http.post("/wcs-server/wcs/deviceRepairWorkOrder/getAllRepairCount", {}, { loading: false });
};
// 首页-查询所有出勤设备
export const getDeviceOnlineStatisticsCount = () => {
  return http.post("/wcs-server/device/mineTrain/getDeviceOnlineStatisticsCount", {}, { loading: false });
};
// 首页-实时故障
export const listDispatchFaultByMap = () => {
  return http.post("/dispatch-server/dispatch/faults/listDispatchFaultByMap", {}, { loading: false });
};
