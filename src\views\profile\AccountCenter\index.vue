<template>
  <div class="container">
    <Profiles :data="userData" @update-user-info="fetchData" />
    <AccountSecurity :data="userData" style="flex: 1" @update-user-info="fetchData" />
  </div>
</template>

<script lang="ts" setup>
/**
 * @file 账号中心模块
 * <AUTHOR>
 * @date 2024/11/13
 */
import Profiles from "./Profiles/index.vue";
import AccountSecurity from "./AccountSecurity.vue";
import { getCurrentDetailUser } from "@/api/modules/user";

import { ref } from "vue";
import { useUserStore } from "@/stores/modules/user";
const userData: any = ref({});
const userStore = useUserStore();

function fetchData() {
  getCurrentDetailUser().then((res: any) => {
    userData.value = res.data;
    userStore.setUserInfo(res.data);
  });
}

fetchData();
</script>

<style lang="scss" scoped>
.container {
  width: 100%;
  height: var(--page-height);
  display: flex;
  gap: 4px;
}
</style>
