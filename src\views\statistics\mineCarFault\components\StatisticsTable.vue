<template>
  <div class="statistics-table">
    <el-radio-group size="default" @change="onTypeChange" v-model="currentType">
      <el-radio-button v-for="item in selectOpt" :label="item.label" :value="item.value" />
    </el-radio-group>
    <el-table :data="tableData" height="500" show-summary header-cell-class-name="table-header-cell">
      <template #empty>
        <PlaceholderImage />
      </template>
      <el-table-column v-for="item in tableFields" :prop="item.prop" :label="item.label">
        <template v-if="['环比', '同比'].includes(item.label)" #default="scope">
          <div style="color: #f25555" v-if="scope.row[item.prop] >= 0">
            <span style="margin-right: 4px; font-size: 10px">▲</span>
            <span>{{ Math.abs(scope.row[item.prop])?.toFixed(2) ?? 0 }}%</span>
          </div>
          <div style="color: #00c290" v-else>
            <span style="margin-right: 4px; font-size: 10px">▼</span>
            <span>{{ Math.abs(scope.row[item.prop])?.toFixed(2) ?? 0 }}%</span>
          </div>
        </template>
        <template v-else-if="item.format" #default="scope">
          {{ item.format(scope.row) }}
        </template>
        <template v-else #default="scope">
          {{ scope.row[item.prop] ?? "-" }}
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination">
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 25, 50, 100]"
        :total="pagination.total"
        layout="total, prev, pager, next, sizes, jumper"
        size="default"
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, inject, watch, type ComputedRef } from "vue";
import PlaceholderImage from "@/components/PlaceholderImage.vue";
import { SearchParams } from "../index.vue";

const searchParams = inject<ComputedRef<SearchParams>>("searchParams");

const props = defineProps({
  api: { type: Function, required: true }
});

const pagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0
});
const currentType = ref("car");
const selectOpt = [
  { label: "车辆", value: "car", tableLabel: "矿车名称" },
  { label: "故障码", value: "faultsCode", tableLabel: "故障码" }
];

const tableFields = ref([
  { label: "日期", prop: "date" },
  { label: "矿车名称", prop: "name" },
  { label: "故障次数(次)", prop: "faultsCount" },
  { label: "环比", prop: "countMom" },
  { label: "同比", prop: "countYoy" },
  { label: "故障时长(小时)", prop: "faultDuration" },
  { label: "环比", prop: "durationMom" },
  { label: "同比", prop: "durationYoy" }
]);

const getData = async () => {
  const params = {
    ...searchParams?.value,
    tableType: currentType.value,
    current: pagination.value.currentPage,
    size: pagination.value.pageSize
  };
  try {
    const res = await props?.api(params);
    if (!res.success) return;
    tableData.value = res.data.records;
    pagination.value.total = res.data.total;
  } catch (error) {}
};

watch(
  () => searchParams?.value,
  newParams => newParams && getData(),
  { deep: true, immediate: true }
);

const onTypeChange = (value: string) => {
  const targetLabel = selectOpt.find(item => item.value === value)?.tableLabel!;
  for (const x of tableFields.value) {
    if (x.prop === "name") {
      x.label = targetLabel;
      break;
    }
  }
  pagination.value.currentPage = 1;
  pagination.value.pageSize = 10;
  getData();
};

const tableData = ref([]);
const handleSizeChange = (val: number) => {
  getData();
};
const handleCurrentChange = (val: number) => {
  getData();
};
</script>
<style lang="scss" scoped>
.pagination {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
.el-table {
  width: 100%;
  margin-top: 10px;
  border: 1px solid #ebeef5;
  :deep(thead) {
    color: #1a1a1a;
  }
  :deep(.table-header-cell) {
    background: #f5f6f8 !important;
  }
}
</style>
