<template>
  <div class="card-container">
    <div style="display: flex; align-items: center; gap: 4px; margin: auto">
      <SvgIcon name="IconCharging1" icon-style="width:20px; height:20px" :color="'var(--el-color-primary)'" />
      <el-text size="large" tag="b">破碎站-{{ selectedDeviceInfo?.rawData?.name }}</el-text>
    </div>

    <div
      style="
        overflow: hidden;
        margin-top: 14px;
        padding-top: 2px;
        display: flex;
        background-color: var(--el-bg-color);
        gap: 2px;
        flex: 1;
      "
    >
      <!-- 实时故障 -->
      <TabsCard :tabs="[{ key: 'info', title: '基本信息' }]" style="flex: 1">
        <template #info>
          <!-- <el-empty image-size="0" style="height: 245px; background-color: var(--card-bg-color)" /> -->
          <el-descriptions
            :column="2"
            size="small"
            direction="vertical"
            style="width: 100%; background-color: var(--card-bg-color); margin-top: 16px"
          >
            <el-descriptions-item
              v-for="item in infos"
              :key="item.key"
              :label="item.label"
              label-class-name="description-label"
              class-name="description-item"
            >
              {{ item.value || "-" }}
            </el-descriptions-item>
          </el-descriptions>
        </template>
      </TabsCard>
      <!-- 运行日志 -->
      <TabsCard :tabs="[{ key: 'log', title: '运行日志' }]" style="flex: 1">
        <template #log>
          <el-empty image-size="0" style="height: 245px; background-color: var(--card-bg-color)" />
        </template>
      </TabsCard>
    </div>
  </div>
</template>
<script lang="ts" setup>
// 挖机
import TabsCard from "../../TabsCard.vue";
import SvgIcon from "@/components/SvgIcon/index.vue";
import { useDeviceSelection } from "@/views/copilot/request";
const { selectedDeviceInfo } = useDeviceSelection()!;

interface InfoItem {
  key: string;
  label: string;
  value: string | number;
}
const infos: InfoItem[] = [
  { key: "truckId", label: "破碎站模式", value: "-" },
  { key: "connectionStatus", label: "卸矿卡车编号", value: "-" },
  { key: "startChargingTime", label: "允许卸矿最低安全深度", value: "-" },
  { key: "preChargingLevel", label: "矿卡举升状态", value: "-" },
  { key: "currentChargingLevel", label: "实时料位深度", value: "-" }
];
</script>

<style lang="scss" scoped>
.card-container {
  flex: 1;
  display: flex;
  height: 100%;
  flex-direction: column;
  box-sizing: border-box;
  padding: 16px;
}
:deep(.description-label) {
  background: transparent !important;
  color: var(--el-text-color-secondary) !important;
  width: 50%;
}
:deep(.description-item) {
  padding-bottom: 2px !important;
}
</style>
