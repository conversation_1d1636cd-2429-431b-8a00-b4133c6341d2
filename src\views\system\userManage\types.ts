/**
 * @file 系统管理-用户管理-类型声明文件
 * <AUTHOR>
 * @date 2024/11/12
 */

// 新增/修改表单对应字段
export const formMap: any = new Map([
  ["真实姓名", "employeeName"],
  ["所属部门", "systemOrgId"],
  ["所属部门名称", "orgName"],
  ["职位", "job"],
  ["账号", "userName"],
  ["手机号码", "mobile"],
  ["角色", "roleName"],
  ["角色id", "roleId"],
  ["状态", "status"],
  ["类型", "type"]
]);

// table对应字段
export const columnMap: any = new Map([
  ["真实姓名", "employeeName"],
  ["所属部门", "orgName"],
  ["职位", "job"],
  ["账号", "userName"],
  ["手机号码", "mobile"],
  ["角色", "roleName"],
  ["状态", "status"],
  ["类型", "type"]
]);

export enum Status {
  // 正常
  NORMAL = "100",
  // 已冻结
  FREEZE = "101",
  // 待激活
  PENDING_ACTIVATION = "102",
  // 密码待改
  PASSWORD_CHANGE = "103"
}

export const statusEnum = [
  {
    bg: "rgba(0, 194, 144, .1)",
    status: Status.NORMAL,
    color: "rgba(0, 194, 144, 1)",
    text: "正常"
  },
  {
    bg: "rgba(241, 242, 242, 1)",
    status: Status.FREEZE,
    color: "rgba(101, 102, 102, 1)",
    text: "已冻结"
  },
  {
    bg: "rgba(0, 209, 217, .1)",
    status: Status.PENDING_ACTIVATION,

    color: "rgba(0, 209, 217, 1)",
    text: "待激活"
  },
  {
    bg: "rgba(86, 137, 254, .1)",
    status: Status.PASSWORD_CHANGE,
    color: "rgba(119, 102, 249, 1)",
    text: "密码待改"
  }
];
