<template>
  <form-create v-model:api="fApi" :option="option" :rule="rule"></form-create>
</template>
<script lang="tsx" setup>
/**
 * @file 系统管理-操作日志-登录日志
 * <AUTHOR>
 * @date 2024/11/14
 */
import { ref } from "vue";
import { getLoginLogList } from "@/api/modules";

import { loginLogColumnMap } from "../types";
import formCreate from "@form-create/element-ui";
import LASelect from "@/components/LASelect";
const fApi = ref();
const option = {
  form: { inline: true },
  resetBtn: false,
  submitBtn: false
};

const rule = ref([
  {
    type: "SearchFormOperation",
    field: "v:search",
    wrap: { style: "marginBottom: 0" },
    children: [
      {
        component: LASelect,
        field: "operateDeck",
        style: { width: "200px", lineHeight: "initial" },
        props: {
          placeholder: "操作端",
          list: [
            {
              label: "网页端",
              value: "网页端"
            },
            {
              label: "小程序",
              value: "小程序"
            }
          ]
        }
      },
      {
        type: "LADateTimeRangePicker",
        name: "time",
        style: { lineHeight: "initial", height: "32px" },
        props: {
          type: "daterange",
          format: "YYYY-MM-DD",
          placeholder: ["起始日期", "截止日期"]
        },
        on: {
          "update:start": val => {
            if (val) {
              fApi.value.form["startTime"] = val;
            } else {
              fApi.value.form["startTime"] = undefined;
            }
          },
          "update:end": val => {
            if (val) {
              fApi.value.form["endTime"] = val;
            } else {
              fApi.value.form["endTime"] = undefined;
            }
          }
        }
      },
      {
        type: "input",
        field: "search",
        props: {
          size: "default",
          placeholder: "输入操作人/账号"
        }
      }
    ]
  },
  {
    type: "ProTable",
    props: {
      columns: [
        {
          prop: loginLogColumnMap.get("操作人/账号"),
          label: "操作人/账号"
        },
        {
          prop: loginLogColumnMap.get("操作端"),
          label: "操作端"
        },
        {
          prop: loginLogColumnMap.get("设备IP"),
          label: "设备IP"
        },
        {
          prop: loginLogColumnMap.get("登录时间"),
          label: "登录时间"
        }
      ],
      fetch: getLoginLogList
    }
  }
]);
</script>
<style lang="scss" scoped>
:deep(.el-row) {
  height: calc(100vh - 178px) !important;
}

.el-form-item {
  margin-right: 4px !important;
}
</style>
