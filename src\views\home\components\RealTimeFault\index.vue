<template>
  <div class="real-time-fault">
    <LACard :showHeader="false" shadow="never">
      <LATitle :bottom="0" title="实时故障">
        <div class="historical-records" @click="toPage">故障记录</div>
      </LATitle>
      <List />
    </LACard>
  </div>
</template>

<script lang="ts" setup>
/**
 * @file 首页-实时故障
 * <AUTHOR>
 * @date 2025/1/21
 */
import LATitle from "@/components/LATitle.vue";
import LACard from "@/components/LACard/index.vue";
import { useRouter } from "vue-router";
import List from "@/views/home/<USER>/RealTimeFault/List.vue";
const router = useRouter();
const toPage = () => {
  router.push("/statisticalReport/faultRecord/index");
};
</script>

<style lang="scss" scoped>
.real-time-fault {
  width: 100%;
  height: 100%;
  .word {
    justify-content: space-between;
  }
  .historical-records {
    font-size: 14px;
    line-height: 100%;
    color: #356afdff;
    cursor: pointer;
    margin-left: 10px;
  }
}
</style>
