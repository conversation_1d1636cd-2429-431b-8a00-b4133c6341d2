/**
 * @file 统计报表-任务记录-类型声明文件
 * <AUTHOR>
 * @date 2025/7/16
 */

// table字段声明
export const columnMap: any = new Map([
  ["矿车名称", "miningCarName"],
  ["任务类型", "taskType"],
  ["物料类型", "materialTypeName"],
  ["铲点", "shovelPoint"],
  ["挖机师傅", "excavatorOperator"],
  ["地磅", "weighbridge"],
  ["卸点", "unloadingPoint"],
  ["起点时间", "startTime"],
  ["完成时间", "completionTime"],
  ["重量", "weight"],
  ["里程", "mileage"],
  ["计划编号", "planNumber"]
]);
// 矿卡任务状态枚举
export enum StatusEnum {
  /** 空闲中*/
  TRAIN_READY = 1000,
  /** 运行中*/
  TRAIN_BUSY = 1001,
  /** 充电中*/
  TRAIN_CHARGING = 1002,
  /** 故障中*/
  TRAIN_FAILURE = 1003,
  /** 离线中*/
  TRAIN_OFFLINE = 1006
}
// 矿卡任务详情枚举
export const TaskTypeMap = new Map([
  [StatusEnum.TRAIN_READY, "空闲中"],
  [StatusEnum.TRAIN_BUSY, "运行中"],
  [StatusEnum.TRAIN_CHARGING, "充电中"],
  [StatusEnum.TRAIN_FAILURE, "故障中"],
  [StatusEnum.TRAIN_OFFLINE, "离线中"]
]);
// 矿卡任务状态
export const truckTaskStatusEnum = [
  {
    bg: "rgba(234, 240, 255)",
    status: StatusEnum.TRAIN_READY,
    color: "rgba(53, 106, 253, 1)",
    text: "空闲中"
  },
  {
    bg: "rgba(229, 249, 244)",
    status: StatusEnum.TRAIN_BUSY,
    color: "rgba(0, 194, 144, 1)",
    text: "运行中"
  },
  {
    bg: "rgba(229, 251, 251)",
    status: StatusEnum.TRAIN_CHARGING,
    color: "rgba(0, 209, 217, 1)",
    text: "充电中"
  },
  {
    bg: "rgba(255, 241, 236)",
    status: StatusEnum.TRAIN_FAILURE,
    color: "rgba(249, 116, 75, 1)",
    text: "故障中"
  },
  {
    bg: "rgba(239, 239, 239)",
    status: StatusEnum.TRAIN_OFFLINE,
    color: "rgba(101, 102, 102, 1)",
    text: "离线中"
  }
];
