<template>
  <div>
    <LADetailCard
      :columns="2"
      :detail-data="detailData"
      :detail-labels="detailLabels"
      :tag-icon="tag"
      :title="detailData?.repairWorkOrderNumber"
    >
      <template #extra-btn="{ data }">
        <!--关闭工单-->
        <form-create
          v-if="data.workOrderStatus !== 'closed'"
          v-model:api="closeFormApi"
          :option="closeOption"
          :rule="closeRule"
        ></form-create>
        <!--转让工单-->
        <form-create
          v-if="data.workOrderStatus !== 'closed'"
          v-model:api="transferFormApi"
          :option="transferOption"
          :rule="transferRule"
        ></form-create>
        <!--填写维修结果-->
        <form-create
          v-if="data.workOrderStatus !== 'closed'"
          v-model:api="resultFormApi"
          :option="resultOption"
          :rule="resultRule"
        ></form-create>
      </template>
    </LADetailCard>

    <RepairRecord :repair-record="repairRecordData" />
  </div>
</template>

<script lang="tsx" setup>
/**
 * @file 维修保养-维修工单-详情组件
 * <AUTHOR>
 * @date 2025/2/11
 */
import LADetailCard from "@/components/LADetailCard.vue";
import { computed, nextTick, ref } from "vue";
import { columnMap, repairOrderStatusEnum } from "@/views/maintenance/repairOrder/types.ts";
import formCreate from "@form-create/element-ui";
import {
  closeFormCreate,
  repairResultFormCreate,
  transferFormCreate
} from "@/views/maintenance/repairOrder/components/formCreate.ts";
import {
  closeWorkOrder,
  getRepairRecordList,
  getWorkOrderDetail,
  modifyWorkOrder,
  transferWorkOrder
} from "@/api/modules/repair.ts";
import DetailBtn from "@/components/FormCreate/DialogBtn/DetailBtn.vue";
import ExperienceDetailComponent from "@/views/maintenance/repairOrder/components/ExperienceDetailComponent.vue";
import RepairRecord from "@/views/maintenance/repairOrder/components/RepairRecord.vue";

const props = defineProps({
  formCreateInject: {
    type: Object,
    default: () => {}
  }
});
// 转让工单配置
const transferFormApi = ref();
const transferOption = {
  resetBtn: false,
  submitBtn: false,
  row: {
    justify: "center"
  }
};
const transferRule = ref([
  {
    type: "AddBtn",
    props: {
      btn: {
        icon: "",
        style: {
          height: "32px",
          borderRadius: "6px",
          color: "#000",
          border: "1px solid #DDE2E8",
          background: "#fff"
        },
        content: "转让工单"
      },
      // 是否是自定义表单弹窗 未嵌入表格只是显示弹窗form
      isCustomDialog: true,
      formatter: () => {
        // console.log("个人资料", model.value);
        return {
          id: detailData.value.id,
          newResponsiblePersonId: detailData.value.repairResponsiblePersonId,
          newResponsiblePersonName: detailData.value.repairResponsiblePersonName
        };
      },
      dialog: { title: "转让工单" },
      submitRequest: params => {
        return transferWorkOrder(params).then(res => {
          refresh();
          return res;
        });
      }
    },
    children: [transferFormCreate]
  }
]);
// 关闭工单配置
const closeFormApi = ref();
const closeOption = {
  resetBtn: false,
  submitBtn: false,
  row: {
    justify: "center"
  }
};
const closeRule = ref([
  {
    type: "AddBtn",
    props: {
      btn: {
        icon: "",
        style: {
          height: "32px",
          borderRadius: "6px",
          color: "rgba(242, 85, 85, 1)",
          border: "1px solid #DDE2E8",
          background: "#fff"
        },
        content: "关闭工单"
      },
      dialog: { title: "关闭工单" },
      submitRequest: params => {
        return closeWorkOrder({ ...params, id: detailData.value.id }).then(res => {
          refresh();
          return res;
        });
      }
    },
    children: [closeFormCreate]
  }
]);
// 填写维修结果配置
const resultFormApi = ref();
const resultOption = {
  resetBtn: false,
  submitBtn: false,
  row: {
    justify: "center"
  }
};
const resultRule = ref([
  {
    type: "AddBtn",
    props: {
      btn: {
        icon: "",
        style: {
          height: "32px",
          borderRadius: "6px",
          color: "#fff",
          border: "1px solid #DDE2E8",
          background: "rgba(53, 106, 253, 1)"
        },
        content: "填写维修结果"
      },
      dialog: { title: "填写维修结果" },
      submitRequest: params => {
        return modifyWorkOrder({ ...params, id: detailData.value.id }).then(res => {
          refresh();
          return res;
        });
      }
    },
    children: [repairResultFormCreate]
  }
]);
// 储存详情方法
const detailData = ref();
// 储存维修记录数据
const repairRecordData = ref();

const detailId = ref();
// 刷新/获取数据的方法
const refresh = async () => {
  // 获取工单详情
  await getWorkOrderDetail({ id: detailId.value }).then(res => {
    detailData.value = res.data;
  });
  // 获取工单记录
  await getRepairRecordList({ workOrderId: detailId.value }).then(res => {
    repairRecordData.value = res.data;
  });
};

nextTick(() => {
  // 储存id信息
  detailId.value = props.formCreateInject.api.formData().id;
  refresh();
});
// 图标样式
const tag = computed(() => {
  return {
    enum: repairOrderStatusEnum,
    prop: "workOrderStatus",
    value: detailData?.value?.workOrderStatus
  };
});
// 详情数据
const detailLabels = {
  [columnMap.get("矿车名称")]: {
    label: "矿车名称",
    hiddenColon: true
  },
  [columnMap.get("故障码")]: {
    label: "故障码",
    hiddenColon: true,
    formatter: (value: number) => {
      if (value != undefined) {
        return (
          <div style="display:flex;align-items:center">
            <span style="margin-right:8px;line-height:100%">{value}</span>
            <DetailBtn
              button={{
                buttonText: "维修经验",
                style: {
                  padding: 0,
                  display: "inline-block",
                  height: "auto"
                }
              }}
              dialog={{
                title: "维修经验详情",
                class: "repair-order-detail"
              }}
            >
              <ExperienceDetailComponent orderDetail={detailData} />
            </DetailBtn>
          </div>
        );
      }
      return "-";
    }
  },
  [columnMap.get("故障时间")]: {
    hiddenColon: true,
    label: "故障时间"
  },
  [columnMap.get("报修人")]: {
    hiddenColon: true,

    label: "报修人"
  },
  [columnMap.get("维修负责人")]: {
    hiddenColon: true,

    label: "维修负责人"
  },
  [columnMap.get("故障描述")]: {
    hiddenColon: true,

    label: "故障描述"
  }
};
</script>

<style scoped></style>
