<template>
  <div class="panel">
    <el-tooltip effect="light" :content="item.tooltip" placement="right" v-for="item of actions" :key="item.action">
      <div class="card-container" @click="item.onClick" @mousedown="activeAction = item.action" @mouseup="activeAction = ''">
        <SvgIcon
          :name="item.icon"
          icon-style="width:24px; height:24px"
          :color="activeAction === item.action || actionStates[item.action] ? 'var(--el-color-primary)' : ''"
        />
      </div>
    </el-tooltip>
  </div>
  <!-- 定位移动 -->
  <PositionMover ref="positionMoverRef" :cancel-callback="cancelCallback" :position="position" direction="0" />
</template>

<script lang="ts" setup>
import { useFullscreen } from "@vueuse/core";
import { computed, ref, watch } from "vue";
import SvgIcon from "@/components/SvgIcon/index.vue";
import PositionMover from "@/views/copilot/components/Billboard/Truck/components/PositionMover.vue";
import { useMapActionChannel } from "@/views/map/channel";

const activeAction = ref("");
const actionStates = ref({
  fullscreen: false,
  reset: false,
  location: false,
  dashed: true
});

const props = defineProps<{ container: HTMLElement }>();
const { toggle, isFullscreen } = useFullscreen(computed(() => props.container));

const actions = [
  {
    action: "fullscreen",
    tooltip: "全屏",
    icon: "IconMapFullscreen",
    onClick() {
      toggle();
      actionStates.value.fullscreen = !actionStates.value.fullscreen;
    }
  },
  {
    action: "reset",
    icon: "IconMapReset",
    tooltip: "重置",
    onClick() {
      useMapActionChannel().post({ type: "reset", data: null });
      actionStates.value.reset = !actionStates.value.reset;
    }
  },
  {
    action: "location",
    icon: "IconMapLocation",
    tooltip: "定位",
    onClick() {
      actionStates.value.location = !actionStates.value.location;
      useMapActionChannel().post({ type: "location", data: { active: actionStates.value.location } });
    }
  },
  {
    action: "dashed",
    icon: "IconMapDashed",
    tooltip: "虚线",
    onClick() {
      actionStates.value.dashed = !actionStates.value.dashed;
      useMapActionChannel().post({ type: "dashed", data: { active: actionStates.value.dashed } });
    }
  }
];

const positionMoverRef = ref();
const position = ref({ lng: 0, lat: 0 });
// 选择了取消 获取关闭按钮（非确定按钮），移除之前的点
const cancelCallback = () => {
  useMapActionChannel().post({ type: "location", data: { active: true, removeLastPoint: true } });
};
// Watch fullscreen changes from external triggers
watch(isFullscreen, value => {
  actionStates.value.fullscreen = value;
});
// 左侧操作面板点击定位按钮之后 显示定位移动对话框
watch(useMapActionChannel().data, data => {
  if (data.type === "location-move") {
    position.value = data.data.position;
    positionMoverRef.value.show();
    console.log("data.data.point", data.data.position);
  }
});
</script>

<style lang="scss" scoped>
.panel {
  position: absolute;
  width: 40px;
  z-index: 10;
  left: 24px;
  top: 24px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 2px;
}
.card-container {
  width: 40px;
  height: 40px;
  padding: 4px;
  box-sizing: border-box;
  display: flex;
  line-height: 12px;
  text-align: center;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  cursor: pointer;
  user-select: none;
  &:hover {
    border: 1px solid #fff;
  }
}
</style>
