/**
 * @file 设备管理-驾驶舱管理-表单创建文件
 * <AUTHOR>
 * @date 2024/11/20
 */
import { formMap } from "../types";

export const cockpitManageFormCreate = {
  type: "form-create",
  props: {
    option: {
      submitBtn: false,
      onSubmit(formData, api) {
        console.log("formData", formData);
        // 通知 table 搜索数据变化，刷新数据
        api.top.bus.$emit("searchFormChanged");
      }
    },
    rule: [
      {
        type: "input",
        field: formMap.get("名称"),
        title: "驾驶舱名称",
        validate: [
          { required: true, message: "请输入驾驶舱名称" },
          {
            pattern: /^.{1,20}$/,
            message: "字符限长20位"
          }
        ]
      },

      {
        type: "input",
        field: formMap.get("机器码"),
        title: "机器码",
        validate: [{ required: true, message: "请输入机器码" }]
      }
    ]
  }
};
