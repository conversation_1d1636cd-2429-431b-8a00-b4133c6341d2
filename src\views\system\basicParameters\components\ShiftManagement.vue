<template>
  <form-create v-model:api="fApi" :option="option" :rule="rule"></form-create>
</template>
<script lang="tsx" setup>
/**
 * @file 系统管理-基础参数-班次管理
 * <AUTHOR>
 * @date 2024/11/14
 */
import { ref } from "vue";
import { getFlightList, addFlight, deleteFlight } from "@/api/modules";
import { shiftScheduleColumnMap } from "../types";
import formCreate from "@form-create/element-ui";

import { shiftFormCreate } from "@/views/system/basicParameters/components/formCreate";
const fApi = ref();
const option = {
  form: { inline: true },
  resetBtn: false,
  submitBtn: false
};

const rule = ref([
  {
    type: "SearchFormOperation",
    field: "v:search",
    wrap: { style: "marginBottom: 0" },
    children: [
      {
        type: "input",
        field: "search",
        props: {
          size: "default",
          placeholder: "班次名称"
        }
      },
      // 新增
      {
        type: "AddBtn",
        slot: "suffix",
        props: {
          btn: { content: "新增班次" },
          dialog: { title: "新增班次" },
          size: "default",
          submitRequest: addFlight
        },
        children: [shiftFormCreate]
      }
    ]
  },
  {
    type: "ProTable",
    props: {
      columns: [
        {
          prop: shiftScheduleColumnMap.get("班次名称"),
          label: "班次名称"
        },
        {
          prop: shiftScheduleColumnMap.get("开始时间"),
          label: "开始时间"
        },
        {
          prop: shiftScheduleColumnMap.get("结束时间"),
          label: "结束时间"
        },
        { prop: "operation", label: "操作", style: { color: "red" }, fixed: "right" }
      ],
      fetch: getFlightList,
      operations: [
        { content: "修改", action: "edit" },
        { content: "删除", action: "delete", props: { style: { color: "rgba(242, 85, 85, 1)" } } }
      ]
    },
    children: [
      {
        type: "EditBtn",
        props: {
          action: "edit",
          dialog: { title: "修改班次" },
          submitRequest: addFlight
        },
        children: [shiftFormCreate]
      },
      {
        type: "ConfirmDialog",
        on: {
          // 监听弹窗组件抛出的的afterSubmit事件，用于刷新页面
          afterSubmit: () => {
            // 刷新，调用组件内部请求方法
            fApi.value.exec("v:search", "onSearch");
          }
        },
        props: {
          action: "delete",
          subtitle: row => {
            return row.name;
          },
          title: "是否删除班次",
          message: "删除后不可恢复",
          // 请求param：参数
          submitRequest: deleteFlight
        }
      }
    ]
  }
]);
</script>
<style lang="scss" scoped>
:deep(.el-row) {
  height: calc(100vh - 178px) !important;
}

.el-form-item {
  margin-right: 4px !important;
}
</style>
