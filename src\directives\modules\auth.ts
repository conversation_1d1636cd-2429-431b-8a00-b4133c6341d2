/**
 * v-auth
 * 按钮权限指令
 */
import { useAuthStore } from "@/stores/modules/auth";
import type { Directive, DirectiveBinding } from "vue";

const auth: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    const { value } = binding;
    if (!value) return;
    const authStore = useAuthStore();
    const currentPageRoles = authStore.currentPageRolesGet;

    if (value instanceof Array && value.length) {
      const hasPermission = value.every(item => currentPageRoles[item]);
      if (hasPermission) {
        el.style.textDecoration = "underline solid green 4px";
      } else {
        el.style.textDecoration = "underline solid red 4px";
      }
      // if (!hasPermission) el.remove();
    } else {
      if (currentPageRoles[value]) {
        el.style.textDecoration = "underline solid green 4px";
      } else {
        el.style.textDecoration = "underline solid red 4px";
      }
      // if (!currentPageRoles[value]) el.remove();
    }
  }
};

export default auth;
