/**
 * @file 设备管理-挖机管理-类型声明文件
 * <AUTHOR>
 * @date 2024/12/2
 */

// table字段声明
export const columnMap: any = new Map([
  ["名称", "name"],
  ["编码", "code"],
  ["绑定流量卡", "cardName"],
  ["最大装载率", "maxLoad"]
]);
// 新增/修改form字段声明
export const formMap = new Map([
  ["名称", "name"],
  ["编码", "code"],
  ["绑定流量卡", "card"],
  ["最大装载率", "maxLoad"]
]);

// 挖机状态枚举
export enum StatusEnum {
  // 只要不是0和-1都是在线中
  /** 离线*/
  OFFLINE = 0,
  /** 允许泊车*/
  PARKING = 1,
  /** 等待装载*/
  WAIT_LOAD = 2,
  /** 装载中*/
  LOADING = 3,
  /** 空闲中*/
  WAITING = 4,
  /** 故障*/
  FAULTS = -1
}

// 矿卡状态 (颜色可自定义)
export const BulldozerStatusEnum = [
  {
    bg: "rgba(234, 240, 255)",
    status: StatusEnum.OFFLINE,
    color: "rgba(53, 106, 253, 1)",
    text: "离线"
  },
  {
    bg: "rgba(234, 240, 255)",
    status: StatusEnum.PARKING,
    color: "rgba(53, 106, 253, 1)",
    text: "允许泊车"
  },
  {
    bg: "rgba(234, 240, 255)",
    status: StatusEnum.WAIT_LOAD,
    color: "rgba(53, 106, 253, 1)",
    text: "等待装载"
  },
  {
    bg: "rgba(234, 240, 255)",
    status: StatusEnum.LOADING,
    color: "rgba(53, 106, 253, 1)",
    text: "装载中"
  },
  {
    bg: "rgba(234, 240, 255)",
    status: StatusEnum.WAITING,
    color: "rgba(53, 106, 253, 1)",
    text: "空闲中"
  },
  {
    bg: "rgba(234, 240, 255)",
    status: StatusEnum.FAULTS,
    color: "rgba(53, 106, 253, 1)",
    text: "故障"
  }
];
