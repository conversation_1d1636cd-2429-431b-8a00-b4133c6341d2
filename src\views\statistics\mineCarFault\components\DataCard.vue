<template>
  <div class="data-card" :style="{ '--leftColor': color }">
    <div class="title">{{ config.title }}</div>
    <div class="info-item item1">
      <div class="value">{{ cardData[config.dataKey] || 0 }}</div>
      <div class="unit" v-if="config.unit">{{ config.unit }}</div>
    </div>
    <div class="info-item item2">
      <div class="label">环比&nbsp;</div>
      <div class="value" :class="{ up: cardData?.momFlag === true, down: cardData?.momFlag === false }">
        <template v-if="[true, false].includes(cardData?.momFlag)">
          <span style="font-size: 10px" v-if="cardData.momFlag">▲</span>
          <span style="font-size: 10px" v-else>▼</span>
          {{ Math.abs(cardData.mom) }}%
        </template>
        <template v-else> - </template>
      </div>
    </div>
    <div class="info-item item2" v-if="searchParams?.reportType !== 'year'">
      <div class="label">同比&nbsp;</div>
      <div class="value" :class="{ up: cardData?.yoyFlag === true, down: cardData?.yoyFlag === false }">
        <template v-if="[true, false].includes(cardData?.yoyFlag)">
          <span style="font-size: 10px" v-if="cardData.yoyFlag">▲</span>
          <span style="font-size: 10px" v-else>▼</span>
          {{ Math.abs(cardData.yoy) }}%
        </template>
        <template v-else> - </template>
      </div>
    </div>
    <div class="info-item item3" v-if="showAverageDaily">日均&nbsp;{{ averageDaily }}{{ config.unit }}</div>
  </div>
</template>
<script setup lang="ts">
import { ref, inject, watch, computed, type ComputedRef } from "vue";
import { SearchParams } from "../index.vue";
import { getDatePositionByFormat } from "@/utils/date";

const searchParams = inject<ComputedRef<SearchParams>>("searchParams");
interface CardConfig {
  title: string;
  unit: string;
  dataKey: string;
  dayAvg?: boolean;
  api: (params: SearchParams) => Promise<{
    success: boolean;
    data: CardData;
  }>;
}

const props = defineProps<{
  config: CardConfig;
  color: string | undefined;
  weeklyCurrentDate: string | undefined;
}>();

interface CardData {
  total: number;
  mom: number;
  momFlag?: boolean;
  yoy?: number;
  yoyFlag?: boolean;
}
const cardData = ref<CardData>({} as CardData);
const averageDaily = ref("");
const showAverageDaily = computed(() => {
  return props?.config.dayAvg && searchParams?.value.reportType !== "day";
});
const getData = async (params: SearchParams) => {
  try {
    const res = await props?.config.api(params);
    if (!res.success) return;
    cardData.value = res.data;
    const total = cardData.value[props?.config.dataKey];

    const dateStr = searchParams?.value.date! || props?.weeklyCurrentDate!;

    const day = getDatePositionByFormat(dateStr);
    averageDaily.value = (total / day).toFixed(2);
  } catch (error) {
    console.log(error);
  }
};

watch(
  () => searchParams?.value,
  newParams => newParams && getData(newParams),
  { deep: true, immediate: true }
);
</script>
<style lang="scss" scoped>
.data-card {
  flex: 1;
  padding: 20px;
  font-size: 14px;
  color: #656666;
  border: 1px solid #dde2e8;
  border-left-color: var(--leftColor);
  border-left-width: 2px;
  .item1 {
    display: flex;
    column-gap: 4px;
    align-items: baseline;
    font-weight: bold;
    color: #1a1a1a;
    .value {
      font-size: 28px;
    }
  }
  .item2 {
    display: flex;
    margin: 5px 0;
    color: #656666;
    .up {
      color: #f25555;
    }
    .down {
      color: #00c290;
    }
  }
}
</style>
