<template>
  <vc-overlay-html :position="position" ref="htmlOverlay" :show="show" :teleport="{ to: 'body' }">
    <div class="window-popup__container">
      <slot></slot>
    </div>
  </vc-overlay-html>
</template>
<script lang="ts" setup>
import { ref } from "vue";
import { VcOverlayHtml } from "vue-cesium";
defineProps<{ position: Cesium.Cartesian3 }>();
interface Actions {
  load: () => void;
  unload: () => void;
  reload: () => void;
  toggle: () => void;
}
const htmlOverlay = ref<InstanceType<typeof VcOverlayHtml> & Actions>();
const show = ref(false);

defineExpose({
  unload() {
    htmlOverlay.value!.unload();
  },
  load() {
    htmlOverlay.value!.load();
  },
  reload() {
    htmlOverlay.value!.reload();
  },
  toggle() {
    console.log("htmlOverlay.value", htmlOverlay.value);
    show.value = !show.value;
  }
});
</script>
<style scoped>
.window-popup__container {
  position: absolute;
  background-color: #fff;
  top: 0;
  left: 0;
}
</style>
