<template>
  <DialogComp
    v-model="formDialogVisible"
    :before-close="handleClose"
    :class="dialog?.class"
    :style="{ '--body-padding': '20px 80px 0', ...dialog?.style }"
    v-bind="dialog"
    width="586px"
  >
    <slot />
    <template v-if="dialog?.footer !== false" #footer>
      <el-button size="large" style="width: 108px" @click="onCancel">取消</el-button>
      <el-button size="large" style="width: 148px; margin-left: 10px" type="primary" @click="onSubmit">确定</el-button>
    </template>
  </DialogComp>
  <el-button v-if="btn?.show !== false" v-auth="btn?.auth" size="default" type="primary" v-bind="btn" @click="operateBtn">
    {{ btn?.content ?? currentPageRoles[btn?.auth!] ?? btn?.auth }}
  </el-button>
</template>
<script lang="ts" setup>
import DialogComp from "./DialogComp.vue";
import { DialogBtnProps } from "./types";
import { nextTick, ref, watch } from "vue";
import { Api } from "@form-create/element-ui";
import { ElNotification } from "element-plus";
import { ResultEnum } from "@/enums/httpEnum";
import { useAuthStore } from "@/stores/modules/auth";

const props = defineProps<{ dialog?: { class?: string; footer?: boolean } } & DialogBtnProps>();
const emit = defineEmits(["editSuccess", "after-submit"]);

const authStore = useAuthStore();
const currentPageRoles = authStore.currentPageRolesGet ?? {};

const childrenApi = ref<Api | null>();
// 弹窗是否显示
const formDialogVisible = ref(false);
// 数据提交 发送请求
const onSubmit = () => {
  if (!childrenApi.value) return;
  childrenApi.value.validate(async (valid: boolean | object) => {
    //valid=>表单通过时为boolean 未通过时 返回验证对象 所以判断是否为true 详细见新增时未通过也会发送请求
    if (valid === true && childrenApi.value && props.submitRequest) {
      // console.log('提交', props.submitRequest, childrenApi.value.formData(), childrenApi.value);
      try {
        const formData = childrenApi.value.formData();
        const res = await props.submitRequest(formData, childrenApi.value);

        // res === true用于不需要发送请求，弹窗处理其他逻辑，比如富文本编辑器
        if (res.statusCode === ResultEnum.SUCCESS || res === true) {
          // 调用提交方法用于更新数据
          await childrenApi.value.submit();
          ElNotification({ title: "温馨提示", message: "提交成功！", type: "success" });
          childrenApi.value.resetFields();
          emit("editSuccess", childrenApi.value);
          emit("after-submit", childrenApi.value, formData, res);
          formDialogVisible.value = false;
        }
      } catch (error) {
        console.error("接口请求失败", error);
      }
    } else {
      // console.log(valid, childrenApi.value?.formData(), props.submitRequest);
      // console.log("表单验证失败", valid);
    }
  });
};
const operateBtn = () => {
  // 如果是纯净按钮则不触发
  if (props.btn?.isPureButton) return;
  formDialogVisible.value = true;
};
// 关闭前 清空字段
const handleClose = done => {
  childrenApi?.value?.resetFields();
  done();
};
// 取消按钮 关闭弹窗
const onCancel = () => {
  childrenApi?.value?.resetFields();
  formDialogVisible.value = false;
};

// 定义一个函数用于处理弹窗打开前，处理 formatter 的默认值
function getHandleData(row?: { [key: string]: any }) {
  // 获取默认值
  const formatter = props.formCreateInject?.rule?.props?.formatter || ((data: any) => data);

  // console.log(11111, props.formCreateInject);
  // 确保 formatter 是一个函数，如果不是，给一个默认的 formatter
  return typeof formatter === "function" ? formatter(row) : (row?: any) => row;
}

// 弹窗打开时，设置弹窗内form-create操作api
watch(
  () => formDialogVisible.value,
  formDialogVisible => {
    if (formDialogVisible) {
      nextTick(() => {
        // 自定义api的值 用于处理api层级不为0的情况,详情见运营管理
        const formChildrenApi = props?.formCreateInject?.api?.children;

        if (props.customApi && formChildrenApi) {
          childrenApi.value = props.customApi(formChildrenApi);
        } else {
          // @ts-ignore
          const formCreate = formChildrenApi.find(item => item.config.n === props.n);

          if (formCreate && props.n) {
            childrenApi.value = formCreate;
          } else {
            childrenApi.value = formChildrenApi.at(-1);
          }
        }
        // 是否是自定义表单弹窗 此方法应用于当前页面未嵌入表格,而只是按钮显示弹窗form 设置自定义表单值 如：调度计划-新增调度计划
        if (props.formCreateInject.rule.props.isCustomDialog) {
          // 此处的正则会导致 getHandleData 调用两次
          const data = getHandleData() ? getHandleData() : {};
          childrenApi.value?.setValue(data);
          // console.log("赋值获取到表单值", childrenApi.value?.formData());
        }
      });
    }
  },
  { flush: "post" }
);
defineExpose({
  toggle() {
    formDialogVisible.value = !formDialogVisible.value;
  }
});
</script>
<style lang="scss">
//清除根节点产生滚动条
.el-overlay-dialog {
  overflow: hidden !important;
}

.el-dialog .__form-dialog {
  margin-top: 8vh !important;
}
</style>
