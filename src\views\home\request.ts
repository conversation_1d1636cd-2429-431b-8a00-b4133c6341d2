import { useAsyncStoreManager } from "./store";
import type { Ref } from "vue";
import {
  // 统计数据接口
  getDispatchTaskCountStatistics,
  getDispatchTaskWeightStatistics
} from "@/api/modules/copilot";

import {
  getAllMaintenanceCount,
  getAllMaintenanceTrain,
  getAllRepairCount,
  getDeviceOnlineStatisticsCount,
  getProjectDetailsQueue,
  getProjectDetailStatisticsCount,
  getProjectStatusCount,
  listDispatchFaultByMap
} from "@/api/modules/home.ts";

const { register, getStore, refreshAll } = useAsyncStoreManager();

interface CustomError extends Error {
  status?: number;
  response?: { [key: string]: any };
}

// 创建store key生成器
const createStoreKey = (prefix: string, params: Record<string, any>) => {
  const paramsKey = JSON.stringify(params);
  return `${prefix}:${paramsKey}`;
};

// 首页-实时数据
export const useDispatchStatistics = <T = any>(params: Record<string, any> = {}) => {
  const storeKey = createStoreKey("homeDispatchStatistics", params);
  if (!getStore(storeKey, false)) {
    register(storeKey, {
      fetcher: async () => {
        const [taskCountResponse, weightStatsResponse] = await Promise.all([
          getDispatchTaskCountStatistics(params),
          getDispatchTaskWeightStatistics(params)
        ]);
        return {
          taskCount: (taskCountResponse as ResultData<any>).data,
          weightStats: (weightStatsResponse as ResultData<any>).data
        } as T;
      },
      refreshInterval: 60000,
      immediate: true
    });
  }
  return getStore(storeKey) as {
    data: Ref<T>;
    error: Ref<CustomError | null>;
    isLoading: Ref<boolean>;
    isStale: Ref<boolean>;
    lastUpdated: Ref<Date | null>;
    isEnabled: Ref<boolean>;
    refresh: () => Promise<void>;
    pauseAutoRefresh?: () => void;
    resumeAutoRefresh?: () => void;
  };
};
// 首页-任务队列
export const useTaskQueue = <T = any>(params: Record<string, any> = {}) => {
  const storeKey = createStoreKey("homeTaskQueue", params);
  if (!getStore(storeKey, false)) {
    register(storeKey, {
      fetcher: async () => {
        const response = await getProjectDetailsQueue();
        // console.log(111111, response);
        return (response as ResultData<{ data: T[] }>).data;
      },
      refreshInterval: 10000,
      immediate: true
    });
  }
  return getStore(storeKey) as {
    data: Ref<T>;
    error: Ref<CustomError | null>;
    isLoading: Ref<boolean>;
    isStale: Ref<boolean>;
    lastUpdated: Ref<Date | null>;
    isEnabled: Ref<boolean>;
    refresh: () => Promise<void>;
    pauseAutoRefresh?: () => void;
    resumeAutoRefresh?: () => void;
  };
};
// 首页-调度任务统计
export const useDispatchPlanStatistics = <T = any>(params: Record<string, any> = {}) => {
  const storeKey = createStoreKey("dispatchPlanStatistics", params);
  if (!getStore(storeKey, false)) {
    register(storeKey, {
      fetcher: async () => {
        const response = await getProjectStatusCount();
        return (response as ResultData<{ data: T[] }>).data;
      },
      refreshInterval: 60000,
      immediate: true
    });
  }
  return getStore(storeKey) as {
    data: Ref<T>;
    error: Ref<CustomError | null>;
    isLoading: Ref<boolean>;
    isStale: Ref<boolean>;
    lastUpdated: Ref<Date | null>;
    isEnabled: Ref<boolean>;
    refresh: () => Promise<void>;
    pauseAutoRefresh?: () => void;
    resumeAutoRefresh?: () => void;
  };
};
// 首页-调度计划状态统计
export const useDispatchProjectDetailStatisticsCount = <T = any>(params: Record<string, any> = {}) => {
  const storeKey = createStoreKey("dispatchTaskStatistics", params);
  if (!getStore(storeKey, false)) {
    register(storeKey, {
      fetcher: async () => {
        const response = await getProjectDetailStatisticsCount();
        return (response as ResultData<{ data: T[] }>).data;
      },
      refreshInterval: 60000,
      immediate: true
    });
  }
  return getStore(storeKey) as {
    data: Ref<T>;
    error: Ref<CustomError | null>;
    isLoading: Ref<boolean>;
    isStale: Ref<boolean>;
    lastUpdated: Ref<Date | null>;
    isEnabled: Ref<boolean>;
    refresh: () => Promise<void>;
    pauseAutoRefresh?: () => void;
    resumeAutoRefresh?: () => void;
  };
};
// 首页-维修保养统计
export const useMaintainStatistics = <T = any>(params: Record<string, any> = {}) => {
  const storeKey = createStoreKey("maintainStatistics", params);
  if (!getStore(storeKey, false)) {
    register(storeKey, {
      fetcher: async () => {
        const [maintenanceCountResponse, repairCountResponse] = await Promise.all([
          getAllMaintenanceCount(),
          getAllRepairCount()
        ]);
        return {
          maintenanceCount: (maintenanceCountResponse as ResultData<any>).data,
          repairCount: (repairCountResponse as ResultData<any>).data
        } as T;
      },
      refreshInterval: 60000,
      immediate: true
    });
  }
  return getStore(storeKey) as {
    data: Ref<T>;
    error: Ref<CustomError | null>;
    isLoading: Ref<boolean>;
    isStale: Ref<boolean>;
    lastUpdated: Ref<Date | null>;
    isEnabled: Ref<boolean>;
    refresh: () => Promise<void>;
    pauseAutoRefresh?: () => void;
    resumeAutoRefresh?: () => void;
  };
};
// 获取所有待保养的矿车列表
export const useAllMaintenanceTrain = <T = any>(params: Record<string, any> = {}) => {
  const storeKey = createStoreKey("allMaintenanceTrain", params);
  if (!getStore(storeKey, false)) {
    register(storeKey, {
      fetcher: async () => {
        const response = await getAllMaintenanceTrain();
        return (response as ResultData<PaginationResponse<T>>).data;
      },
      refreshInterval: 60000,
      immediate: true
    });
  }
  return getStore(storeKey) as {
    data: Ref<PaginationResponse<T>>;
    error: Ref<CustomError | null>;
    isLoading: Ref<boolean>;
    isStale: Ref<boolean>;
    lastUpdated: Ref<Date | null>;
    isEnabled: Ref<boolean>;
    refresh: () => Promise<void>;
    pauseAutoRefresh?: () => void;
    resumeAutoRefresh?: () => void;
  };
};
// 首页-所有出勤设备统计
export const useAllDeviceAttendance = <T = any>(params: Record<string, any> = {}) => {
  const storeKey = createStoreKey("allDeviceAttendance", params);
  if (!getStore(storeKey, false)) {
    register(storeKey, {
      fetcher: async () => {
        const response = await getDeviceOnlineStatisticsCount();
        return (response as ResultData<{ data: T[] }>).data;
      },
      refreshInterval: 60000,
      immediate: true
    });
  }
  return getStore(storeKey) as {
    data: Ref<T>;
    error: Ref<CustomError | null>;
    isLoading: Ref<boolean>;
    isStale: Ref<boolean>;
    lastUpdated: Ref<Date | null>;
    isEnabled: Ref<boolean>;
    refresh: () => Promise<void>;
    pauseAutoRefresh?: () => void;
    resumeAutoRefresh?: () => void;
  };
};
// 首页-实时故障列表
export const useRealTimeFaultList = <T = any>(params: Record<string, any> = {}) => {
  const storeKey = createStoreKey("realTimeFaultList", params);
  if (!getStore(storeKey, false)) {
    register(storeKey, {
      fetcher: async () => {
        const response = await listDispatchFaultByMap();
        return (response as ResultData<PaginationResponse<T>>).data;
      },
      refreshInterval: 60000,
      immediate: true
    });
  }
  return getStore(storeKey) as {
    data: Ref<T>;
    error: Ref<CustomError | null>;
    isLoading: Ref<boolean>;
    isStale: Ref<boolean>;
    lastUpdated: Ref<Date | null>;
    isEnabled: Ref<boolean>;
    refresh: () => Promise<void>;
    pauseAutoRefresh?: () => void;
    resumeAutoRefresh?: () => void;
  };
};
interface ResultData<T> {
  success: boolean;
  data: T;
  statusCode: number;
  message: string;
  jwtToken: string | null;
}

interface PaginationResponse<T> {
  records: T[];
  total: number;
  size: number;
  current: number;
  pages: number;
}

// 导出刷新方法
export { refreshAll };
