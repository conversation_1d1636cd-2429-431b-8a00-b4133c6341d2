/**
 * @file 设备管理-破碎站管理-类型声明文件
 * <AUTHOR>
 * @date 2024/11/25
 */

// table字段声明
export const columnMap: any = new Map([
  ["名称", "name"],
  ["编码", "code"],
  ["允许卸矿最低安全深度(米)", "minSecurityDepth"],
  ["经度", "lon"],
  ["纬度", "lat"],
  ["海拔", "ele"],
  ["朝向", "orientations"],
  ["最大喂矿率(t/h)", "maxFeedRate"]
]);
// 新增/修改form字段声明
export const formMap = new Map([
  ["破碎站名称", "name"],
  ["破碎站编码", "code"],
  ["允许卸矿最低安全深度", "minSecurityDepth"],
  ["经度", "lon"],
  ["纬度", "lat"],
  ["海拔", "ele"],
  ["朝向", "orientations"],
  ["最大喂矿率", "maxFeedRate"]
]);

// 破碎站状态枚举
export enum StatusEnum {
  /** 离线*/
  OFFLINE = 0,
  /** 允许卸矿*/
  ALLOW_UNLOAD = 1
}
