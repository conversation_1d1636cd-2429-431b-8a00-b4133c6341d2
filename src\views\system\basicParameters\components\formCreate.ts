/**
 * @file 系统管理-参数配置-表单文件
 * <AUTHOR>
 * @date 2024/11/20
 */

import { materialFormMap, shiftScheduleColumnMap } from "../types";
import formCreate from "@form-create/element-ui";
import MiningTruckLoadCapacity from "./MiningTruckLoadCapacity.vue";
formCreate.component("MiningTruckLoadCapacity", MiningTruckLoadCapacity);
//物料管理表单
export const materialFormCreate = {
  type: "form-create",
  props: {
    rule: [
      {
        type: "input",
        field: materialFormMap.get("物料名称"),
        title: "物料名称",
        validate: [
          { required: true, message: "请输入物料名称" },
          {
            pattern: /^.{1,20}$/,
            message: "字符限长20位"
          }
        ]
      },
      {
        type: "input",
        field: materialFormMap.get("物料编码"),
        title: "物料编码",
        validate: [
          { required: true, message: "请输入物料编码" },
          {
            pattern: /^.{1,20}$/,
            message: "字符限长20位"
          }
        ]
      }
    ],
    option: {
      submitBtn: false,
      onSubmit(formData, api) {
        console.log(api);
        // 通知 table 搜索数据变化，刷新数据
        api.top.children[0].bus.$emit("searchFormChanged");
      }
    }
  }
};
// 矿卡载重
export const miningCarFormCreate = {
  type: "form-create",
  props: {
    rule: [
      {
        type: "MiningTruckLoadCapacity",
        field: "details"
      }
    ],
    option: {
      submitBtn: false,
      onSubmit(formData, api) {
        // 通知 table 搜索数据变化，刷新数据
        api.top.bus.$emit("searchFormChanged");
      }
    }
  }
};
// 班次管理
export const shiftFormCreate = {
  type: "form-create",
  props: {
    rule: [
      {
        type: "input",
        field: shiftScheduleColumnMap.get("班次名称"),
        title: "班次名称",
        validate: [
          { required: true, message: "请输入班次名称" },
          {
            pattern: /^.{1,20}$/,
            message: "字符限长20位"
          }
        ]
      },
      {
        type: "timePicker",
        style: {
          width: "100%"
        },
        field: shiftScheduleColumnMap.get("开始时间"),
        title: "开始时间",
        validate: [{ required: true, message: "请选择开始时间" }]
      },
      {
        type: "timePicker",
        style: {
          width: "100%"
        },
        field: shiftScheduleColumnMap.get("结束时间"),
        title: "结束时间",
        validate: [{ required: true, message: "请选择结束时间" }]
      }
    ],
    option: {
      submitBtn: false,
      onSubmit(formData, api) {
        // 通知 table 搜索数据变化，刷新数据
        api.top.children[0].bus.$emit("searchFormChanged");
      }
    }
  }
};
