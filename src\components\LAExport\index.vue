<template>
  <el-button v-auth="'export'" data-type="export-btn" size="default" @click="exportFile"> 导出 </el-button>
</template>

<script lang="ts" setup>
/**
 * @file 文件导出
 * <AUTHOR>
 * @date 2024/11/14
 */
import { ElButton } from "element-plus";
import { useExportFormEventBus, useExportSelectedEventBus } from "@/components/LAExport/eventbus";
import { shallowRef, toRaw, unref } from "vue";
import JsFileDownloader from "js-file-downloader";
import { useUserStore } from "@/stores/modules/user";
// 权限
import qs from "qs";
const userStore = useUserStore();

const props = withDefaults(
  defineProps<{
    beforeExport: (params: any) => any;
    downloadUrl: string;
    title: string;
    params?: object;
  }>(),
  {
    beforeExport: (params: any) => {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars,no-unused-vars
      const { selectedRows, ...rest } = params;
      if (Array.isArray(selectedRows))
        return {
          ids: [...selectedRows].map(({ id }) => id).join(",") || undefined,
          ...rest
        };
      return rest;
    }
  }
);
// eslint-disable-next-line vue/no-setup-props-destructure
const { beforeExport, downloadUrl } = props;
const searchParams = shallowRef({});
const selectedParams = shallowRef([]);
useExportFormEventBus.on(params => {
  searchParams.value = params;
});
useExportSelectedEventBus.on(params => {
  selectedParams.value = params;
});

const exportFile = () => {
  const finalParams = beforeExport({
    ...unref(searchParams),
    selectedRows: toRaw(unref(selectedParams)),
    ...props.params
  });
  if (!finalParams) return;
  new JsFileDownloader({
    url: downloadUrl,
    method: "POST",
    filename: `${props.title}.xlsx`,
    headers: [{ name: "Authorization", value: "Bearer " + userStore.token }],
    body: qs.stringify(finalParams)
  });
};
</script>

<style lang="scss" scoped></style>
