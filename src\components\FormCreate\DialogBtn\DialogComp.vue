<template>
  <el-dialog append-to-body class="__form-dialog" destroy-on-close>
    <template #header>
      <div class="form-dialog-header">
        <span>{{ $attrs.title }}</span>
        <span v-if="$attrs.subtitle">
          - {{ typeof $attrs.subtitle === "function" ? $attrs.subtitle($attrs.rowData) : $attrs.subtitle }}
        </span>
      </div>
    </template>
    <template v-for="(item, key, i) in slots" :key="i" v-slot:[key]>
      <slot :name="key"></slot>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
import { useSlots } from "vue";
import { ElDialog } from "element-plus";
const slots = useSlots();
</script>

<style lang="scss">
.__form-dialog {
  border-radius: 16px !important;
  .el-dialog__header {
    color: #1a1a1a;
    font-weight: bold;
    font-size: 20px;
    border-bottom: 1px solid #ebedf1;
    .el-dialog__headerbtn {
      height: 58px;
    }
  }

  .el-dialog__body {
    padding: 20px;
    .el-form-item__label {
      font-weight: bold;
      color: #1a1a1a;
      --el-color-danger: var(--el-color-primary);
    }
  }

  //--el-color-danger: var(--el-color-primary);
  //.el-form-item__error {
  //  color: var(--el-color-danger) !important;
}

.el-dialog__footer {
  border-top: 1px solid var(--el-border-color-lighter);
}
</style>
