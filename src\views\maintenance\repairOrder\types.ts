/**
 * @file 维修保养-维修工单-类型声明
 * <AUTHOR>
 * @date 2025/2/10
 */

// table字段声明
export const columnMap: any = new Map([
  ["维修工单号", "repairWorkOrderNumber"],
  ["矿车名称", "deviceName"],
  ["工单状态", "workOrderStatus"],
  ["报修人", "reportPersonName"],
  ["维修负责人", "repairResponsiblePersonName"],
  ["故障时间", "faultTime"],
  ["维修结果", "repairStatus"],
  ["故障码", "faultCode"],
  ["故障描述", "faultDescription"]
]);
// form字段声明
export const formMap: any = new Map([
  ["维修工单号", "repairWorkOrderNumber"],
  ["矿车名称", "deviceId"],
  ["故障码", "faultCode"],
  ["故障描述", "faultDescription"],
  ["工单状态", "workOrderStatus"],
  ["报修人", "reportPersonId"],
  ["报修人名称", "reportPersonName"],
  ["维修负责人", "repairResponsiblePersonId"],
  ["维修负责人名称", "repairResponsiblePersonName"],
  ["故障时间", "faultTime"]
]);
// 转让工单表单字段
export const transferOrderFormMap: any = new Map([
  ["维修负责人", "newResponsiblePersonId"],
  ["维修负责人名称", "newResponsiblePersonName"]
]);
// 填写维修结果表单字段
export const fillRepairResultFormMap: any = new Map([
  ["维修内容", "repairContent"],
  ["维修结果", "repairResults"]
]);
// 工单状态枚举
export enum OrderStatusEnum {
  /** 已关闭*/
  CLOSED = "closed",
  /** 待维修*/
  NEED_REPAIR = "needRepair"
}

// 工单状态信息
export const repairOrderStatusEnum = [
  {
    bg: "rgba(0, 194, 144, 0.1)",
    workOrderStatus: OrderStatusEnum.NEED_REPAIR,
    color: "rgba(0, 194, 144, 1)",
    text: "待维修"
  },
  {
    bg: "rgba(101, 102, 102, 0.1)",
    workOrderStatus: OrderStatusEnum.CLOSED,
    color: "rgba(101, 102, 102, 1)",
    text: "已关闭"
  }
];
// 维修结果枚举
export enum RepairStatusEnum {
  // 已修复
  REPAIRED = "repaired",
  // 未修复
  UN_REPAIRED = "unRepaired"
}
// 维修结果状态信息
export const repairedResultInfo = new Map([
  [RepairStatusEnum.REPAIRED, "已修复"],
  [RepairStatusEnum.UN_REPAIRED, "未修复"]
]);
export interface RepairRecord {
  id: number;
  personName: string;
  createDate: string;
  record: string;
  // 根据实际接口响应补充其他字段
}
