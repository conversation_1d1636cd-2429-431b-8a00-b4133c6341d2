/**
 * 设备数据管理器
 * 遵循KISS原则，提供简单的设备数据存储和查询功能
 */

import { ref, computed } from "vue";
import type {
  Device,
  DeviceType,
  APIDeviceData,
  WSDeviceInitData,
  DeviceStatistics,
  MineTrainDevice,
  BulldozerDevice,
  ChargingPileDevice,
  CrushingStationDevice,
  CockpitDevice
} from "./deviceTypes";
import { mergeDeviceData, validateDeviceData, processDeviceList, logDeviceUpdate, hasDeviceChanged } from "@/utils/deviceUtils";

// 设备数据存储
const devices = ref<Map<string, Device>>(new Map());
const statistics = ref<DeviceStatistics>({
  attendanceNumber: 0,
  leisureNumber: 0,
  rechargeBatteriesNumber: 0,
  faultsNumber: 0
});
const lastUpdated = ref<Date | null>(null);
const isLoading = ref(false);
const error = ref<Error | null>(null);

/**
 * 设备数据管理器
 */
export const useDeviceManager = () => {
  /**
   * 从HTTP API数据初始化设备列表
   */
  const initFromAPI = (apiData: APIDeviceData) => {
    try {
      isLoading.value = true;
      error.value = null;
      console.log("[DeviceManager] Initializing devices from API data:", apiData);

      const newDevices = new Map<string, Device>();

      // 处理矿车数据
      apiData.mineTrainDtoList?.forEach(item => {
        const device: MineTrainDevice = {
          id: item.id,
          name: item.name,
          code: item.code,
          status: item.status,
          deviceType: "mineTrain",
          driveMode: item.driveMode,
          electricQuantity: item.electricQuantity,
          taskFlag: item.taskFlag,
          taskCurrentStep: item.taskCurrentStep,
          errorFlag: item.errorFlag,
          errorLevel: item.errorLevel
        };
        newDevices.set(item.code, device);
      });

      // 处理挖机数据
      apiData.bulldozersList?.forEach(item => {
        const device: BulldozerDevice = {
          id: item.id,
          name: item.name,
          code: item.code,
          status: item.status,
          deviceType: "bulldozer"
        };
        newDevices.set(item.code, device);
      });

      // 处理充电桩数据
      apiData.chargingPileList?.forEach(item => {
        const device: ChargingPileDevice = {
          id: item.id,
          name: item.name,
          code: item.code,
          status: item.status,
          deviceType: "chargingPile"
        };
        newDevices.set(item.code, device);
      });

      // 处理破碎站数据
      apiData.crushingStationList?.forEach(item => {
        const device: CrushingStationDevice = {
          id: item.id,
          name: item.name,
          code: item.code,
          status: item.status,
          deviceType: "crushingStation"
        };
        newDevices.set(item.code, device);
      });

      // 处理驾驶舱数据
      apiData.cockpitsList?.forEach(item => {
        const device: CockpitDevice = {
          id: item.id,
          name: item.name,
          code: item.code,
          status: item.status,
          deviceType: "cockpit"
        };
        newDevices.set(item.code, device);
      });

      // 更新统计数据
      statistics.value = {
        attendanceNumber: apiData.attendanceNumber || 0,
        leisureNumber: apiData.leisureNumber || 0,
        rechargeBatteriesNumber: apiData.rechargeBatteriesNumber || 0,
        faultsNumber: apiData.faultsNumber || 0
      };

      devices.value = newDevices;
      lastUpdated.value = new Date();
    } catch (err) {
      error.value = err as Error;
      console.error("Failed to initialize devices from API:", err);
    } finally {
      isLoading.value = false;
    }
  };

  /**
   * 从WebSocket数据更新设备列表
   */
  const updateFromWebSocket = (wsData: WSDeviceInitData) => {
    try {
      // 更新挖机数据
      wsData.bulldozersList?.forEach(item => {
        const existingDevice = devices.value.get(item.code);
        if (existingDevice && existingDevice.deviceType === "bulldozer") {
          // 合并更新现有设备
          const updatedDevice: BulldozerDevice = {
            ...existingDevice,
            name: item.name || existingDevice.name,
            id: item.id || existingDevice.id,
            posture: item.posture
          };
          devices.value.set(item.code, updatedDevice);
        } else {
          // 创建新设备（如果不存在）
          const newDevice: BulldozerDevice = {
            id: item.id || "",
            name: item.name || "",
            code: item.code,
            status: 0, // 默认状态
            deviceType: "bulldozer",
            posture: item.posture
          };
          devices.value.set(item.code, newDevice);
        }
      });

      // 更新矿车数据
      wsData.mineTrainList?.forEach(item => {
        const existingDevice = devices.value.get(item.code);
        if (existingDevice && existingDevice.deviceType === "mineTrain") {
          // 合并更新现有设备
          const updatedDevice: MineTrainDevice = {
            ...existingDevice,
            name: item.name || existingDevice.name,
            id: item.id || existingDevice.id,
            electricQuantity: item.electricQuantity,
            driveMode: item.driveMode?.toString()
          };
          devices.value.set(item.code, updatedDevice);
        } else {
          // 创建新设备（如果不存在）
          const newDevice: MineTrainDevice = {
            id: item.id || "",
            name: item.name || "",
            code: item.code,
            status: 0, // 默认状态
            deviceType: "mineTrain",
            electricQuantity: item.electricQuantity,
            driveMode: item.driveMode?.toString()
          };
          devices.value.set(item.code, newDevice);
        }
      });

      lastUpdated.value = new Date();
    } catch (err) {
      error.value = err as Error;
      console.error("Failed to update devices from WebSocket:", err);
    }
  };

  /**
   * 更新单个设备
   */
  const updateSingleDevice = (deviceCode: string, deviceData: Partial<Device>) => {
    const existingDevice = devices.value.get(deviceCode);
    if (existingDevice) {
      const updatedDevice = { ...existingDevice, ...deviceData };
      devices.value.set(deviceCode, updatedDevice);
      lastUpdated.value = new Date();
    }
  };

  /**
   * 获取指定类型的设备列表
   */
  const getDevicesByType = (type: DeviceType) => {
    return computed(() => {
      return Array.from(devices.value.values()).filter(device => device.deviceType === type);
    });
  };

  /**
   * 根据code获取设备
   */
  const getDeviceByCode = (code: string) => {
    return computed(() => devices.value.get(code));
  };

  /**
   * 获取所有设备
   */
  const getAllDevices = () => {
    return computed(() => Array.from(devices.value.values()));
  };

  return {
    // 状态
    devices: computed(() => devices.value),
    statistics: computed(() => statistics.value),
    lastUpdated: computed(() => lastUpdated.value),
    isLoading: computed(() => isLoading.value),
    error: computed(() => error.value),

    // 方法
    initFromAPI,
    updateFromWebSocket,
    updateSingleDevice,
    getDevicesByType,
    getDeviceByCode,
    getAllDevices
  };
};
