<template>
  <form-create v-model:api="fApi" :option="option" :rule="rule"></form-create>
</template>
<script lang="tsx" setup>
/**
 * @file 设备管理-流量卡管理
 * <AUTHOR>
 * @date 2024/11/18
 */
import { ref } from "vue";
import { columnMap } from "./types";
import { flowCardMangeFormCreate } from "./components/formCreate";
import formCreate from "@form-create/element-ui";
import { addMobileTraffic, deleteMobileTraffic, getMobileTrafficList } from "@/api/modules/device";

const fApi = ref();
const option = {
  form: { inline: true },
  resetBtn: false,
  submitBtn: false
};

const rule = ref([
  {
    type: "SearchFormOperation",
    field: "v:search",
    wrap: { style: "marginBottom: 0" },
    children: [
      {
        type: "input",
        field: "search",
        props: {
          size: "default",
          placeholder: "流量卡名称/编码"
        }
      },
      // 新增
      {
        type: "AddBtn",
        slot: "suffix",
        props: {
          btn: { content: "新增流量卡", auth: "add" },
          dialog: {
            title: "新增流量卡"
          },

          size: "default",
          submitRequest: addMobileTraffic
        },
        children: [flowCardMangeFormCreate]
      }
    ]
  },
  {
    type: "ProTable",
    props: {
      columns: [
        {
          prop: columnMap.get("编码"),
          label: "编码"
        },
        {
          prop: columnMap.get("iccId"),
          label: "ICCID"
        },
        {
          prop: columnMap.get("所属设备"),
          label: "所属设备"
        },
        {
          prop: columnMap.get("激活时间"),
          label: "激活时间"
        },
        {
          prop: columnMap.get("流量池月套餐流量(GB)"),
          label: "流量池月套餐流量(GB)"
        },
        {
          prop: columnMap.get("月已使用流量(GB)"),
          label: "月已使用流量(GB)"
        },
        {
          prop: columnMap.get("流量池月剩余流量(GB)"),
          label: "流量池月剩余流量(GB)"
        },
        {
          prop: columnMap.get("创建时间"),
          label: "创建时间"
        },
        { prop: "operation", label: "操作", fixed: "right" }
      ],
      fetch: getMobileTrafficList,
      operations: [
        { content: "修改", action: "edit", auth: "update" },
        { content: "删除", action: "delete", auth: "delete", props: { style: { color: "rgba(242, 85, 85, 1)" } } }
      ]
    },

    children: [
      {
        type: "EditBtn",
        props: {
          action: "edit",
          dialog: {
            title: "修改流量卡"
          },
          submitRequest: addMobileTraffic
        },
        children: [flowCardMangeFormCreate]
      },
      {
        type: "ConfirmDialog",
        on: {
          // 监听弹窗组件抛出的的afterSubmit事件，用于刷新页面
          afterSubmit: () => {
            // 刷新，调用组件内部请求方法
            fApi.value.exec("v:search", "onSearch");
          }
        },
        props: {
          title: "是否删除流量卡",
          message: "删除后不可恢复",
          subtitle: row => {
            return row.cardNo;
          },
          action: "delete",
          // 模拟请求param：参数
          submitRequest: deleteMobileTraffic
        }
      }
    ]
  }
]);
</script>
<style lang="scss" scoped>
:deep(.el-row) {
  height: var(--page-height);
}

.el-form-item {
  margin-right: 4px !important;
}
</style>
