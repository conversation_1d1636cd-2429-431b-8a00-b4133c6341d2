<template>
  <div>
    <LADetailCard :detail-data="detailData" :detail-labels="detailLabels" :title="detailData?.position" />
  </div>
</template>

<script lang="tsx" setup>
/**
 * @file 维修保养-保养工单-详情-保养知识详情组件
 * <AUTHOR>
 * @date 2025/2/20
 */
import LADetailCard from "@/components/LADetailCard.vue";
import { nextTick, ref } from "vue";

import { columnMap, projectColumnMap } from "@/views/maintenance/maintenanceOrder/types.ts";
import { showValue } from "@/utils/helpers.ts";

const props = defineProps({
  formCreateInject: {
    type: Object,
    default: () => {}
  }
});
const detailData = ref();
const detailLabels = {
  [columnMap.get("矿卡型号")]: {
    label: "矿车型号",
    hiddenColon: true,
    formatter: (val: string) => {
      return val === "common" ? "通用" : showValue(val);
    }
  },
  [projectColumnMap.get("保养间隔")]: {
    label: "保养间隔",
    hiddenColon: true
  },
  [projectColumnMap.get("材料准备")]: {
    label: "材料准备",
    hiddenColon: true
  },
  [projectColumnMap.get("保养方式")]: {
    label: "保养方式",
    hiddenColon: true,
    formatter: (val: string) => {
      return <div v-html={val}></div>;
    }
  }
};

nextTick(() => {
  // 储存id信息
  detailData.value = props.formCreateInject.api.formData();
});
</script>

<style scoped></style>
