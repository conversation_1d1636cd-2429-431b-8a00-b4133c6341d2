<template>
  <form-create v-model:api="fApi" :option="option" :rule="rule"></form-create>
</template>
<script lang="tsx" setup>
/**
 * @file 系统管理-基础参数-物料管理
 * <AUTHOR>
 * @date 2024/11/21
 */
import { ref } from "vue";
import { getMaterialList, addMaterial, deleteMaterial, materialTrainSaveList } from "@/api/modules";
import { materialColumnMap } from "../types";
import formCreate from "@form-create/element-ui";
import { materialFormCreate, miningCarFormCreate } from "./formCreate";

const fApi = ref();
const option = {
  form: { inline: true },
  resetBtn: false,
  submitBtn: false
};

const rule = ref([
  {
    type: "SearchFormOperation",
    field: "v:search",
    wrap: { style: "marginBottom: 0" },
    children: [
      {
        type: "input",
        field: "search",
        style: { width: "200px" },
        props: {
          size: "default",
          placeholder: "物料名称/编码"
        }
      },
      // 新增
      {
        type: "AddBtn",
        slot: "suffix",
        props: {
          btn: { content: "新增物料" },
          dialog: { title: "新增物料" },
          size: "default",
          submitRequest: addMaterial
        },
        children: [materialFormCreate]
      },
      {
        type: "AddBtn",
        slot: "suffix",
        props: {
          btn: {
            content: "矿卡载重",
            icon: "",
            style: {
              borderRadius: "6px",
              color: "#1a1a1a",
              border: "1px solid rgba(189, 189, 191, 1)",
              background: "#fff"
            }
          },
          dialog: {
            title: "矿卡载重",
            // 绑定到弹窗根节点的样式
            class: "dialog-custom-width miningCar-class"
          },
          size: "default",
          submitRequest: params => {
            const newParmas = params.details.flatMap(item => item.materialTrainList || []);
            return materialTrainSaveList(newParmas);
          }
        },
        children: [miningCarFormCreate]
      }
    ]
  },
  {
    type: "ProTable",
    props: {
      columns: [
        {
          prop: materialColumnMap.get("物料名称"),
          label: "物料名称"
        },

        {
          prop: materialColumnMap.get("物料编码"),
          label: "物料编码"
        },

        { prop: "operation", label: "操作", style: { color: "red" }, fixed: "right" }
      ],
      fetch: getMaterialList,
      operations: [
        { content: "修改", action: "edit" },
        { content: "删除", action: "delete", props: { style: { color: "rgba(242, 85, 85, 1)" } } }
      ]
    },

    children: [
      {
        type: "EditBtn",
        props: {
          action: "edit",
          dialog: { title: "修改物料" },
          submitRequest: addMaterial
        },
        children: [materialFormCreate]
      },
      {
        type: "ConfirmDialog",
        on: {
          // 监听弹窗组件抛出的的afterSubmit事件，用于刷新页面
          afterSubmit: () => {
            // 刷新，调用组件内部请求方法
            fApi.value.exec("v:search", "onSearch");
          }
        },
        props: {
          action: "delete",
          subtitle: row => {
            return row.name;
          },
          title: "是否删除物料",
          message: "删除后不可恢复",
          // 模拟请求param：参数
          submitRequest: deleteMaterial
        }
      }
    ]
  }
]);
</script>
<style lang="scss" scoped>
:deep(.el-row) {
  height: calc(100vh - 178px) !important;
}

.el-form-item__content {
  width: 100%;
}
:deep(.search-form__operator) {
  width: 100%;
  .el-select--large .el-select__wrapper {
    min-height: initial;
    padding: 9px 16px;
  }
  .el-date-editor {
    min-height: 32px;
  }
}

.form-create {
  height: 100%;
}
</style>
<style lang="scss">
.miningCar-class {
  .form-dialog-header {
    font-size: 18px;
  }
  .el-dialog__body {
    padding: 20px;
    //min-width: 1300px !important;
  }
  .left-list {
    margin-right: 20px;
  }
  .el-row {
    margin: 0 !important;
    flex-wrap: nowrap;
  }

  .el-form-item--large {
    margin-bottom: 0 !important;
  }
  .el-form-item {
    margin-bottom: 0 !important;
  }
}
</style>
