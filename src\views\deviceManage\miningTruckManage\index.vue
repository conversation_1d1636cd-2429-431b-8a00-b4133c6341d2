<template>
  <form-create v-model:api="fApi" :option="option" :rule="rule"></form-create>
</template>
<script lang="tsx" setup>
/**
 * @file 设备管理-矿卡管理
 * <AUTHOR>
 * @date 2024/11/25
 */
import { ref } from "vue";
import { columnMap, miningTruckStatusEnum } from "./types";
import { miningTruckFormCreate, nodeFormCreate } from "./components/formCreate";
import formCreate from "@form-create/element-ui";
import { authorizations, deleteMineTrain, getMineTrainList, saveMineTrain } from "@/api/modules/device";

const fApi = ref();
const option = {
  form: { inline: true },
  resetBtn: false,
  submitBtn: false
};

const rule = ref([
  {
    type: "SearchFormOperation",
    field: "v:search",
    wrap: { style: "marginBottom: 0" },
    children: [
      {
        type: "input",
        field: "search",
        props: {
          size: "default",
          placeholder: "矿卡名称/编码"
        }
      },
      // 新增
      {
        type: "AddBtn",
        slot: "suffix",
        props: {
          btn: { content: "新增矿卡" },
          dialog: {
            title: "新增矿卡", // 绑定到弹窗根节点的样式
            class: "dialog-custom-width"
          },

          size: "default",
          submitRequest: saveMineTrain
        },
        children: [miningTruckFormCreate]
      }
    ]
  },
  {
    type: "ProTable",
    props: {
      columns: [
        {
          prop: columnMap.get("名称"),
          label: "名称"
        },
        {
          prop: columnMap.get("编码"),
          label: "编码"
        },
        {
          prop: columnMap.get("状态"),
          label: "状态",
          tag: true,
          enum: [...miningTruckStatusEnum]
        },
        {
          prop: columnMap.get("型号"),
          label: "型号"
        },
        {
          prop: columnMap.get("绑定流量卡"),
          label: "绑定流量卡"
        },
        {
          prop: columnMap.get("承载能力"),
          label: "承载能力(t)"
        },
        {
          prop: columnMap.get("ACU版本"),
          label: "ACU版本"
        },
        {
          prop: columnMap.get("授权剩余时长"),
          label: "授权剩余时长(天)"
        },
        { prop: "operation", label: "操作", fixed: "right" }
      ],
      fetch: getMineTrainList,
      operations: [
        { content: "修改", action: "edit" },
        {
          content: "节点",
          action: "node",
          onClick: row => {
            console.log(row);
          }
        },
        { content: "授权", action: "authorize" },
        { content: "删除", action: "delete", props: { style: { color: "rgba(242, 85, 85, 1)" } } }
      ]
    },
    children: [
      // 修改
      {
        type: "EditBtn",
        props: {
          action: "edit",
          dialog: {
            title: "修改矿卡",
            // 绑定到弹窗根节点的样式
            class: "dialog-custom-width"
          },
          submitRequest: saveMineTrain
        },
        children: [miningTruckFormCreate]
      },
      // 节点
      {
        type: "EditBtn",
        props: {
          action: "node",
          dialog: {
            title: "节点",
            footer: false,
            subtitle: row => {
              return row?.code;
            },
            // 绑定到弹窗根节点的样式
            class: "dialog-custom-node",
            style: { maxHeight: "70%" }
          },
          submitRequest: data => {
            // 提交的请求
            return Promise.resolve(true);
          }
        },
        on: {
          init(row, api) {
            api.children[0].findRule({ name: "nodeTable" }).props.params.deviceId = row.id;
          }
        },
        children: [nodeFormCreate]
      },
      // 授权
      {
        type: "ConfirmDialog",
        on: {
          // 监听弹窗组件抛出的的afterSubmit事件，用于刷新页面
          afterSubmit: () => {
            // 刷新，调用组件内部请求方法
            fApi.value.exec("v:search", "onSearch");
          }
        },
        props: {
          action: "authorize",
          subtitle: row => {
            return row.code;
          },
          title: "是否授权",
          message: "是否确认授权",
          // 模拟请求param：参数
          submitRequest: authorizations
        }
      },
      // 删除
      {
        type: "ConfirmDialog",
        on: {
          // 监听弹窗组件抛出的的afterSubmit事件，用于刷新页面
          afterSubmit: () => {
            // 刷新，调用组件内部请求方法
            fApi.value.exec("v:search", "onSearch");
          }
        },
        props: {
          action: "delete",
          subtitle: row => {
            return row.name;
          },
          title: "是否删除矿卡",
          message: "删除后不可恢复",
          // 模拟请求param：参数
          submitRequest: deleteMineTrain
        }
      }
    ]
  }
]);
</script>
<style lang="scss" scoped>
:deep(.el-row) {
  height: var(--page-height);
}

.el-form-item {
  margin-right: 4px !important;
}
</style>

<style lang="scss">
.dialog-custom-width {
  width: 50%;
}
.dialog-custom-node {
  .el-dialog__body {
    padding: 20px !important;
  }
  margin-top: 8vh !important;
  width: 80% !important;
  .table-box {
    height: 720px !important;
    border: 1px solid #dfe6ec;
  }
}
</style>
