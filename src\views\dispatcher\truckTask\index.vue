<template>
  <form-create v-model:api="fApi" :option="option" :rule="rule"></form-create>
</template>
<script lang="tsx" setup>
/**
 * @file 调度管理-矿车任务管理
 * <AUTHOR>
 * @date 2025/2/5
 */
import { ref } from "vue";
import { columnMap, truckTaskStatusEnum } from "./types";

import formCreate from "@form-create/element-ui";
import { getMineTrainList } from "@/api/modules/device";
import { detailFormCreate } from "@/views/dispatcher/truckTask/components/formCreate.ts";
import LASelect from "@/components/LASelect.tsx";
import { getAllMaterial } from "@/api/modules/dispatch.ts";

const fApi = ref();
const option = {
  form: { inline: true },
  resetBtn: false,
  submitBtn: false
};

const rule = ref([
  {
    type: "SearchFormOperation",
    field: "v:search",
    wrap: { style: "marginBottom: 0" },
    children: [
      {
        component: LASelect,
        field: "name",
        style: { width: "200px", lineHeight: "initial" },
        props: {
          fetch: getAllMaterial,
          replaceFields: { key: "id", label: "name", value: "id" },
          placeholder: "矿卡名称"
        }
      },
      {
        component: LASelect,
        field: "status",
        style: { width: "200px", lineHeight: "initial" },
        props: {
          fetch: getAllMaterial,
          replaceFields: { key: "id", label: "name", value: "id" },
          placeholder: "状态"
        }
      },
      {
        type: "LADateTimeRangePicker",
        name: "time",
        style: { lineHeight: "initial", height: "32px" },
        props: {
          type: "daterange",
          format: "YYYY-MM-DD",
          placeholder: ["起始日期", "截止日期"]
        },

        on: {
          "update:start": val => {
            if (val) {
              fApi.value.form["startTime"] = val;
            } else {
              fApi.value.form["startTime"] = undefined;
            }
          },
          "update:end": val => {
            if (val) {
              fApi.value.form["endTime"] = val;
            } else {
              fApi.value.form["endTime"] = undefined;
            }
          }
        }
      }
    ]
  },
  {
    type: "ProTable",
    props: {
      columns: [
        {
          prop: columnMap.get("矿车名称"),
          label: "矿车名称"
        },
        {
          prop: columnMap.get("任务类型"),
          label: "任务类型",
          render: scope => {
            return scope.row.name;
          }
        },
        {
          prop: columnMap.get("状态"),
          label: "状态",
          tag: true,
          enum: [...truckTaskStatusEnum]
        },
        {
          prop: columnMap.get("起点"),
          label: "起点"
        },
        {
          prop: columnMap.get("终点"),
          label: "终点"
        },
        {
          prop: columnMap.get("开始时间"),
          label: "开始时间"
        },
        {
          prop: columnMap.get("完成时间"),
          label: "完成时间"
        },

        { prop: "operation", label: "操作", fixed: "right" }
      ],
      fetch: getMineTrainList,
      operations: [{ content: "详情", action: "detail" }]
    },

    children: [
      // 详情组件
      {
        type: "DetailBtn",
        props: {
          action: "detail",
          dialog: {
            title: "矿车任务详情"
          }
        },
        children: [detailFormCreate]
      }
    ]
  }
]);
</script>
<style lang="scss" scoped>
:deep(.el-row) {
  height: calc(100vh - 138px) !important;
  .el-select--large .el-select__wrapper {
    min-height: initial;
  }
}

.el-form-item {
  margin-right: 4px !important;
}
</style>
