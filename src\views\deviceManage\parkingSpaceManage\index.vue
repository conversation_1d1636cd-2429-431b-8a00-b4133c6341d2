<template>
  <form-create v-model:api="fApi" :option="option" :rule="rule"></form-create>
</template>
<script lang="tsx" setup>
/**
 * @file 设备管理-停车位管理
 * <AUTHOR>
 * @date 2024/11/19
 */
import { ref } from "vue";
import { columnMap, statusEnum } from "./types";
import { parkingSpaceManageFormCreate, parkingSpaceManageStatusFormCreate } from "./components/formCreate";
import formCreate from "@form-create/element-ui";
import { deleteParkingSpot, getParkingSpotList, saveParkingSpot } from "@/api/modules/device";

const fApi = ref();
const option = {
  form: { inline: true },
  resetBtn: false,
  submitBtn: false
};

const rule = ref([
  {
    type: "SearchFormOperation",
    field: "v:search",
    wrap: { style: "marginBottom: 0" },
    children: [
      {
        type: "input",
        field: "search",
        props: {
          size: "default",
          placeholder: "停车位名称/编码"
        }
      },
      // 新增
      {
        type: "AddBtn",
        slot: "suffix",
        props: {
          btn: { content: "新增停车位" },
          dialog: {
            title: "新增停车位",
            // 绑定到弹窗根节点的样式
            class: "dialog-custom-width"
          },

          size: "default",
          submitRequest: saveParkingSpot
        },
        children: [parkingSpaceManageFormCreate]
      }
    ]
  },
  {
    type: "ProTable",
    props: {
      columns: [
        {
          prop: columnMap.get("名称"),
          label: "名称"
        },
        {
          prop: columnMap.get("编码"),
          label: "编码"
        },
        {
          prop: columnMap.get("对应充电桩"),
          label: "对应充电桩"
        },
        {
          prop: columnMap.get("状态"),
          label: "状态",
          tag: true,
          enum: [...statusEnum]
        },
        {
          prop: columnMap.get("经度"),
          label: "经度"
        },
        {
          prop: columnMap.get("纬度"),
          label: "纬度"
        },
        {
          prop: columnMap.get("海拔"),
          label: "海拔(米)"
        },
        {
          prop: columnMap.get("朝向"),
          label: "朝向"
        },

        { prop: "operation", label: "操作", fixed: "right" }
      ],
      fetch: getParkingSpotList,
      operations: [
        { content: "状态更改", action: "status", auth: "update" },
        { content: "修改", action: "edit", auth: "update" },
        { content: "删除", action: "delete", auth: "delete", props: { style: { color: "rgba(242, 85, 85, 1)" } } }
      ]
    },

    children: [
      {
        type: "EditBtn",
        props: {
          action: "status",
          dialog: {
            title: "状态更改"
          },
          submitRequest: params => {
            // TODO 接口
            console.log(params);
          }
        },
        children: [parkingSpaceManageStatusFormCreate]
      },
      {
        type: "EditBtn",
        props: {
          action: "edit",
          dialog: {
            title: "修改停车位",
            // 绑定到弹窗根节点的样式
            class: "dialog-custom-width"
          },
          submitRequest: saveParkingSpot
        },
        children: [parkingSpaceManageFormCreate]
      },
      {
        type: "ConfirmDialog",
        on: {
          // 监听弹窗组件抛出的的afterSubmit事件，用于刷新页面
          afterSubmit: () => {
            // 刷新，调用组件内部请求方法
            fApi.value.exec("v:search", "onSearch");
          }
        },
        props: {
          title: "是否删除停车位",
          message: "删除后不可恢复",
          subtitle: row => {
            return row[columnMap.get("名称")];
          },
          action: "delete",
          // 模拟请求param：参数
          submitRequest: deleteParkingSpot
        }
      }
    ]
  }
]);
</script>
<style lang="scss" scoped>
:deep(.el-row) {
  height: var(--page-height);
}

.el-form-item {
  margin-right: 4px !important;
}
</style>
