/**
 * @file 维修保养-保养知识库-类型声明文件
 * <AUTHOR>
 * @date 2025/2/27
 */

// 保养知识库表格字段声明
export const TramcarColumnMap: any = new Map([
  ["售卖车型号", "modelNumber"],
  ["售卖车型号名称", "modelNumberName"],
  ["保养部位", "position"],
  ["保养间隔(天)", "cycle"],
  ["材料准备", "material"],
  ["保养方式", "method"]
]);
// 表单字段声明
export const formMap = new Map([
  ["售卖车型号", "modelNumber"],
  ["保养部位", "position"],
  ["保养间隔(天)", "cycle"],
  ["材料准备", "material"],
  ["保养方式", "method"]
]);
// 充电桩表格字段声明
export const shiftScheduleColumnMap = new Map([
  ["故障描述", "faultDescription"],
  ["故障编码", "faultCode"],
  ["维修经验", "repairExperience"],
  ["设备型号", "modelNumber"]
]);
