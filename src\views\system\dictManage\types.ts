/**
 * @file 系统管理-字典管理-类型声明文件
 * <AUTHOR>
 * @date 2024/11/14
 */

// 字典类型枚举,是否是系统字典
export enum DictionaryTypeEnum {
  /** 系统字典*/
  SYSTEM_DICTIONARY = 1,
  /** 普通字典*/
  NORMAL_DICTIONARY = 2
}

// table字段声明
export const columnMap: any = new Map([
  ["字典名称", "dname"],
  ["字典编码", "code"],
  ["创建人", "userName"],
  ["创建时间", "createDate"],
  ["系统字典", "isSystem"],
  ["字典说明", "remark"],
  ["上级字典", "dname"]
]);
// 新增/修改form字段声明
export const formMap = new Map([
  ["上级字典名称", "pname"],

  ["字典名称", "dname"],
  ["字典编码", "code"],
  ["创建人", "userName"],
  ["创建时间", "createDate"],
  ["字典说明", "remark"],
  ["上级字典", "pcode"]
]);
