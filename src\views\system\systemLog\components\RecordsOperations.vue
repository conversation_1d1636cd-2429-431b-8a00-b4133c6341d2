<template>
  <form-create v-model:api="fApi" :option="option" :rule="rule"></form-create>
</template>
<script lang="tsx" setup>
/**
 * @file 系统管理-系统日志-操作记录
 * <AUTHOR>
 * @date 2024/11/14
 */
import { nextTick, ref } from "vue";
import { columnMap, operationStatusInfo } from "../types";
import formCreate, { Api } from "@form-create/element-ui";
import LASelect from "@/components/LASelect";
import { getOperateLogList, getDictionaryEnum } from "@/api/modules";

const fApi = ref();
const option = {
  form: { inline: true },
  resetBtn: false,
  forceCoverValue: true,
  submitBtn: false
};

const rule = ref([
  {
    type: "SearchFormOperation",
    field: "v:search",
    wrap: { style: "marginBottom: 0" },

    children: [
      {
        component: LASelect,
        field: "operateDeck",
        style: { width: "200px", lineHeight: "initial" },
        props: {
          placeholder: "操作端",
          list: [
            {
              label: "网页端",
              value: "网页端"
            },
            {
              label: "小程序",
              value: "小程序"
            }
          ]
        }
      },
      {
        component: LASelect,
        field: "owningModule",
        style: { width: "200px" },
        props: {
          fetch: getDictionaryEnum,
          replaceFields: { key: "id", label: "dname", value: "code" },
          params: { code: "ServiceModuleType" },
          placeholder: "服务模块"
        }
      },
      {
        component: LASelect,
        field: "status",
        style: { width: "200px" },
        props: {
          placeholder: "状态",
          list: [
            { label: "正常", value: 101 },
            { label: "异常", value: 102 }
          ]
        }
      },
      {
        type: "LADateTimeRangePicker",
        name: "time",

        style: { lineHeight: "initial", height: "32px" },
        props: {
          type: "daterange",
          format: "YYYY-MM-DD"
        },

        on: {
          "update:start": val => {
            if (val) {
              fApi.value.form["startTime"] = val;
            } else {
              fApi.value.form["startTime"] = undefined;
            }
          },
          "update:end": val => {
            if (val) {
              fApi.value.form["endTime"] = val;
            } else {
              fApi.value.form["endTime"] = undefined;
            }
          }
        }
      },
      {
        type: "input",
        field: "search",
        style: { width: "200px" },
        props: {
          size: "default",
          placeholder: "输入操作人/账号"
        }
      }
    ]
  },
  {
    type: "ProTable",
    props: {
      columns: [
        {
          prop: columnMap.get("操作人/账号"),
          label: "操作人/账号"
        },
        {
          prop: columnMap.get("操作端"),
          label: "操作端"
        },
        {
          prop: columnMap.get("服务模块"),
          label: "服务模块"
        },
        {
          prop: columnMap.get("调用接口"),
          label: "调用接口"
        },
        {
          prop: columnMap.get("请求方式"),
          label: "请求方式"
        },
        {
          prop: columnMap.get("响应时长"),
          label: "响应时长(ms)"
        },
        {
          prop: "status",
          label: "状态",
          tag: true,
          enum: [...operationStatusInfo]
        },
        {
          prop: columnMap.get("设备IP"),
          label: "设备IP"
        },
        {
          prop: columnMap.get("操作时间"),
          label: "操作时间"
        },
        { prop: "operation", label: "操作", style: { color: "red" }, fixed: "right" }
      ],
      fetch: getOperateLogList,
      operations: [
        {
          content: "详情",
          action: "detail"
        }
      ]
    },

    children: [
      {
        type: "EditBtn",
        props: {
          action: "detail",
          dialog: { title: "详情", footer: false, style: { maxHeight: "70%" } },
          submitRequest: data => {
            // 提交的请求
            return Promise.resolve(true);
          }
        },
        children: [
          {
            type: "form-create",
            // name: "operationRecordDetails",
            props: {
              option: { submitBtn: false },
              rule: [
                {
                  type: "LAShadowButtonGroup",
                  field: "LAShadowButtonGroup",
                  wrap: { style: "marginBottom: 0" },
                  style: { "margin-bottom": "10px" },
                  value: "request",
                  props: {
                    list: [
                      { label: "请求参数", value: "request" },
                      { label: "响应参数", value: "response" }
                    ]
                  },
                  update(value, rule, api: Api) {
                    nextTick(() => {
                      api.findRule({ name: "json" }).children = [
                        JSON.stringify(
                          JSON.parse(value === "request" ? api.formData().requestParams : api.formData().responseParams),
                          null,
                          2
                        )
                      ];
                    });
                  }
                },
                {
                  type: "pre",
                  name: "json",
                  style: {
                    flex: "none",
                    width: "100%",
                    maxHeight: "calc(70vh - 178px)",
                    overflow: "auto"
                  }
                }
              ]
            }
          }
        ]
      }
    ]
  }
]);
</script>
<style lang="scss" scoped>
:deep(.el-row) {
  height: calc(100vh - 178px) !important;
}

.el-form-item__content {
  width: 100%;
}

:deep(.search-form__operator) {
  width: 100%;

  .el-select--large .el-select__wrapper {
    min-height: initial;
    padding: 9px 16px;
  }

  .el-date-editor {
    min-height: 32px;
  }
}

.form-create {
  height: 100%;
}
</style>
