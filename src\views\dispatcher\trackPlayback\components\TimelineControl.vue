<template>
  <div class="timeline-control">
    <!-- 中间进度条区域 -->
    <div class="progress-section" v-if="showTimelineControl">
      <div class="progress-container">
        <el-slider
          :model-value="timelinePercent"
          @update:model-value="handleTimelineDrag"
          :min="0"
          :max="100"
          :step="0.1"
          :disabled="!hasData"
          :show-tooltip="false"
          @change="handleTimelineChange"
        />
      </div>
      <div class="time-range">
        <!-- 左侧开始时间 -->
        <div class="start-time">{{ formatDateTime(startTime) }}</div>
        <!-- 当前时间显示 -->
        <div class="current-time">
          <el-icon :size="14"><Clock /></el-icon>
          {{ formatDateTime(currentTime, "HH:mm:ss") }}
        </div>
        <!-- 右侧结束时间 -->
        <div class="end-time">{{ formatDateTime(endTime) }}</div>
      </div>
    </div>

    <!-- 播放控制 -->
    <div class="playback-controls" v-if="showTimelineControl">
      <ElIcon :size="48" color="#ffffff" style="cursor: pointer" @click="togglePlayback">
        <VideoPlay v-if="!isPlaying" />
        <VideoPause v-else />
      </ElIcon>
      <LASelect
        :model-value="playSpeed"
        @update:model-value="$emit('update:play-speed', $event)"
        :list="speedOptions"
        :replace-fields="{ key: 'value', label: 'label', value: 'value' }"
        placeholder="倍速"
        size="default"
        style="width: 88px"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ElButton, ElSlider } from "element-plus";
import LASelect from "@/components/LASelect";
import dayjs from "dayjs";

interface Props {
  currentTime: number;
  isPlaying: boolean;
  playSpeed: number;
  hasData: boolean;
  startTime: number;
  endTime: number;
  timelinePercent: number;
  showTimelineControl: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  currentTime: 0,
  isPlaying: false,
  playSpeed: 1,
  hasData: false,
  startTime: 0,
  endTime: 0,
  timelinePercent: 0
});

// 倍速选项
const speedOptions = [
  { value: 16, label: "16x" },
  { value: 8, label: "8x" },
  { value: 4, label: "4x" },
  { value: 2, label: "2x" },
  { value: 1, label: "1x" }
];

const emit = defineEmits<{
  "update:timelinePercent": [value: number];
  "update:play-speed": [value: number];
  "timeline-change": [percent: number];
  "timeline-drag": [percent: number];
  "toggle-playback": [];
}>();

// 格式化日期时间
const formatDateTime = (timestamp: number, format: string = "YYYY-MM-DD HH:mm:ss"): string => {
  if (!timestamp) return "----/--/-- --:--:--";
  return dayjs(timestamp).format(format);
};

// 处理时间轴变化
const handleTimelineChange = (percent: number | number[]) => {
  const value = Array.isArray(percent) ? percent[0] : percent;
  emit("update:timelinePercent", value);
  emit("timeline-change", value);
};

// 处理时间轴拖拽
const handleTimelineDrag = (percent: number | number[]) => {
  const value = Array.isArray(percent) ? percent[0] : percent;
  emit("update:timelinePercent", value);
  emit("timeline-drag", value);
};

// 切换播放状态
const togglePlayback = () => {
  emit("toggle-playback");
};
</script>

<style scoped>
.timeline-control {
  padding: 10px 20px;
  background: rgba(31, 33, 88, 1);
  height: 72px;
  border-radius: 0px 0px 8px 8px;
  display: flex;
  align-items: center;
  gap: 16px;
}

.time-range {
  width: 100%;
  display: flex;
  margin-top: -5px;
  justify-content: space-between;
}

.start-time,
.end-time {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  text-align: center;
}

.progress-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.progress-container {
  width: 100%;
}

.current-time {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #ffffff;
  padding: 3px 10px;
  border-radius: 4px;
  font-size: 12px;
  width: 96px;
  white-space: nowrap;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.playback-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 进度条样式调整 */
.progress-container :deep(.el-slider__runway) {
  background-color: rgba(255, 255, 255, 0.2);
  height: 6px;
  border-radius: 3px;
}

.progress-container :deep(.el-slider__button) {
  border: 2px solid #ffffff;
  background-color: #356afd;
  width: 24px;
  height: 24px;
  border-radius: 50%;
}

/* 倍速选择器样式 */
</style>
