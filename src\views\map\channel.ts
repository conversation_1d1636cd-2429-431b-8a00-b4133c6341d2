import { useBroadcastChannel } from "@vueuse/core";
export const EventChannel = {
  change: "map-change",
  action: "map-action",
  event: "map-event",
  update: "map-update"
};

type MapChangeData = { type: "map-change"; data: string };
type MapActionData = { type: "reset" | "location" | "dashed" | "location-move" | string; data: any };
type MapEventData = { type: "map-event"; data: any };
type MapUpdateData<T> = { type: "map-update"; data: T };

// 地图内部监听 外部的地图改变事件， 重新获取OSM地图数据
export const useMapChangeChannel = () => {
  return useBroadcastChannel<MapChangeData, MapChangeData>({
    name: EventChannel.change
  });
};
// 地图内部监听 外部的地图操作事件， 执行对应的操作
export const useMapActionChannel = () => {
  return useBroadcastChannel<MapActionData, MapActionData>({
    name: EventChannel.action
  });
};
// 地图内部发送事件， 外部监听
export const useMapEventChannel = () => {
  return useBroadcastChannel<MapEventData, MapEventData>({
    name: EventChannel.event
  });
};
// 外部发送更新事件， 地图内部监听
export const useMapUpdateChannel = <T extends any>() => {
  return useBroadcastChannel<MapUpdateData<T>, MapUpdateData<T>>({
    name: EventChannel.update
  });
};
