<template>
  <div class="search-form">
    <el-radio-group @change="onReportChange" v-model="formData.reportType" size="default">
      <el-radio-button v-for="item in reportTypeOptions" :label="item.label" :value="item.value" :key="item.value" />
    </el-radio-group>
    <div style="margin: 20px 0">
      <el-date-picker
        v-if="formData.reportType === 'week'"
        @change="onWeekChange"
        v-model="weekDate"
        type="week"
        format="YYYY [第] ww [周]"
        placeholder="选择日期"
        size="default"
        style="width: 180px"
      />
      <el-date-picker
        v-else
        v-model="formData.date"
        :type="datePickerType"
        :value-format="valueFormat"
        placeholder="选择日期"
        size="default"
        style="width: 180px"
      />
      <el-select v-model="formData.deviceId" clearable placeholder="全部车辆" size="default" style="width: 180px; margin: 0 4px">
        <el-option v-for="item in carList" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-button size="default" @click="onSearch" type="primary">查询</el-button>
      <el-button size="default" @click="onReset" style="margin-left: 4px">重置</el-button>
    </div>
  </div>
</template>
<script setup lang="ts">
import dayjs from "dayjs";
import "dayjs/locale/zh-cn";
import { ref, reactive, computed } from "vue";
import { getNoDeleteMineTrainList } from "@/api/modules/device";
import { ElMessage } from "element-plus";
import { getWeekRanges, type WeekRangesResult } from "@/utils/date";

dayjs.locale("zh-cn");

const emit = defineEmits(["search", "reportChange", "otherValue"]);

const defaultDate = dayjs().format("YYYY-MM-DD");

const weekDate = ref();
const formData = reactive({
  reportType: "day",
  date: defaultDate,
  deviceId: "",
  weekStartDateCurrent: "",
  weekEndDateCurrent: "",
  weekStartDateYoy: "",
  weekEndDateYoy: ""
});

interface optionsItem {
  label: string;
  value: string;
}

const carList = ref<optionsItem[]>([]);
getNoDeleteMineTrainList().then(res => {
  carList.value = res.data?.map((item: any) => ({ label: item.name, value: item.id }));
});

const reportTypeOptions = [
  { label: "日报", value: "day" },
  { label: "周报", value: "week" },
  { label: "月报", value: "month" },
  { label: "年报", value: "year" }
];
const onReportChange = (value: string) => {
  emit("reportChange", value);
  onReset();
};

const onWeekChange = (date: string | dayjs.Dayjs = dayjs()) => {
  const dateStr = dayjs(date).format("YYYY-MM-DD");
  setFormWeekValue(getWeekRanges(dateStr));
};

const datePickerType = computed(() => {
  const reportType = formData.reportType;
  return reportType === "day" ? "date" : reportType;
});

const valueFormat = ref("YYYY-MM-DD");
const getDefaultDate = (type: string) => {
  const now = dayjs();
  switch (type) {
    case "year":
      valueFormat.value = "YYYY";
      return now.format("YYYY");
    case "month":
      valueFormat.value = "YYYY-MM";
      return now.format("YYYY-MM");
    case "week":
      return now.format("YYYY-MM-DD");
    case "date":
      valueFormat.value = "YYYY-MM-DD";
      return now.format("YYYY-MM-DD");
    default:
      valueFormat.value = "YYYY-MM-DD";
      return now.format("YYYY-MM-DD");
  }
};

const onSearch = () => {
  if (formData.reportType === "week") {
    if (!weekDate.value) return ElMessage.warning("请选择日期");
    setFormWeekValue(getWeekRanges(weekDate.value));
  } else if (!formData.date) {
    return ElMessage.warning("请选择日期");
  }

  const formValue = { ...formData };
  for (const key in formValue) {
    if (!formValue[key]) {
      delete formValue[key];
    }
  }
  emit("otherValue", weeklyCurrentDate.value);
  emit("search", formValue);
};

const weeklyCurrentDate = ref("");
const setFormWeekValue = (weekInfo: WeekRangesResult) => {
  console.log("weekInfo", weekInfo);
  const { currentWeekRange, lastYearWeekRange, targetDate } = weekInfo;
  formData.weekStartDateCurrent = currentWeekRange.start;
  formData.weekEndDateCurrent = currentWeekRange.end;
  formData.weekStartDateYoy = lastYearWeekRange.start;
  formData.weekEndDateYoy = lastYearWeekRange.end;
  weeklyCurrentDate.value = targetDate;
};

const onReset = () => {
  weekDate.value = "";
  formData.date = "";
  for (const key in formData) {
    if (Object.prototype.hasOwnProperty.call(formData, key)) {
      if (key === "date") {
        const date = getDefaultDate(datePickerType.value) as string;
        if (datePickerType.value === "week") {
          weekDate.value = date;
        } else {
          formData[key] = date;
        }
      } else if (key !== "reportType") {
        formData[key] = "";
      }
    }
  }
  onSearch();
};
</script>
