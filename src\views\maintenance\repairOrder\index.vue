<template>
  <form-create v-model:api="fApi" :option="option" :rule="rule"></form-create>
</template>
<script lang="tsx" setup>
/**
 * @file 维修保养-维修工单-页面
 * <AUTHOR>
 * @date 2025/2/10
 */

import { ref } from "vue";
import { columnMap, repairOrderStatusEnum, repairedResultInfo } from "./types";
import formCreate from "@form-create/element-ui";
import { getAllMineTrainList } from "@/api/modules/device";
import LASelect from "@/components/LASelect.tsx";
import { repairOrderFormCreate } from "@/views/maintenance/repairOrder/components/formCreate.ts";
import { detailFormCreate } from "@/views/maintenance/repairOrder/components/detailFormCreate.ts";
import { deviceRepairWorkOrderSave, getRepairWorkOrderList } from "@/api/modules/repair.ts";

import { useUserStore } from "@/stores/modules/user.ts";
import { showValue } from "@/utils/helpers.ts";
const userStore = useUserStore();

const fApi = ref();
const option = {
  form: { inline: true },
  resetBtn: false,
  submitBtn: false
};

const rule = ref([
  {
    type: "SearchFormOperation",
    field: "v:search",
    wrap: { style: "marginBottom: 0" },
    children: [
      {
        component: LASelect,
        field: "deviceId",
        style: { width: "200px", lineHeight: "initial" },
        props: {
          fetch: getAllMineTrainList,
          replaceFields: { key: "id", label: "name", value: "id" },
          placeholder: "矿卡名称"
        }
      },
      {
        component: LASelect,
        field: "orderStatus",
        style: { width: "200px", lineHeight: "initial" },
        props: {
          list: [
            {
              label: "待维修",
              value: "needRepair"
            },
            {
              label: "已关闭",
              value: "closed"
            }
          ],

          placeholder: "工单状态"
        }
      },
      {
        type: "input",
        field: "repairUserName",
        props: {
          size: "default",
          placeholder: "维修/维修人"
        }
      },
      {
        type: "input",
        field: "repairWorkOrderNumber",
        props: {
          size: "default",
          placeholder: "维修工单号"
        }
      },
      {
        type: "LADateTimeRangePicker",
        style: { lineHeight: "initial", height: "32px" },
        props: {
          type: "daterange",
          format: "YYYY-MM-DD",
          placeholder: ["起始日期", "截止日期"]
        },
        on: {
          "update:start": val => {
            if (val) {
              fApi.value.form["startDate"] = val;
            } else {
              fApi.value.form["startDate"] = undefined;
            }
          },
          "update:end": val => {
            if (val) {
              fApi.value.form["endDate"] = val;
            } else {
              fApi.value.form["endDate"] = undefined;
            }
          }
        }
      },
      {
        type: "AddBtn",
        slot: "suffix",
        props: {
          btn: { content: "新增", auth: "add" },
          action: "add",
          dialog: { title: "新增维修工单" },
          size: "default",
          isCustomDialog: true,
          formatter: (): any => {
            const userInfo = userStore.userInfo;
            // 初始化一个默认项
            return {
              reportPersonId: userInfo.id,
              reportPersonName: userInfo.employeeName
            };
          },
          submitRequest: deviceRepairWorkOrderSave
        },
        children: [repairOrderFormCreate]
      }
    ]
  },
  {
    type: "ProTable",
    props: {
      columns: [
        {
          prop: columnMap.get("维修工单号"),
          label: "维修工单号"
        },
        {
          prop: columnMap.get("工单状态"),
          label: "工单状态",
          tag: true,
          enum: [...repairOrderStatusEnum]
        },
        {
          prop: columnMap.get("矿车名称"),
          label: "矿车名称"
        },
        {
          prop: columnMap.get("报修人"),
          label: "报修人"
        },
        {
          prop: columnMap.get("维修负责人"),
          label: "维修负责人"
        },
        {
          prop: columnMap.get("故障时间"),
          label: "故障时间"
        },
        {
          prop: columnMap.get("维修结果"),
          label: "维修结果",
          render: scope => {
            return showValue(repairedResultInfo.get(scope.row.repairStatus));
          }
        },
        { prop: "operation", label: "操作", fixed: "right" }
      ],
      fetch: getRepairWorkOrderList,
      operations: [{ content: "详情", action: "detail" }]
    },
    children: [
      // 详情组件
      {
        type: "DetailBtn",
        props: {
          action: "detail",
          dialog: {
            class: "repair-order-detail",
            title: "维修工单详情"
          }
        },
        children: [detailFormCreate]
      }
    ]
  }
]);
</script>
<style lang="scss" scoped>
:deep(.el-row) {
  height: calc(100vh - 138px) !important;

  .el-select--large .el-select__wrapper {
    min-height: initial;
  }
}

.el-form-item {
  margin-right: 4px !important;
}
</style>
<style>
.repair-order-detail {
  width: 900px !important;
}
</style>
