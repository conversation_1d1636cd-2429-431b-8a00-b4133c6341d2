<template>
  <div class="real-time-data">
    <LACard :showHeader="false" shadow="never">
      <LATitle title="实时数据"></LATitle>
      <!-- 产量图表 -->
      <LineChart />
    </LACard>
  </div>
</template>

<script lang="ts" setup>
/**
 * @file 首页-实时数据
 * <AUTHOR>
 * @date 2025/1/21
 */
import LATitle from "@/components/LATitle.vue";
import LACard from "@/components/LACard/index.vue";
import LineChart from "@/views/home/<USER>/RealTimeData/LineChart.vue";
</script>

<style lang="scss" scoped>
:deep(.el-card__body) {
  display: flex;
  flex-direction: column;
}
</style>
