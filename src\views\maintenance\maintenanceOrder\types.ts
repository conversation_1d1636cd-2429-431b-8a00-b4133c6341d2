/**
 * @file 维修保养-保养工单-类型声明文件
 * <AUTHOR>
 */

// table字段声明
export const columnMap: any = new Map([
  ["保养工单号", "number"],
  ["工单状态", "status"],
  ["逾期天数", "overdueDays"],
  ["矿车名称", "deviceName"],
  ["矿卡型号", "modelNumber"],
  ["保养间隔", "cycle"],
  ["工单生成时间", "maintenanceTime"],
  ["保养负责人", "chargePerson"],
  ["保养完成时间", "completionTime"]
]);
export const projectColumnMap: any = new Map([
  ["保养部位", "position"],
  ["保养间隔", "cycle"],
  ["材料准备", "material"],
  ["保养方式", "method"]
]);
// 填写保养结果表单
export const maintenanceResultFormMap = new Map([
  ["保养备注", "maintenanceContent"],
  ["保养结果", "maintenanceResults"]
]);
// 转让表单
export const transferFormMap = new Map([
  ["保养负责人", "newResponsiblePersonId"],
  ["保养负责人名称", "newResponsiblePersonName"]
]);
// 派工表单
export const assignFormMap = new Map([
  ["派工负责人", "userId"],
  ["派工负责人名称", "personName"],
  ["派工单号", "workOrderId"]
]);
// 状态枚举
export enum StatusEnum {
  /** 未派工 */
  NOT_ASSIGNED = "notAssigned",
  /** 待保养 */
  TO_MAINTAIN = "waitingForMaintenance",
  /** 已关闭 */
  CLOSED = "completed"
}
// 任务状态
export const maintenanceStatusInfo = [
  {
    bg: "rgba(234, 240, 255)",
    color: "rgba(53, 106, 253, 1)",
    status: StatusEnum.NOT_ASSIGNED,
    text: "未派工"
  },
  {
    bg: "rgba(229, 249, 244)",
    status: StatusEnum.TO_MAINTAIN,
    color: "rgba(0, 194, 144, 1)",
    text: "待保养"
  },
  {
    text: "已关闭",
    bg: "rgba(239, 239, 239)",
    color: "rgba(101, 102, 102, 1)",
    status: StatusEnum.CLOSED
  }
];
// 提交保养结果的表单字段
export const maintenanceResultFieldsMap = new Map([
  ["保养部位", "position"],
  ["保养方式", "content"]
]);
