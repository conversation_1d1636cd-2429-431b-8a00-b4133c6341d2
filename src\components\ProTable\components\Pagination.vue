<template>
  <!-- 分页组件 -->
  <el-pagination
    :background="true"
    :current-page="pageable.current"
    :page-size="pageable.size"
    :page-sizes="[10, 25, 50, 100]"
    :total="pageable.total"
    layout="total, sizes, prev, pager, next, jumper"
    size="default"
    @size-change="handleSizeChange"
    @current-change="handleCurrentChange"
  />
</template>

<script lang="ts" setup>
interface Pageable {
  current: number;
  size: number;
  total: number;
}

interface PaginationProps {
  pageable: Pageable;
  handleSizeChange: (size: number) => void;
  handleCurrentChange: (currentPage: number) => void;
}

defineProps<PaginationProps>();
</script>
<style lang="scss" scoped>
:deep(.el-pager) {
  li {
    background-color: #fff !important;
    border: 1px solid #dde2e8;
    --el-pagination-border-radius: 4px !important;
  }
}
:deep(.btn-next),
:deep(.btn-prev) {
  background-color: #fff !important;
  border-radius: 4px !important;
  border: 1px solid #dde2e8;
}
:deep(.btn-prev) {
  --el-pagination-item-gap: 0;
}

/* pagination */
:deep(.el-pagination__total) {
  margin-left: 10px;
}
:deep(.el-pagination__sizes.el-pagination__sizes) {
  margin: 0 10px;
  .el-input {
    width: 98px;
  }
}

:deep(.el-pagination__jump.el-pagination__jump) {
  margin-left: 0;
}
:deep(li.is-active) {
  background-color: #fff !important;
  color: #356afd !important;
  border: 1px solid #356afd !important;
}
</style>
