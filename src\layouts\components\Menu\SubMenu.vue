<template>
  <template v-for="subItem in menuList" :key="subItem.path">
    <!--多层级菜单渲染-->
    <el-sub-menu v-if="subItem.children?.length" :index="subItem.path">
      <template #title>
        <!--   路径前的icon   -->
        <img v-show="subItem.meta.icon" :src="imgSrc(subItem.meta.icon, subItem.path)" class="menu-title-icon" />
        <!--        <SvgIcon class="menu-title-icon" name="IconDispatchActivation" />-->

        <span class="sle">{{ subItem.meta.title }}</span>
      </template>
      <SubMenu :activeMenu="activeMenu" :menu-list="subItem.children" :operate-menu="operateMenu" />
    </el-sub-menu>
    <!--单级菜单渲染-->
    <el-menu-item v-else :index="subItem.path" @click="handleClickMenu(subItem)">
      <template #title>
        <img v-show="subItem.meta.icon" :src="imgSrc(subItem.meta.icon, subItem.path)" class="menu-title-icon" />
        <span class="sle">{{ subItem.meta.title }}</span>
      </template>
    </el-menu-item>
  </template>
</template>

<script lang="ts" setup>
/**
 * @file 默认系统布局-侧边菜单栏组件
 * <AUTHOR>
 * @date 2024/10/31
 */
import { useRouter } from "vue-router";
// 菜单名称前的icon,对应route.meta.icon字段的值,或者是直接渲染一个url
import * as prefixIcon from "../icon/index";

const props = defineProps<{ menuList: Menu.MenuOptions[]; activeMenu: ""; operateMenu: "open" | "close" }>();
const imgSrc = (iconName: string, path: string) => {
  if (!iconName) return;
  // iconName后加上Activation
  if (props.activeMenu === path) {
    return props.activeMenu === path && props.operateMenu === "open" ? prefixIcon[`${iconName}Activation`] : prefixIcon[iconName];
  }
  return prefixIcon[iconName];
};

const router = useRouter();
const handleClickMenu = (subItem: Menu.MenuOptions) => {
  if (subItem.meta.isLink) return window.open(subItem.meta.isLink, "_blank");
  router.push(subItem.path);
};
</script>

<style lang="scss">
.is-opened {
  .el-sub-menu__title {
    background-color: #0e165f;
    color: #fff;
  }

  .el-menu {
    background-color: #0e165f;
  }
}

.menu-title-icon {
  width: 20px !important;
  height: 20px !important;
  filter: brightness(0.8);
}

.sle {
  margin-left: 8px;
}

.el-sub-menu .el-sub-menu__title:hover {
  color: var(--el-menu-hover-text-color) !important;
  background-color: transparent !important;
}

.el-sub-menu {
  .el-sub-menu__title {
    height: 56px;
  }
}

.el-menu--collapse {
  .is-active {
    .el-sub-menu__title {
      color: #ffffff !important;
      background-color: var(--el-color-primary) !important;
    }
  }
}

.el-menu-item {
  border: 1px solid #0e165f;
  height: 46px;
  &:hover {
    color: var(--el-menu-hover-text-color);
  }

  &.is-active {
    color: var(--el-menu-active-color) !important;
    background: linear-gradient(90deg, #356afd 0%, rgba(27, 53, 127, 0) 100%);
  }
}

.vertical,
.classic,
.transverse {
  .el-menu-item {
    height: 46px;

    &.is-active {
      &::before {
        left: 0;
      }
    }
  }
}

.columns {
  .el-menu-item {
    &.is-active {
      &::before {
        right: 0;
      }
    }
  }
}
</style>
