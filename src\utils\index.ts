import { isArray } from "@/utils/is";
import { FieldNamesProps } from "@/components/ProTable/interface";
import { cloneDeep } from "lodash-unified";
import { RouteRecordRaw } from "vue-router";
import { useAuthStore } from "@/stores/modules/auth.ts";

const mode = import.meta.env.VITE_ROUTER_MODE;

/**
 * 整理路由中meta下的btnAuth
 * @param arr 处理好的的路由数组
 * @return {路由名: ['add', 'delete'], ...} 整理好的权限数组
 * <AUTHOR>
 * @date 2024/11/26
 */
export function ascendingBtnAuth(arr: any[]) {
  const btnAuthArr = {};

  function loop(list) {
    if (list.meta.leafFlag && list.meta.btnAuth) {
      btnAuthArr[list.name] = list.meta.btnAuth;
    }

    if (list.children) {
      list.children.forEach(child => loop(child));
    }
  }
  arr.forEach(data => loop(data));

  return btnAuthArr;
}
// 递归删除空键的工具函数
export function removeEmptyValues<T>(obj: T): T {
  // 非对象/数组/null/特殊对象直接返回
  if (typeof obj !== "object" || obj === null || obj instanceof Date || obj instanceof Blob || obj instanceof FormData) {
    return obj;
  }

  // 处理数组（递归处理每个元素）
  if (Array.isArray(obj)) {
    return obj.map(item => removeEmptyValues(item)) as unknown as T;
  }

  // 处理普通对象
  const newObj: Record<string, any> = {};
  for (const [key, value] of Object.entries(obj)) {
    // 递归处理嵌套对象
    const processedValue = removeEmptyValues(value);

    // 删除值为空字符串的键（关键修改点）
    if (processedValue === "") continue;

    // 保留其他所有键值对
    newObj[key] = processedValue;
  }
  return newObj as T;
}
/**
 * 按照路由中meta下的rank属性升序来排序路由
 * @param arr 未排序的路由数组
 * @return {any[]} 已经排序的好的路由
 * <AUTHOR>
 * @date 2024/11/26
 */
export function ascending(arr: any[]) {
  arr.forEach(item => {
    if (item.children && item.children.length) {
      item.children = ascending(item.children);
    }
  });
  // 排序操作
  return arr.sort((a: { meta: { rank: number } }, b: { meta: { rank: number } }) => {
    return a?.meta?.rank - b?.meta?.rank;
  });
}
/**
 * 根据后端路由返回后的路由信息 处理重定向
 * @param arrRoutes 没有处理重定向之前的路由信息
 * @return {Array<>} 处理完重定向
 * <AUTHOR>
 * @date 2024/11/26
 */
export function addRoutesRedirect(arrRoutes: Array<RouteRecordRaw>) {
  if (!arrRoutes || !arrRoutes.length) return;
  arrRoutes.forEach((v: RouteRecordRaw) => {
    // 如果有子菜单,处理当前菜单重定向目标为第一个子菜单
    if (v.children && v.children.length) {
      // 如果路由有一个noRedirect属性,则不进行处理
      if (!v?.meta?.noRedirect) {
        v.redirect = v.redirect || v.children[0].path;
      }
      // 递归处理子菜单
      addRoutesRedirect(v.children);
    }
  });
  return arrRoutes;
}
/**
 * @description 使用递归处理路由菜单 ,返回前端需要的数据格式
 * @param [] menuList 所有菜单列表
 * @return {any}组装完成的路由数据
 */
export function processAsyncRoutes(menuList: any[]) {
  if (!menuList || !menuList.length) {
    return [];
  }
  function loop(list) {
    return cloneDeep(list).map(route => {
      /**
       * 后端返回的数据中,除了菜单数据还有页面对应菜单下的按钮数据,
       * 如果是菜单数据且为菜单的叶子节点,则其会有一个属性leafFlag为true
       * <AUTHOR>
       * @date 2024/11/26
       * */
      let children = [];
      if (route.children && route.children.length && route.leafFlag === false) {
        children = loop(route.children);
      }
      const { name, image, leafFlag, sort, url, resourceFunction, menuCode } = route;
      // 该菜单下的按钮权限
      let btnAuth = [];
      /* 处理叶子节点菜单上的权限*/
      if (leafFlag && resourceFunction) {
        // 把菜单的权限全部放在btnAuth数组中,然后放在菜单的meta元数据中,方便使用
        btnAuth = resourceFunction.split(",");
      }
      // 组装成前端路由需要的格式
      return {
        // 路由的跳转路径
        path: url,
        // 路由的标识
        name: menuCode,
        component: url,
        // 子级路由
        children,
        // 路由元信息
        meta: {
          // 菜单名称
          title: name,
          // 菜单排序
          rank: sort,
          // 菜单的icon
          icon: image,
          leafFlag: leafFlag,
          // 菜单的按钮权限信息
          btnAuth,
          ...routeMetaMap.get(menuCode)
        }
      };
    });
  }
  return loop(menuList);
}
/**
 * @description  路由参数配置简介
 * @param path ==> 路由菜单访问路径
 * @param name ==> 路由 name (对应页面组件 name, 可用作 KeepAlive 缓存标识 && 按钮权限筛选)
 * @param redirect ==> 路由重定向地址
 * @param component ==> 视图文件路径
 * @param meta ==> 路由菜单元信息
 * @param meta.icon ==> 菜单和面包屑对应的图标
 * @param meta.title ==> 路由标题 (用作 document.title || 菜单的名称)
 * @param meta.activeMenu ==> 当前路由为详情页时，需要高亮的菜单
 * @param meta.isLink ==> 路由外链时填写的访问地址
 * @param meta.isHide ==> 是否在菜单中隐藏 (通常列表详情页需要隐藏)
 * @param meta.isFull ==> 菜单是否全屏 (示例：数据大屏页面)
 * @param meta.isAffix ==> 菜单是否固定在标签页中 (首页通常是固定项)
 * @param meta.isKeepAlive ==> 当前路由是否缓存
 * */
const routeMetaMap = new Map([
  ["home", { isAffix: true, isHide: true }],
  ["copilot", { isHide: true, isFull: true, isAffix: true }],
  ["dictList", { isHide: true }],
  ["AccountCenter", { isHide: true }],
  ["menuMange", { isKeepAlive: true }],
  ["systemLog", { isKeepAlive: true }]
]);
/**
 * @description 获取localStorage
 * @param {String} key Storage名称
 * @returns {String}
 */
export function localGet(key: string) {
  const value = window.localStorage.getItem(key);
  try {
    return JSON.parse(window.localStorage.getItem(key) as string);
  } catch (error) {
    return value;
  }
}

/**
 * @description 存储localStorage
 * @param {String} key Storage名称
 * @param {*} value Storage值
 * @returns {void}
 */
export function localSet(key: string, value: any) {
  window.localStorage.setItem(key, JSON.stringify(value));
}

/**
 * @description 清除localStorage
 * @param {String} key Storage名称
 * @returns {void}
 */
export function localRemove(key: string) {
  window.localStorage.removeItem(key);
}

/**
 * @description 清除所有localStorage
 * @returns {void}
 */
export function localClear() {
  window.localStorage.clear();
}

/**
 * @description 判断数据类型
 * @param {*} val 需要判断类型的数据
 * @returns {String}
 */
export function isType(val: any) {
  if (val === null) return "null";
  if (typeof val !== "object") return typeof val;
  else return Object.prototype.toString.call(val).slice(8, -1).toLocaleLowerCase();
}
// 判断是否有某个菜单的权限
/**
 * @description 判断是否有某个菜单的权限
 * @param {String} menuCode 菜单标识
 * @returns {Boolean}
 */
export function isHasMenu(menuCode: string) {
  const authStore = useAuthStore();
  console.log(authStore.flatMenuListGet);
  // authStore.flatMenuListGet遍历每一项的name属性，如果和传入的menuCode相等，则返回true，否则返回false
  return authStore.flatMenuListGet.some((item: any) => item.name === menuCode);
}
/**
 * @description 生成唯一 uuid
 * @returns {String}
 */
export function generateUUID() {
  let uuid = "";
  for (let i = 0; i < 32; i++) {
    let random = (Math.random() * 16) | 0;
    if (i === 8 || i === 12 || i === 16 || i === 20) uuid += "-";
    uuid += (i === 12 ? 4 : i === 16 ? (random & 3) | 8 : random).toString(16);
  }
  return uuid;
}

/**
 * 判断两个对象是否相同
 * @param {Object} a 要比较的对象一
 * @param {Object} b 要比较的对象二
 * @returns {Boolean} 相同返回 true，反之 false
 */
export function isObjectValueEqual(a: { [key: string]: any }, b: { [key: string]: any }) {
  if (!a || !b) return false;
  let aProps = Object.getOwnPropertyNames(a);
  let bProps = Object.getOwnPropertyNames(b);
  if (aProps.length != bProps.length) return false;
  for (let i = 0; i < aProps.length; i++) {
    let propName = aProps[i];
    let propA = a[propName];
    let propB = b[propName];
    if (!b.hasOwnProperty(propName)) return false;
    if (propA instanceof Object) {
      if (!isObjectValueEqual(propA, propB)) return false;
    } else if (propA !== propB) {
      return false;
    }
  }
  return true;
}

/**
 * @description 生成随机数
 * @param {Number} min 最小值
 * @param {Number} max 最大值
 * @returns {Number}
 */
export function randomNum(min: number, max: number): number {
  let num = Math.floor(Math.random() * (min - max) + max);
  return num;
}

/**
 * @description 获取当前时间对应的提示语
 * @returns {String}
 */
export function getTimeState() {
  let timeNow = new Date();
  let hours = timeNow.getHours();
  if (hours >= 6 && hours <= 10) return `早上好 ⛅`;
  if (hours >= 10 && hours <= 14) return `中午好 🌞`;
  if (hours >= 14 && hours <= 18) return `下午好 🌞`;
  if (hours >= 18 && hours <= 24) return `晚上好 🌛`;
  if (hours >= 0 && hours <= 6) return `凌晨好 🌛`;
}

/**
 * @description 获取浏览器默认语言
 * @returns {String}
 */
export function getBrowserLang() {
  let browserLang = navigator.language ? navigator.language : navigator.browserLanguage;
  let defaultBrowserLang = "";
  if (["cn", "zh", "zh-cn"].includes(browserLang.toLowerCase())) {
    defaultBrowserLang = "zh";
  } else {
    defaultBrowserLang = "en";
  }
  return defaultBrowserLang;
}

/**
 * @description 获取不同路由模式所对应的 url + params
 * @returns {String}
 */
export function getUrlWithParams() {
  const url = {
    hash: location.hash.substring(1),
    history: location.pathname + location.search
  };
  return url[mode];
}

/**
 * @description 使用递归扁平化菜单，方便添加动态路由
 * @param {Array} menuList 菜单列表
 * @returns {Array}
 */
export function getFlatMenuList(menuList: Menu.MenuOptions[]): Menu.MenuOptions[] {
  let newMenuList: Menu.MenuOptions[] = JSON.parse(JSON.stringify(menuList));

  return newMenuList.flatMap(item => [item, ...(item.children ? getFlatMenuList(item.children) : [])]);
}

/**
 * @description 使用递归过滤出需要渲染在左侧菜单的列表 (需剔除 isHide == true 的菜单)
 * @param {Array} menuList 菜单列表
 * @returns {Array}
 * */
export function getShowMenuList(menuList: Menu.MenuOptions[]) {
  let newMenuList: Menu.MenuOptions[] = JSON.parse(JSON.stringify(menuList));
  return newMenuList.filter(item => {
    item.children?.length && (item.children = getShowMenuList(item.children));
    return !item.meta?.isHide;
  });
}

/**
 * @description 使用递归找出所有面包屑存储到 pinia/vuex 中
 * @param {Array} menuList 菜单列表
 * @param {Array} parent 父级菜单
 * @param {Object} result 处理后的结果
 * @returns {Object}
 */
export const getAllBreadcrumbList = (menuList: Menu.MenuOptions[], parent = [], result: { [key: string]: any } = {}) => {
  for (const item of menuList) {
    result[item.path] = [...parent, item];
    if (item.children) getAllBreadcrumbList(item.children, result[item.path], result);
  }
  return result;
};

/**
 * @description 使用递归处理路由菜单 path，生成一维数组 (第一版本地路由鉴权会用到，该函数暂未使用)
 * @param {Array} menuList 所有菜单列表
 * @param {Array} menuPathArr 菜单地址的一维数组 ['**','**']
 * @returns {Array}
 */
export function getMenuListPath(menuList: Menu.MenuOptions[], menuPathArr: string[] = []): string[] {
  for (const item of menuList) {
    if (typeof item === "object" && item.path) menuPathArr.push(item.path);
    if (item.children?.length) getMenuListPath(item.children, menuPathArr);
  }
  return menuPathArr;
}
/**
 * @description 递归查询当前 path 所对应的菜单对象 (该函数暂未使用)
 * @param {Array} menuList 菜单列表
 * @param {String} path 当前访问地址
 * @returns {Object | null}
 */
export function findMenuByPath(menuList: Menu.MenuOptions[], path: string): Menu.MenuOptions | null {
  for (const item of menuList) {
    if (item.path === path) return item;
    if (item.children) {
      const res = findMenuByPath(item.children, path);
      if (res) return res;
    }
  }
  return null;
}

/**
 * @description 使用递归过滤需要缓存的菜单 name (该函数暂未使用)
 * @param {Array} menuList 所有菜单列表
 * @param {Array} keepAliveNameArr 缓存的菜单 name ['**','**']
 * @returns {Array}
 * */
export function getKeepAliveRouterName(menuList: Menu.MenuOptions[], keepAliveNameArr: string[] = []) {
  menuList.forEach(item => {
    item.meta.isKeepAlive && item.name && keepAliveNameArr.push(item.name);
    item.children?.length && getKeepAliveRouterName(item.children, keepAliveNameArr);
  });
  return keepAliveNameArr;
}

/**
 * @description 格式化表格单元格默认值 (el-table-column)
 * @param {Number} row 行
 * @param {Number} col 列
 * @param {*} callValue 当前单元格值
 * @returns {String}
 * */
export function formatTableColumn(row: number, col: number, callValue: any) {
  // 如果当前值为数组，使用 / 拼接（根据需求自定义）
  if (isArray(callValue)) return callValue.length ? callValue.join(" / ") : "--";
  return callValue ?? "--";
}

/**
 * @description 处理 ProTable 值为数组 || 无数据
 * @param {*} callValue 需要处理的值
 * @returns {String}
 * */
export function formatValue(callValue: any) {
  // 如果当前值为数组，使用 / 拼接（根据需求自定义）
  if (isArray(callValue)) return callValue.length ? callValue.join(" / ") : "--";
  return callValue ?? "--";
}

/**
 * @description 处理 prop 为多级嵌套的情况，返回的数据 (列如: prop: user.name)
 * @param {Object} row 当前行数据
 * @param {String} prop 当前 prop
 * @returns {*}
 * */
export function handleRowAccordingToProp(row: { [key: string]: any }, prop: string) {
  if (!prop) return "-";
  if (!prop.includes(".")) return row[prop] ?? "-";
  prop.split(".").forEach(item => (row = row[item] ?? "-"));
  return row;
}

/**
 * @description 处理 prop，当 prop 为多级嵌套时 ==> 返回最后一级 prop
 * @param {String} prop 当前 prop
 * @returns {String}
 * */
export function handleProp(prop: string) {
  const propArr = prop.split(".");
  if (propArr.length == 1) return prop;
  return propArr[propArr.length - 1];
}

/**
 * @description 根据枚举列表查询当需要的数据（如果指定了 label 和 value 的 key值，会自动识别格式化）
 * @param {String} callValue 当前单元格值
 * @param {Array} enumData 字典列表
 * @param {Array} fieldNames label && value && children 的 key 值
 * @param {String} type 过滤类型（目前只有 tag）
 * @returns {String}
 * */
export function filterEnum(callValue: any, enumData?: any, fieldNames?: FieldNamesProps, type?: "tag") {
  const value = fieldNames?.value ?? "value";
  const label = fieldNames?.label ?? "label";
  const children = fieldNames?.children ?? "children";
  let filterData: { [key: string]: any } = {};
  // 判断 enumData 是否为数组
  if (Array.isArray(enumData)) filterData = findItemNested(enumData, callValue, value, children);
  // 判断是否输出的结果为 tag 类型
  if (type == "tag") {
    return filterData?.tagType ? filterData.tagType : "";
  } else {
    return filterData ? filterData[label] : "-";
  }
}

/**
 * @description 递归查找 callValue 对应的 enum 值
 * */
export function findItemNested(enumData: any, callValue: any, value: string, children: string) {
  return enumData.reduce((accumulator: any, current: any) => {
    if (accumulator) return accumulator;
    if (current[value] === callValue) return current;
    if (current[children]) return findItemNested(current[children], callValue, value, children);
  }, null);
}
