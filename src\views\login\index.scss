:deep(.el-form-item__label) {
  display: none;
}

:deep(.el-checkbox__inner) {
  border-radius: 50%;
}
:deep(.el-checkbox__input.is-checked + .el-checkbox__label) {
  //系统默认颜色
  color: initial;
}
//按钮样式
.welcome-title {
  margin-bottom: 40px;
  font-family:
    Source <PERSON>,
    Source Han Sans CN;
  font-weight: bold;
  font-size: 32px;
  color: #1a1a1a;
  line-height: 1;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
.el-button {
  width: 100%;
  box-shadow:
    0 5px 5px #5689fe40,
    0 10px 10px #5689fe1a;
  filter: drop-shadow(0px 10px 8px rgba(86, 137, 254, 0.26));
  height: 50px;
}

.submit-button {
  letter-spacing: 2px;
  border-radius: 12px;
  border: none;
  background: linear-gradient(90deg, #29b1ff 0%, #5689fe 100%);
  font-size: 16px;
  font-weight: 900;
}
.login-container {
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #356afd;
  overflow: auto;
}
.login-box {
  width: 100%;
  height: 100%;
  background: #356afd;
  position: relative;
  overflow: hidden;
}
.img-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 61.8%;
  height: 100vh;
}
@media screen and (max-width: 1100px) {
  .form-container {
    width: 100vw !important;
  }
}
.form-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: #e5edff;
  margin-left: auto;
  min-width: 550px;
  height: 100%;
  width: 38.2%;
  position: absolute;
  right: 0;
  top: 0;
}

.icon {
  width: 20px;
  height: 20px;
  cursor: pointer;
}

@media screen and (max-width: 1024px) {
  .login-box {
    grid-template-columns: 1fr 1fr;
  }
  .img-container {
    background-size: 100%;
  }
}
@media screen and (max-width: 768px) {
  .img-container {
    display: none;
  }
  .login-box {
    grid-template-columns: 1fr;
  }
}
