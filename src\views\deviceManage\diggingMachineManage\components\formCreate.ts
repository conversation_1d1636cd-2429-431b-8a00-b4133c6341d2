/**
 * @file 设备管理-挖机管理-表单创建文件
 * <AUTHOR>
 * @date 2024/12/2
 */
import { formMap } from "../types";

export const diggingMachineFormCreate = {
  type: "form-create",
  props: {
    option: {
      submitBtn: false,
      onSubmit(formData, api) {
        // 通知 table 搜索数据变化，刷新数据
        api.top.bus.$emit("searchFormChanged");
      }
    },
    rule: [
      {
        type: "input",
        field: formMap.get("名称"),
        title: "名称",
        validate: [
          { required: true, message: "请输入辅助车辆名称" },
          {
            pattern: /^.{1,20}$/,
            message: "字符限长20位"
          }
        ]
      },
      {
        type: "input",
        field: formMap.get("编码"),
        title: "编码",
        validate: [{ required: true, message: "请输入挖机编码" }]
      },

      {
        type: "input",
        field: formMap.get("最大装载率"),
        title: "最大装载率",
        children: [{ type: "div", slot: "suffix", children: ["t/h"] }],
        validate: [
          { required: true, message: "请输入最大装载率" },
          {
            pattern: /^\d+(\.\d+)?$/, // 允许输入数字和小数
            message: "请输入大于0的数字"
          }
        ]
      }
    ]
  }
};
