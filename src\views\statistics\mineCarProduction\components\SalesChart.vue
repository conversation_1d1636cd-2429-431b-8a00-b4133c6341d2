<template>
  <div class="sales-chart">
    <div class="chart-info">
      <div class="title-date">
        <div class="title">{{ props.config.title }}</div>
        <div class="date">{{ searchParams?.date }}</div>
      </div>
      <template v-if="searchParams?.reportType === 'year'">
        <el-radio-group @change="onDateChange" v-model="chartType" size="large">
          <el-radio-button v-for="item in dateOptions" :label="item.label" :value="item.value" :key="item.value" size="small" />
        </el-radio-group>
      </template>
      <div>
        <slot name="right"></slot>
      </div>
    </div>
    <div class="chart-wrap">
      <ECharts class="chart" :option="chartOption" resize />
    </div>
  </div>
</template>
<script setup lang="ts">
import dayjs from "dayjs";
import { ref, reactive, computed, watch, inject, type ComputedRef } from "vue";
import ECharts from "@/components/ECharts/index.vue";
import { ECOption } from "@/components/ECharts/config";
import { SearchParams } from "../index.vue";
import { tooltipDarkStyle } from "@/styles/theme/echarts";

const searchParams = inject<ComputedRef<SearchParams>>("searchParams");
interface Config {
  title: string;
  unit: string;
  api: (params: SearchParams) => Promise<{
    success: boolean;
    data: {};
  }>;
}

const props = defineProps<{
  config: Config;
}>();

const chartType = ref<string>("month");
const dateOptions = [
  { label: "周", value: "week" },
  { label: "月", value: "month" },
  { label: "季", value: "quarter" }
];

const hourArray: string[] = [];
for (let i = 0; i < 24; i++) {
  const hour = String(i).padStart(2, "0");
  hourArray.push(`${hour}:00`);
}

const onDateChange = (value: any) => {
  searchParams && getData();
};
const chartData = reactive<{
  xAxis: string[];
  series: SeriesItem[];
}>({
  xAxis: [],
  series: []
});

interface SeriesItem {
  name: string;
  data: number[];
}

const getData = async () => {
  const params = { ...searchParams?.value };
  if (params?.reportType === "year") {
    params.chartType = chartType.value;
  }
  try {
    const res = await props.config?.api(params);
    if (!res.success) return;
    chartData.xAxis = res.data.xaxis;
    chartData.series = res.data.seriesList;
  } catch (error) {
    console.log(error);
  }
};

watch(
  () => searchParams?.value,
  newParams => newParams && getData(),
  { deep: true, immediate: true }
);

const labelFormatter = (value: number, index: number) => {
  const reportType = searchParams?.value.reportType;

  if (reportType === "year") {
    if (chartType.value === "month") {
      return dayjs(value).format("MM月");
    } else {
      return value;
    }
  } else if (reportType === "month") {
    return dayjs(value).format("DD");
  } else if (reportType === "day") {
    return hourArray[index];
  }
};
// 基础配置
const baseOption: Partial<ECOption> = {
  grid: { top: 40, right: 40, bottom: 20, left: 55 },
  legend: {
    show: true
  },
  tooltip: {
    trigger: "axis",
    className: "echarts-tooltip-dark",
    backgroundColor: "rgba(26, 26, 26 , 0.9)",
    formatter: (params: any) => tooltipDarkStyle(params, [props.config.unit])
  },
  xAxis: {
    type: "category",
    boundaryGap: false,
    axisLine: {
      show: true,
      lineStyle: {
        color: ""
      }
    },
    axisTick: { show: false },
    axisLabel: {
      show: true,
      formatter: (value: any, index: number) => labelFormatter(value, index)
    }
  },
  yAxis: {
    type: "value",
    splitLine: {
      lineStyle: {
        type: "dashed"
      }
    },
    axisLabel: {}
  }
} as const;

const chartOption = computed(() => ({
  ...baseOption,
  xAxis: {
    ...baseOption.xAxis,
    data: chartData.xAxis || []
  },
  series: chartData.series.map(item => ({
    type: "line",
    showSymbol: false,
    data: item.data,
    name: item.name
  })) as any
})) as unknown as ComputedRef<ECOption>;
</script>
<style lang="scss" scoped>
.sales-chart {
  flex: 0 0 calc(50% - 5px);
  height: 330px;
  padding: 16px 0 16px 16px;
  border: 1px solid #dde2e8;
}
.chart-info {
  display: flex;
  justify-content: space-between;
  .title {
    font-size: 14px;
    font-weight: bold;
    color: #1a1a1a;
  }
  .date {
    font-size: 12px;
    color: #656666;
  }
  .el-radio-group {
    align-items: flex-start;
    margin-left: -40px;
  }
}
.chart-wrap {
  height: 257px;
}
</style>
