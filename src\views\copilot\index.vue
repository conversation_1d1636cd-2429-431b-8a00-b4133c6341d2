<template>
  <el-container class="copilot-container__dark">
    <el-header height="56px" style="background: linear-gradient(180deg, #191a47 0%, #101150 100%)">
      <HeaderBar />
    </el-header>
    <el-container>
      <el-aside width="775px">
        <Aside />
      </el-aside>
      <el-main style="padding: 0">
        <el-scrollbar style="height: calc(100vh - 56px)">
          <MapContainer />
          <div class="flex-align-center" style="gap: 8px; height: 396px; margin-top: 8px">
            <Billboard />
            <ProductionAndGlobalAction />
          </div>
        </el-scrollbar>
      </el-main>
    </el-container>
  </el-container>
</template>

<script lang="ts" setup>
import { useTheme } from "@/hooks/useTheme";
import { onBeforeRouteLeave } from "vue-router";
import { useGlobalStore } from "@/stores/modules/global";
import HeaderBar from "./components/HeaderBar/index.vue";
import Aside from "./components/Aside/index.vue";
import MapContainer from "./components/MapContainer/index.vue";
import Billboard from "./components/Billboard/index.vue";
import ProductionAndGlobalAction from "./components/ProductionAndGlobalAction/index.vue";
import "./socket";
const globalStore = useGlobalStore();
globalStore.setGlobalState("isDark", true);
const { switchDark } = useTheme();
switchDark();
onBeforeRouteLeave(() => {
  globalStore.setGlobalState("isDark", false);
  switchDark();
});
// 分析数据
</script>

<style lang="scss">
.copilot-container__dark {
  --el-bg-color: #040536;
  --el-border-color: #27295b;
  --el-color-secondary: #409eff;
  --el-color-primary: #00d1d9;
  --el-color-primary-opacity-1: rgba(0, 209, 217, 0.1);
  --el-color-success: #00c290;
  --el-color-error: #f25555;
  --el-color-warning: rgba(255, 187, 0, 1);
  --el-color-error-opacity-1: rgba(242, 85, 85, 0.1);
  --el-text-color-secondary: rgba(116, 117, 149, 1);
  --card-bg-color: #191a47;
  .el-message-box {
    --el-messagebox-width: 488px;
    --el-bg-color: #3b3ca7;
    --el-messagebox-border-radius: 16px;
  }
  //--el-messagebox-width: 488px;
  //max-width: var(--el-messagebox-width);
  //padding: var(--el-messagebox-padding-primary);
  //background-color: var(--el-bg-color);
  //border-radius: var(--el-messagebox-border-radius);
  //font-size: var(--el-messagebox-font-size);
  //box-shadow: var(--el-messagebox-box-shadow);
}
/*html.dark {
  color-scheme: dark;
  --el-color-primary: #409eff;
  --el-color-primary-light-3: rgb(50.8, 116.6, 184.5);
  --el-color-primary-light-5: rgb(42, 89, 137.5);
  --el-color-primary-light-7: rgb(33.2, 61.4, 90.5);
  --el-color-primary-light-8: rgb(28.8, 47.6, 67);
  --el-color-primary-light-9: rgb(24.4, 33.8, 43.5);
  --el-color-primary-dark-2: rgb(102.2, 177.4, 255);
  --el-color-success: #67c23a;
  --el-color-success-light-3: rgb(78.1, 141.8, 46.6);
  --el-color-success-light-5: rgb(61.5, 107, 39);
  --el-color-success-light-7: rgb(44.9, 72.2, 31.4);
  --el-color-success-light-8: rgb(36.6, 54.8, 27.6);
  --el-color-success-light-9: rgb(28.3, 37.4, 23.8);
  --el-color-success-dark-2: rgb(133.4, 206.2, 97.4);
  --el-color-warning: #e6a23c;
  --el-color-warning-light-3: rgb(167, 119.4, 48);
  --el-color-warning-light-5: #7d5b28;
  --el-color-warning-light-7: rgb(83, 62.6, 32);
  --el-color-warning-light-8: rgb(62, 48.4, 28);
  --el-color-warning-light-9: rgb(41, 34.2, 24);
  --el-color-warning-dark-2: rgb(235, 180.6, 99);
  --el-color-danger: #f56c6c;
  --el-color-danger-light-3: rgb(177.5, 81.6, 81.6);
  --el-color-danger-light-5: rgb(132.5, 64, 64);
  --el-color-danger-light-7: rgb(87.5, 46.4, 46.4);
  --el-color-danger-light-8: rgb(65, 37.6, 37.6);
  --el-color-danger-light-9: rgb(42.5, 28.8, 28.8);
  --el-color-danger-dark-2: rgb(247, 137.4, 137.4);
  --el-color-error: #f56c6c;
  --el-color-error-light-3: rgb(177.5, 81.6, 81.6);
  --el-color-error-light-5: rgb(132.5, 64, 64);
  --el-color-error-light-7: rgb(87.5, 46.4, 46.4);
  --el-color-error-light-8: rgb(65, 37.6, 37.6);
  --el-color-error-light-9: rgb(42.5, 28.8, 28.8);
  --el-color-error-dark-2: rgb(247, 137.4, 137.4);
  --el-color-info: #909399;
  --el-color-info-light-3: rgb(106.8, 108.9, 113.1);
  --el-color-info-light-5: rgb(82, 83.5, 86.5);
  --el-color-info-light-7: rgb(57.2, 58.1, 59.9);
  --el-color-info-light-8: rgb(44.8, 45.4, 46.6);
  --el-color-info-light-9: rgb(32.4, 32.7, 33.3);
  --el-color-info-dark-2: rgb(166.2, 168.6, 173.4);
  --el-box-shadow: 0px 12px 32px 4px rgba(0, 0, 0, 0.36), 0px 8px 20px rgba(0, 0, 0, 0.72);
  --el-box-shadow-light: 0px 0px 12px rgba(0, 0, 0, 0.72);
  --el-box-shadow-lighter: 0px 0px 6px rgba(0, 0, 0, 0.72);
  --el-box-shadow-dark: 0px 16px 48px 16px rgba(0, 0, 0, 0.72), 0px 12px 32px #000000, 0px 8px 16px -8px #000000;
  --el-bg-color-page: #0a0a0a;
  --el-bg-color: #141414;
  --el-bg-color-overlay: #1d1e1f;
  --el-text-color-primary: #e5eaf3;
  --el-text-color-regular: #cfd3dc;
  --el-text-color-secondary: #a3a6ad;
  --el-text-color-placeholder: #8d9095;
  --el-text-color-disabled: #6c6e72;
  --el-border-color-darker: #636466;
  --el-border-color-dark: #58585b;
  --el-border-color: #4c4d4f;
  --el-border-color-light: #414243;
  --el-border-color-lighter: #363637;
  --el-border-color-extra-light: #2b2b2c;
  --el-fill-color-darker: #424243;
  --el-fill-color-dark: #39393a;
  --el-fill-color: #303030;
  --el-fill-color-light: #262727;
  --el-fill-color-lighter: #1d1d1d;
  --el-fill-color-extra-light: #191919;
  --el-fill-color-blank: transparent;
  --el-mask-color: rgba(0, 0, 0, 0.8);
  --el-mask-color-extra-light: rgba(0, 0, 0, 0.3);
}

html.dark .el-button {
  --el-button-disabled-text-color: rgba(255, 255, 255, 0.5);
}

html.dark .el-card {
  --el-card-bg-color: var(--el-bg-color-overlay);
}

html.dark .el-empty {
  --el-empty-fill-color-0: var(--el-color-black);
  --el-empty-fill-color-1: #4b4b52;
  --el-empty-fill-color-2: #36383d;
  --el-empty-fill-color-3: #1e1e20;
  --el-empty-fill-color-4: #262629;
  --el-empty-fill-color-5: #202124;
  --el-empty-fill-color-6: #212224;
  --el-empty-fill-color-7: #1b1c1f;
  --el-empty-fill-color-8: #1c1d1f;
  --el-empty-fill-color-9: #18181a;
}*/
</style>
<style lang="scss">
.copilot-container__dark {
  height: 100vh;
  background: var(--el-bg-color);
  color: #c9cad9;
  .card-container {
    background: var(--card-bg-color);
    border-radius: 8px;
    border: 1px solid #27295b;
  }
}
</style>
<style lang="scss" scoped>
.el-main {
  padding: 16px 16px 0 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
  overflow: hidden;
}
</style>
