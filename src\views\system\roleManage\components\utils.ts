/**
 * @file 系统管理-角色管理-类型声明文件
 * <AUTHOR>
 * @date 2024/11/8
 */
import GrantAuth from "@/views/system/roleManage/components/GrantAuth.vue";
import { formMap } from "@/views/system/roleManage/types";
import formCreate, { Api } from "@form-create/element-ui";

formCreate.component("GrantAuth", GrantAuth);

export const roleFormCreate = {
  type: "form-create",
  props: {
    rule: [
      {
        type: "input",
        field: formMap.get("角色名称"),
        title: "角色名称",
        validate: [
          { required: true, message: "请输入角色名称" },
          {
            pattern: /^.{1,20}$/,
            message: "字符限长20位"
          }
        ]
      },
      {
        type: "input",
        field: formMap.get("角色编码"),
        title: "角色编码",
        validate: [
          { required: true, message: "请输入角色编码" },
          {
            pattern: /^.{1,20}$/,
            message: "字符限长20位"
          }
        ]
      }
    ],
    option: {
      submitBtn: false,
      onSubmit(formData, api) {
        // 通知 table 搜索数据变化，刷新数据
        api.top.bus.$emit("searchFormChanged");
      }
    }
  }
};
export const permissionsFormCreate = {
  type: "form-create",
  props: {
    rule: [
      {
        type: "GrantAuth",
        props: {
          roleId: ""
        },
        // update(val: any, rule, api: Api, init) {
        //   console.log("22", api.formData().roleId);
        //   if (api.formData().roleId) rule.props.roleId = api.formData().roleId;
        // },
        field: "resourceList"
      }
    ],
    option: {
      submitBtn: false,
      onSubmit(formData, api) {
        // 通知 table 搜索数据变化，刷新数据
        api.top.bus.$emit("searchFormChanged");
      }
    }
  }
};
