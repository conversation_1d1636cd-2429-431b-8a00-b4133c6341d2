/**
 * @file 历史数据-故障记录-详情文件
 * <AUTHOR>
 * @date 2025/7/16
 */
import formCreate from "@form-create/element-ui";
import Detail from "@/views/historyData/faultRecord/components/Detail.vue";
import LASelect from "@/components/LASelect.tsx";
import { formMap } from "@/views/historyData/faultRecord/types.ts";
import { getAllUserList } from "@/api/modules";
formCreate.component("Detail", Detail);

// 故障报修表单
export const faultRepairForm = {
  type: "form-create",
  name: "FaultRecordForm",
  props: {
    option: {
      submitBtn: false,
      onSubmit(formData, api) {
        console.log("formData", formData);
        // 通知 table 搜索数据变化，刷新数据
        api.top.bus.$emit("searchFormChanged");
      }
    },
    rule: [
      {
        type: "Detail",
        props: {
          data: {}
        }
      },
      {
        component: LASelect,
        field: formMap.get("报修人"),
        props: {
          replaceFields: { key: "id", label: "employeeName", value: "id" },
          fetch: getAllUserList
        },
        title: "报修人",
        validate: [{ required: true, message: "请选择报修人" }]
      },
      {
        component: LASelect,
        field: formMap.get("维修责任人"),
        props: {
          replaceFields: { key: "id", label: "employeeName", value: "id" },
          fetch: getAllUserList,
          onChange: (value, data, formCreateInject) => {
            formCreateInject.api.setValue(formMap.get("维修责任人名称"), data.employeeName);
          }
        },
        title: "维修责任人",
        validate: [{ required: true, message: "请选择维修责任人" }]
      }
    ]
  }
};
