/* Reset style sheet */



// /* HTML5 display-role reset for older browsers */
// article,
// aside,
// details,
// figcaption,
// figure,
// footer,
// header,
// hgroup,
// menu,
// nav,
// section {
// 	display: block;
// }
// body {
// 	padding: 0;
// 	margin: 0;
// }
// ol,
// ul {
// 	list-style: none;
// }
// blockquote,
// q {
// 	quotes: none;
// }
// blockquote::before,
// blockquote::after,
// q::before,
// q::after {
// 	content: "";
// 	content: none;
// }
// table {
// 	border-spacing: 0;
// 	border-collapse: collapse;
// }

.meta-form .el-form-item .el-form-item__content {
  align-items: flex-start;
}
body {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizelegibility;
  font-family: "sourceHanSans", "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑",
    Arial, sans-serif;
}

::-webkit-scrollbar-track-piece {
  background: #e9e9ef;
}

::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-thumb {
  background: #bac3c5;
  border-radius: 20px;
}
button {
  font-family: "sourceHanSans", "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑",
    Arial, sans-serif;
}
html {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

#app {
  width: 100%;
  height: 100%;
}

label {
  font-weight: 700;
  span {
    line-height: 40px;
  }
}

*,
*::before,
*::after {
  box-sizing: inherit;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

.clearfix {
  &::after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

.bold {
  font-weight: 600;
}

/* 解决 h1 标签在 webkit 内核浏览器中文字大小失效问题 */
:-webkit-any(article, aside, nav, section) h1 {
  font-size: 2em;
}
