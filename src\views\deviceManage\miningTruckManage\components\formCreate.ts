/**
 * @file 设备管理-矿卡管理-表单创建文件
 * <AUTHOR>
 * @date 2024/11/25
 */
import { formMap, nodeColumnMap, nodeStatusEnum } from "../types";
import LASelect from "@/components/LASelect";
import { getNodalList, getMobileTrafficList } from "@/api/modules/device";
export const miningTruckFormCreate = {
  type: "form-create",
  props: {
    option: {
      row: { gutter: 24 },
      global: { "*": { col: { span: 12 } } },
      submitBtn: false,
      onSubmit(formData, api) {
        // 通知 table 搜索数据变化，刷新数据
        api.top.bus.$emit("searchFormChanged");
      }
    },
    rule: [
      {
        type: "input",
        field: formMap.get("矿卡名称"),
        title: "矿卡名称",
        validate: [
          { required: true, message: "请输入矿卡名称" },
          {
            pattern: /^.{1,5}$/,
            message: "字符限长5位"
          }
        ]
      },
      {
        type: "input",
        field: formMap.get("矿卡编码"),
        title: "矿卡编码",
        validate: [{ required: true, message: "请输入矿卡编码" }]
      },
      {
        type: "input",
        field: formMap.get("型号"),
        title: "型号"
      },

      {
        type: "input",
        field: formMap.get("承载能力"),
        title: "承载能力",
        validate: [
          {
            pattern: /^(?:[1-9]\d*|0*[1-9]\d*(?:\.\d+)?|0*\.\d*[1-9]\d*)$/,
            message: "请输入大于0的数字"
          }
        ],

        children: [{ type: "div", slot: "suffix", children: ["t"] }]
      }
    ]
  }
};
// 节点表格
export const nodeFormCreate = {
  type: "form-create",
  props: {
    option: {
      submitBtn: false
    },
    rule: [
      {
        type: "ProTable",
        field: "v:nodeTable",
        name: "nodeTable",
        style: {
          flex: "none",
          width: "100%",
          maxHeight: "calc(70vh - 178px)",
          height: "100%"

          // overflow: "auto"
        },

        props: {
          requestAuto: false,
          params: {
            deviceId: ""
          },
          pagination: false,
          columns: [
            {
              prop: nodeColumnMap.get("节点编码"),
              label: "节点编码"
            },
            {
              prop: nodeColumnMap.get("节点类型"),
              label: "节点类型"
            },
            {
              prop: nodeColumnMap.get("版本号"),
              label: "版本号"
            },
            {
              prop: nodeColumnMap.get("状态"),
              label: "状态",
              tag: true,
              enum: [...nodeStatusEnum]
            },
            { prop: "operation", label: "操作", fixed: "right" }
          ],
          fetch: getNodalList,
          operations: [{ content: "重启", action: "restart" }]
        },
        children: [
          // 授权
          {
            type: "ConfirmDialog",
            on: {
              // 监听弹窗组件抛出的的afterSubmit事件，用于刷新页面
              afterSubmit: api => {
                // 刷新，调用组件内部请求方法
                api.api.exec("v:nodeTable", "getTableList");
              }
            },
            props: {
              action: "restart",
              subtitle: row => {
                return row.code;
              },
              title: "是否重启节点",
              message: "重启后,该节点将重启·",
              // 模拟请求param：参数
              submitRequest: param => {
                console.log(param);
                // 模拟提交的请求为2秒
                return new Promise((resolve, reject) => {
                  setTimeout(() => {
                    // 在这里可以添加请求成功后的逻辑
                    console.log("请求成功");
                    resolve("请求成功返回的数据");
                  }, 2000); // 2000毫秒等于2秒
                });
              }
            }
          }
        ]
      }
    ]
  }
};
