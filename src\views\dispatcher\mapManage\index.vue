<template>
  <form-create v-model:api="fApi" :option="option" :rule="rule"></form-create>
</template>
<script lang="tsx" setup>
/**
 * @file 系统管理-用户管理
 * <AUTHOR>
 * @date 2024/11/14
 */
import { ref } from "vue";
import { columnMap } from "./types";
import formCreate from "@form-create/element-ui";
import { getDispatchMapList, deleteDispatchMap, saveDispatchMap, setDispatchMapInUse } from "@/api/modules/dispatch";
import JsFileDownloader from "js-file-downloader";
import LAUploadFile from "@/components/LAUploadFile.vue";

formCreate.component("LAUploadFile", LAUploadFile);

const fApi = ref();
const option = {
  form: { inline: true },
  resetBtn: false,
  submitBtn: false
};

const rule = ref([
  {
    type: "SearchFormOperation",
    field: "v:search",
    wrap: { style: "marginBottom: 0" },
    children: [
      {
        type: "input",
        field: "search",
        props: {
          size: "default",
          placeholder: "地图名称"
        }
      },
      {
        type: "LAUploadFile",
        on: {
          async success(file) {
            console.log("222", file);
            const res = await saveDispatchMap({ filePath: file.url, mapVersion: file.name });
            if (res.statusCode === 101) {
              fApi.value.exec("v:search", "onSearch");
            }
          }
        },
        props: {
          showFileList: false
        },
        slot: "suffix",
        style: { display: "flex" }
      }
    ]
  },
  {
    type: "ProTable",
    props: {
      columns: [
        {
          prop: columnMap.get("地图名称"),
          width: "400px",
          label: "地图名称",
          render(scope: any) {
            return (
              <div>
                {scope.row.useFlag === 1 ? (
                  <el-tag type="primary" effect="plain" size="small" style={{ marginRight: "2px", padding: "0 4px" }}>
                    在用
                  </el-tag>
                ) : null}
                {scope.row[columnMap.get("地图名称")] || "-"}
              </div>
            );
          }
        },
        {
          prop: columnMap.get("类型"),
          label: "类型",
          render(scope: any) {
            return <div>{scope.row.mapType === 1 ? "自动" : "手动"}</div>;
          }
        },
        {
          prop: columnMap.get("上传时间"),
          label: "上传时间"
        },
        { prop: "operation", label: "操作", fixed: "right" }
      ],
      fetch: getDispatchMapList,
      operations: [
        {
          content: "设为在用",
          action: "edit",
          props: {
            disabled: row => {
              return row.useFlag === 1;
            }
          },
          auth: "update"
          // 0:表示没有在用;1:表示在用
        },
        {
          content: "下载",
          action: "download",
          auth: "download",
          onClick: (row: any) => {
            // console.log(row);
            new JsFileDownloader({
              url: row.filePath,
              nameCallback: function (name) {
                return row.mapVersion;
              }
            });
          }
        },
        {
          content: "删除",
          action: "delete",
          props: {
            type: "danger",
            disabled: row => {
              return row.useFlag === 1;
            }
          }
        }
      ]
    },
    children: [
      {
        type: "ConfirmDialog",
        on: {
          // 监听弹窗组件抛出的的afterSubmit事件，用于刷新页面
          afterSubmit: () => {
            // 刷新，调用组件内部请求方法
            fApi.value.exec("v:search", "onSearch");
          }
        },
        props: {
          title: "是否设为在用",
          message: "设为在用后,当前地图将设为在用",
          action: "edit",
          // 模拟请求param：参数
          submitRequest: setDispatchMapInUse
        }
      },
      {
        type: "ConfirmDialog",
        on: {
          // 监听弹窗组件抛出的的afterSubmit事件，用于刷新页面
          afterSubmit: () => {
            // 刷新，调用组件内部请求方法
            fApi.value.exec("v:search", "onSearch");
          }
          // openDialog: (row, api) => {
          //   api.rule.props.subtitle = row.userName;
          //   // console.log(api.rule.props.subtitle);
          // }
        },

        props: {
          title: "是否删除地图·",
          message: "删除地图后不可恢复",
          action: "delete",
          subtitle: row => {
            return row.mapVersion;
          },
          // 模拟请求param：参数
          submitRequest: deleteDispatchMap
        }
      }
    ]
  }
]);
</script>
<style lang="scss" scoped>
:deep(.el-row) {
  height: var(--page-height);
}

.el-form-item {
  margin-right: 4px !important;
}
</style>
