<template>
  <form-create v-model:api="fApi" :option="option" :rule="rule"></form-create>
</template>
<script lang="tsx" setup>
/**
 * @file 设备管理-摄像头管理
 * <AUTHOR>
 * @date 2024/11/18
 */
import { ref } from "vue";
import { deleteCamera, getCameraList, saveCamera } from "@/api/modules/device";
import { columnMap } from "./types";
import { cameraMangeFormCreate } from "./components/formCreate";
import formCreate from "@form-create/element-ui";

const fApi = ref();
const option = {
  form: { inline: true },
  resetBtn: false,
  submitBtn: false
};

const rule = ref([
  {
    type: "SearchFormOperation",
    field: "v:search",
    wrap: { style: "marginBottom: 0" },
    children: [
      {
        type: "input",
        field: "search",
        props: {
          size: "default",
          placeholder: "摄像头名称/编码"
        }
      },
      // 新增
      {
        type: "AddBtn",
        slot: "suffix",
        props: {
          btn: { content: "新增摄像头" },
          dialog: {
            title: "新增摄像头", // 绑定到弹窗根节点的样式
            class: "dialog-custom-width"
          },

          size: "default",
          submitRequest: saveCamera
        },
        children: [cameraMangeFormCreate]
      }
    ]
  },
  {
    type: "ProTable",
    props: {
      columns: [
        {
          prop: columnMap.get("名称"),
          label: "名称"
        },
        {
          prop: columnMap.get("编码"),
          label: "编码"
        },
        {
          prop: columnMap.get("x坐标"),
          label: "x坐标"
        },
        {
          prop: columnMap.get("y坐标"),
          label: "y坐标"
        },
        {
          prop: columnMap.get("z坐标"),
          label: "z坐标"
        },
        {
          prop: columnMap.get("访问地址"),
          label: "访问地址"
        },
        {
          prop: columnMap.get("登录账号"),
          label: "登录账号"
        },
        {
          prop: columnMap.get("登录密码"),
          label: "登录密码"
        },
        { prop: "operation", label: "操作", fixed: "right" }
      ],
      fetch: getCameraList,
      operations: [
        { content: "修改", action: "edit" },
        { content: "删除", action: "delete", props: { style: { color: "rgba(242, 85, 85, 1)" } } }
      ]
    },

    children: [
      {
        type: "EditBtn",
        props: {
          action: "edit",
          dialog: {
            title: "修改摄像头",
            // 绑定到弹窗根节点的样式
            class: "dialog-custom-width"
          },
          submitRequest: saveCamera
        },
        children: [cameraMangeFormCreate]
      },
      {
        type: "ConfirmDialog",
        on: {
          // 监听弹窗组件抛出的的afterSubmit事件，用于刷新页面
          afterSubmit: () => {
            // 刷新，调用组件内部请求方法
            fApi.value.exec("v:search", "onSearch");
          }
        },
        props: {
          action: "delete",
          title: "是否删除摄像头",
          message: "删除后不可恢复",
          subtitle: row => {
            return row[columnMap.get("名称")];
          },
          // 模拟请求param：参数
          submitRequest: deleteCamera
        }
      }
    ]
  }
]);
</script>
<style lang="scss" scoped>
:deep(.el-row) {
  height: var(--page-height);
}

.el-form-item {
  margin-right: 4px !important;
}
</style>

<style lang="scss"></style>
