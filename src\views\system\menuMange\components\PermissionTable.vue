<template>
  <form-create v-model:api="fApi" :option="option" :rule="rule"></form-create>
</template>

<script lang="tsx" setup>
/**
 * @file 系统管理-菜单管理-右侧权限表格
 * <AUTHOR>
 * @date 2024/11/7
 */

import { ref, watch } from "vue";
import { featurePermissionsFormCreate } from "./formCreate";
import { Plus } from "@element-plus/icons-vue";
import { getMenuSelectList, deleteMenu, addUserMenu } from "@/api/modules";

const fApi = ref();

// 接口参数定义
const initParam = ref({ pid: "", type: "ResourceButton" });
const props = defineProps({
  // 当前点击的菜单的数据
  menuData: { type: Object, default: () => {} }
});
watch(
  () => props.menuData,
  v => {
    if (v) {
      initParam.value.pid = v.id;
    }
  },
  {
    immediate: true
    // deep: true
  }
);
/**
 * @file 系统管理-菜单管理-菜单权限表格
 * <AUTHOR>
 * @date 2024/11/7
 */
const option = {
  form: { inline: true },
  resetBtn: false,
  submitBtn: false
};
const rule = ref([
  {
    type: "AddBtn",
    props: {
      btn: {
        icon: Plus,
        style: {
          marginBottom: "10px", // 按钮样式
          width: "80px",
          height: "32px"
        },
        content: "新增",
        auth: "add_menu"
      },
      dialog: { title: "新增功能权限" },
      submitRequest: (data, api) => {
        let params = { ...data, url: props.menuData.url, pid: props.menuData.id, type: "ResourceButton" };
        // 提交的请求
        return addUserMenu(params);
      }
    },
    children: [featurePermissionsFormCreate]
  },

  {
    type: "ProTable",
    field: "v:ProTable",
    wrap: { style: { width: "100%" } },
    name: "v:ProTable",
    props: {
      pagination: false,
      columns: [
        { prop: "name", label: "名称" },
        { prop: "menuCode", label: "编码" },
        { prop: "operation", label: "操作", fixed: "right" }
      ],
      params: initParam.value,
      fetch: params => {
        // 模拟请求当initParam变化时,会自动重新请求
        return getMenuSelectList(params);
      },
      operations: [
        { content: "修改", action: "editBtn", auth: "update_menu" },
        {
          content: "删除",
          action: "delete",
          auth: "delete_menu",
          props: {
            style: {
              color: "rgba(242, 85, 85, 1)"
            }
          }
        }
      ]
    },
    children: [
      {
        type: "EditBtn",
        props: {
          action: "editBtn",

          dialog: { title: "编辑菜单" },
          submitRequest: data => {
            // 提交的请求
            return addUserMenu(data);
          }
        },
        children: [featurePermissionsFormCreate]
      },
      {
        type: "ConfirmDialog",
        on: {
          // 监听弹窗组件抛出的的afterSubmit事件，用于刷新页面
          afterSubmit: () => {
            // 刷新组件，调用组件内部请求方法
            setTimeout(() => {
              fApi.value.exec("v:ProTable", "getTableList");
            }, 0);
          }
        },
        props: {
          action: "delete",
          subtitle: row => {
            return row.name;
          },
          title: "是否删除功能权限",
          message: "删除后不可恢复",
          // 模拟请求param：参数
          submitRequest: param => {
            console.log(param);
            // 模拟提交的请求为2秒
            return deleteMenu({ id: param.id });
          }
        }
      }
    ]
  }
]);
</script>

<style lang="scss" scoped>
:deep(.table-main) {
  width: 100%;
  height: 100% !important;
}
:deep(.table-box) {
  height: 300px;
  border: 1px solid #ebedf1ff;
}
:deep(.el-table__inner-wrapper) {
  //伪元素
  &::before {
    height: 0 !important;
  }
}
</style>
