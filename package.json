{"name": "auto-haul-ui", "private": true, "version": "1.2.0", "type": "module", "homepage": "https://github.com/HalseySpicy/Geeker-Admin", "repository": {"type": "git", "url": "**************:HalseySpicy/Geeker-Admin.git"}, "scripts": {"dev": "vite", "serve": "vite", "build:dev": "vite build", "build:test": "vue-tsc && vite build --mode test", "build:pro": "vue-tsc && vite build --mode production", "type:check": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "preview": "pnpm build:dev && vite preview", "lint:eslint": "eslint --fix --ext .js,.ts,.vue ./src", "lint:prettier": "prettier --write \"src/**/*.{js,ts,json,tsx,css,less,scss,vue,html,md}\"", "lint:stylelint": "stylelint --cache --fix \"**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "release": "standard-version"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@form-create/element-ui": "^3.2.13", "@types/node": ">=18.0.0 <19.0.0 || >=20.0.0", "@vee-validate/yup": "^4.13.2", "@vueuse/core": "10.11.1", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "^1.7.2", "browser-image-compression": "^2.0.2", "cesium": "^1.125.0", "dayjs": "^1.11.11", "echarts": "^5.5.1", "element-plus": "^2.8.8", "js-file-downloader": "^1.1.25", "lodash-es": "^4.17.21", "lodash-unified": "^1.0.3", "md5": "^2.3.0", "mitt": "^3.0.1", "nprogress": "^0.2.0", "osmtogeojson": "3.0.0-beta.5", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.1", "print-js": "^1.6.0", "qs": "^6.12.1", "screenfull": "^6.0.2", "sortablejs": "^1.15.2", "vee-validate": "^4.13.2", "vue": "^3.4.31", "vue-cesium": "^3.2.9", "vue-router": "^4.4.0", "xlsx": "^0.18.5", "yup": "^1.4.0"}, "devDependencies": {"@commitlint/cli": "^18.4.3", "@commitlint/config-conventional": "^18.4.3", "@types/geojson": "^7946.0.15", "@types/lodash-es": "^4.17.12", "@types/md5": "^2.3.5", "@types/nprogress": "^0.2.3", "@types/qs": "^6.9.15", "@types/sortablejs": "^1.15.8", "@vitejs/plugin-vue": "^5.0.4", "@vitejs/plugin-vue-jsx": "^3.1.0", "autoprefixer": "^10.4.19", "code-inspector-plugin": "^0.16.1", "postcss": "^8.4.38", "postcss-html": "^1.7.0", "prettier": "^3.3.2", "rollup-plugin-visualizer": "^5.12.0", "sass": "^1.77.6", "standard-version": "^9.5.0", "stylelint": "^16.6.1", "stylelint-config-html": "^1.1.0", "stylelint-config-recess-order": "^5.0.1", "stylelint-config-recommended-scss": "^14.0.0", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard": "^36.0.0", "stylelint-config-standard-scss": "^13.1.0", "typescript": "^5.5.2", "unplugin-vue-setup-extend-plus": "^1.0.1", "vite": "^5.3.2", "vite-plugin-compression": "^0.5.1", "vite-plugin-html": "^3.2.2", "vite-plugin-pwa": "^0.20.0", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-devtools": "^7.3.5", "vue-tsc": "^2.0.22"}, "engines": {"node": ">=16.18.0"}, "browserslist": {"production": ["> 1%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}