<template>
  <VcConfigProvider :cesium-path="vcConfig.cesiumPath">
    <VcViewer
      :info-box="false"
      :show-credit="false"
      :base-layer-picker="false"
      :base-layer="false"
      container-id="root"
      :selection-indicator="false"
      ref="viewer"
      :scene-mode="3"
      :sky-box="false"
      @ready="onReady"
    >
      <OSMLoader />
      <!-- <ModelLoader /> -->
      <DashedConnectionPolyLineLoader v-if="!showTrackPlayback" />
      <ObstaclePolygonLoader v-if="!showTrackPlayback" />
      <TrackPlaybackLoader v-if="showTrackPlayback" v-bind="trackPlaybackLoaderProps" @ready="onTrackPlaybackReady" />
      <!-- <VcLayerImagery :sort-order="30">
        <VcImageryProviderTianditu map-style="vec_w" token="436ce7e50d27eede2f2929307e6b33c0"></VcImageryProviderTianditu>
      </VcLayerImagery> -->
    </VcViewer>
  </VcConfigProvider>
</template>
<script lang="ts" setup>
import "vue-cesium/dist/index.css";
import { onMounted, onUnmounted } from "vue";
import mittBus from "@/utils/mittBus";

import { VcConfigProvider, VcViewer, VcLayerImagery, VcImageryProviderTianditu } from "vue-cesium";

import { useMapEventChannel } from "./channel";
import OSMLoader from "./components/OSMLoader.vue";
// import ModelLoader from "./components/ModelLoader.vue";
import DashedConnectionPolyLineLoader from "./components/DashedConnectionPolyLineLoader.vue";
import ObstaclePolygonLoader from "./components/ObstaclePolygonLoader.vue";
import TrackPlaybackLoader from "./components/TrackPlaybackLoader.vue";

withDefaults(
  defineProps<{
    // 显示轨迹回放
    showTrackPlayback?: boolean;
    // 轨迹回放加载器配置
    trackPlaybackLoaderProps?: any;
  }>(),
  {
    showTrackPlayback: false,
    trackPlaybackLoaderProps: () => ({})
  }
);

const emit = defineEmits(["ready", "osm-load-error"]);

let viewer: any = null;

const vcConfig = {
  cesiumPath: "https://cdnjs.cloudflare.com/ajax/libs/cesium/1.125.0/Cesium.js"
  // cesiumPath: "/cesium.js"
};

const onReady = (readyObj: any) => {
  // 设置地球底色
  const color = Cesium.Color.fromCssColorString("#23245a");
  viewer = readyObj.viewer;
  viewer.scene.globe.baseColor = color;
  viewer.scene.backgroundColor = color;

  useMapEventChannel().post({ type: "map-event", data: "ready" });
  emit("ready");
};

// 轨迹回放准备就绪
const onTrackPlaybackReady = (trackViewer: any) => {
  console.log("轨迹回放组件准备就绪");
  // 可以在这里添加额外的轨迹回放相关逻辑
};

// 监听相机移动事件
const handleCameraMove = (locationData: any) => {
  if (viewer && locationData) {
    const { lng, lat, height = 1000 } = locationData;

    // 使用 flyTo 方法移动相机到指定位置
    viewer.camera.flyTo({
      destination: Cesium.Cartesian3.fromDegrees(lng, lat, height),
      duration: 2.0 // 飞行时间（秒）
    });
  }
};

onMounted(() => {
  // 监听相机移动事件
  mittBus.on("moveCameraToLocation", handleCameraMove);
});

onUnmounted(() => {
  // 清理事件监听
  mittBus.off("moveCameraToLocation", handleCameraMove);
});
</script>
