<template>
  <div v-if="list.length">
    <div v-for="task in list" class="task-details">
      <el-steps
        v-if="task?.taskStepList.length"
        :active="getActive(task?.taskStepList)"
        align-center
        finish-status="success"
        style="flex: 1"
      >
        <div style="padding-right: 25px; color: rgba(101, 102, 102, 1)">{{ task?.trainName }}</div>
        <el-step v-for="v in task?.taskStepList" :key="v.id" :title="v.name">
          <template #icon>
            <!--执行完成-->
            <img v-if="v.status === 2" alt="" class="step__icon" src="./imgs/successIcon.svg" />
            <!--执行中-->
            <img v-else-if="v.status === 1" alt="" class="step__icon" src="./imgs/loadIcon.svg" />
            <!--待执行-->
            <div v-else class="step__icon"></div>
          </template>
        </el-step>
      </el-steps>
    </div>
  </div>
  <div v-else class="table-empty">
    <img alt="notData" src="@/assets/images/noData.png" />
  </div>
</template>

<script lang="tsx" setup>
import { ref, watch } from "vue";
import { getDispatchTaskListByMap } from "@/api/modules/dispatch";
const props = defineProps<{
  data: {
    code?: string;
    id?: number;
  };
}>();
const fetchData = async () => {
  // 获取任务明细的数据
  let res = await getDispatchTaskListByMap({ projectDetailsId: props.data?.id });
  list.value = res?.data;
};
const list: any = ref([]);
// 写一个函数传入数组对象,根据数组对象中的status字段返回,正在执行中对象的索引
const getActive = (list: any[]) => {
  if (list?.length) return list.findIndex(v => Number(v.status) === 1);
  return 0;
};
watch(
  () => props.data,
  () => {
    fetchData();
    // getUserList({ roleId: props.roleId });
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
.task-details {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  border-top: 1px solid rgba(235, 237, 241, 1);
  padding: 20px;
  box-sizing: border-box;
  background-color: #fff;
  //:deep(.el-step) {
  //  position: relative;
  //  .el-step__main {
  //    text-align: center;
  //    position: relative;
  //    left: -38%;
  //  }
  //}
  /* :deep(.el-step:last-of-type.is-flex) {
    flex-basis: 10.5% !important;
  }*/
  //border-radius: 10px;
  :deep(.el-step__line-inner) {
    height: 2px;
    top: 12px;
    border-color: rgba(0, 194, 144, 1);
  }
  :deep(.step__icon) {
    height: 24px;
    width: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #ffffff;
    border-radius: 50%;
  }
  .is-wait .step__icon {
    border: 2px solid #ebedf1;
  }
  :deep(.el-step__head.is-success) {
    color: rgba(0, 194, 144, 1);
  }

  :deep(.is-success .el-step__line-inner) {
    width: 100% !important;
    border-width: 1px !important;
  }

  :deep(.el-step__title.is-process) {
    color: rgba(0, 194, 144, 1);
    font-weight: normal;
  }
  :deep(.el-step__title.is-success) {
    color: rgba(0, 194, 144, 1);
  }

  :deep(.el-step__line) {
    background-color: rgba(235, 237, 241, 1);
  }
  //:deep(.el-step__title) {
  //  line-height: normal;
  //}
}
.table-empty {
  line-height: 30px;
  position: absolute;
  text-align: center;
  top: 75%;
  left: 50%;
  img {
    display: inline-flex;
  }
}
</style>
