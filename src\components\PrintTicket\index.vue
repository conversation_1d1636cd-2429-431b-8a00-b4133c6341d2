<template>
  <div id="maintain-ticket" style="width: 750px; padding: 20px 20px 0 20px">
    <table style="width: 100%; margin-bottom: 20px">
      <tr>
        <td style="text-align: left">打印时间：{{ dayjs().format("YYYY-MM-DD HH:mm:ss") }}</td>
        <td style="text-align: right">保养工单号：{{ number }}</td>
      </tr>
    </table>

    <div style="text-align: center; margin: 20px 0; font-size: 24px; font-weight: bold">无人矿卡保养工单</div>

    <div style="margin: 15px 0">
      <table style="width: 100%">
        <tr>
          <td style="font-size: 18px; font-weight: bold; white-space: nowrap">工单信息</td>
          <td style="width: 100%"><div style="border-bottom: 1px solid rgba(26, 26, 26, 1); margin-left: 8px"></div></td>
        </tr>
      </table>
    </div>
    <div class="order-message">
      <div v-for="item in labels" :key="item.filed" class="msg-item">
        <div class="item-label">{{ item.label }}</div>
        <div v-if="item.filed !== 'empty'" class="item-value">{{ props[item.filed] || "-" }}</div>
        <!--        <div v-else class="empty_line"></div>-->
      </div>
    </div>

    <div style="margin: 15px 0">
      <table style="width: 100%">
        <tr>
          <td style="font-size: 18px; font-weight: bold; white-space: nowrap">保养项目</td>
          <td style="width: 100%"><div style="border-bottom: 1px solid rgba(26, 26, 26, 1); margin-left: 8px"></div></td>
        </tr>
      </table>
    </div>

    <table border="1" style="border-collapse: collapse; width: 100%; margin-top: 20px">
      <thead>
        <tr style="height: 40px">
          <th style="text-align: left; padding: 8px; width: 30%">保养部位</th>
          <th style="text-align: left; padding: 8px">保养方式</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="(item, index) in maintenancePositionList" :key="index">
          <td style="padding: 8px; vertical-align: top">{{ item.position }}</td>
          <td style="padding: 8px">
            <div style="margin-bottom: 8px">材料准备: {{ item.material }}</div>
            <div v-html="item.method"></div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <iframe id="print-frame" style="display: none"></iframe>
</template>
<script lang="ts" setup>
import dayjs from "dayjs";
import { columnMap } from "@/views/maintenance/maintenanceOrder/types.ts";
const props = defineProps<{
  id: string;
  // 工单号
  number: string;
  // 保养人
  chargePerson: string;
  // 保养间隔
  cycle: string;
  // 工单生成时间
  maintenanceTime: string;
  // 矿车名称
  deviceName: string;
  // 保养项目
  maintenancePositionList: {
    // 保养部位
    position: string;
    // 保养方式
    method: string;
    // 材料准备
    material: string;
  }[];
}>();

// 订单信息label项
const labels = [
  { filed: columnMap.get("保养工单号"), label: "保养工单号" },
  { filed: columnMap.get("矿车名称"), label: "矿车名称" },
  { filed: columnMap.get("保养间隔"), label: "保养间隔" },
  { filed: columnMap.get("工单生成时间"), label: "工单生成时间" },
  { filed: columnMap.get("保养负责人"), label: "保养负责人" }
];
const print = () => {
  const printFrame = document.getElementById("print-frame")! as HTMLIFrameElement;
  const printContent = document.getElementById("maintain-ticket")!;
  const frameDoc = printFrame.contentWindow!.document;
  frameDoc.open();
  frameDoc.write(`
          <!DOCTYPE html>
          <html>
            <head>
            <style>
               @page {
                size: A4;
                margin: 0;
                }
               .order-message {
               display: flex;
               justify-content: space-between;
               }
               .item-label {
               font-weight: bold;
               }
               img{
               max-width: 100%;
               height: auto;
               }
            </style>
            </head>
            <body>
              ${printContent.outerHTML}
            </body>
          </html>
        `);
  frameDoc.close();

  // 等待样式加载完成后打印
  setTimeout(() => {
    printFrame.contentWindow!.focus();
    printFrame.contentWindow!.print();
  }, 500);
};
defineExpose({ print });
</script>
