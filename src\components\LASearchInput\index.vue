<template>
  <ElInput :model-value="computedModelValue" size="large" v-bind="$attrs" @update:modelValue="handleInput">
    <template #prefix>
      <img :src="useBlueIcon ? search_blue : search" alt="" class="search-prefix" />
    </template>
  </ElInput>
</template>

<script lang="ts" setup>
/**
 * @file 搜索框组件，包装了搜索icon
 * <AUTHOR>
 * @date 2024/10/30
 */
import { ElInput } from "element-plus";
import search from "./img/search.png?url";
import search_blue from "./img/search_blue.png?url";
import { computed } from "vue";

const props = defineProps({
  modelValue: {
    type: String,
    default: ""
  },
  // 是否使用蓝色的搜索icon
  useBlueIcon: { type: Boolean, default: false }
});
const emits = defineEmits(["update:modelValue"]);

const computedModelValue = computed(() => props.modelValue.replace(/(\\_)|(\\%)/g, match => match.substring(1)));
const handleInput = e => {
  emits(
    "update:modelValue",
    e.replace(/(_)|(%)/g, match => "\\" + match)
  );
};
</script>

<style lang="scss" scoped>
.search-prefix {
  width: 20px;
  height: 20px;
}
::v-deep(.el-input__prefix-inner) {
  display: flex;
  align-items: center;
}
</style>
