<template>
  <div class="task-queue">
    <LACard :showHeader="false" shadow="never">
      <LATitle title="任务队列" />
      <div v-if="!error && data?.length > 0" class="task-queue-content">
        <div v-for="(item, i) in data" :key="i" class="task-details">
          <div class="task-details-item">
            <step :task-queue-data="item" />
          </div>
        </div>
      </div>
      <PlaceholderImage v-else :type="showImgType" />
    </LACard>
  </div>
</template>
<script lang="ts" setup>
/**
 * @file 首页-任务队列
 * <AUTHOR>
 * @date 2025/1/23
 */
import LATitle from "@/components/LATitle.vue";
import LACard from "@/components/LACard/index.vue";
import Step from "@/views/home/<USER>/TaskQueue/step.vue";
import { computed } from "vue";
import PlaceholderImage from "@/components/PlaceholderImage.vue";
import { useTaskQueue } from "@/views/home/<USER>";
const { data, error } = useTaskQueue();

// 控制占位图类型noPermission/noData
const showImgType = computed(() => {
  if (error.value && error.value.status === 401) {
    return "noPermission";
  }
  return "noData";
});
</script>
<style scoped>
.task-queue-content {
  flex: 1;
  overflow-x: auto;
}

.task-details {
  margin-bottom: 48px;
}

.task-details-item {
  height: 38px;
  background: linear-gradient(to right, rgba(243, 247, 255) 95%, white 5%);
}
</style>
