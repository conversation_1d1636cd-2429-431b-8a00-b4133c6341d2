/**
 * @file 调度管理-调度任务-类型声明文件
 * <AUTHOR>
 * @date 2024/12/9
 */

// table字段声明
export const columnMap: any = new Map([
  ["任务类型", "type"],
  ["任务类型名称", "projectTypeName"],
  ["任务编号", "number"],
  ["铲点", "loadingPointName"],
  ["卸点", "unloadingPointName"],
  ["物料类型", "materialName"],
  ["状态", "status"],
  ["开始时间", "startTime"]
]);

// 状态枚举
export enum StatusEnum {
  /** 未开始*/
  PROJECT_PENDING_EXECUTION = 0,
  /** 执行中*/
  PROJECT_IN_PROGRESS = 1,
  /** 已结束*/
  PROJECT_DONE = 2,
  /** 暂停中*/
  PROJECT_WAIT = 3,
  /** 已取消*/
  PROJECT_CANCEL = 4,
  /** 待结束*/
  PROJECT_WAIT_FINSH = 5
}
// 任务状态
export const TaskStatusEnum = [
  {
    bg: "rgba(229, 249, 244)",
    status: StatusEnum.PROJECT_IN_PROGRESS,
    color: "rgba(0, 194, 144, 1)",
    text: "执行中"
  },
  {
    bg: "rgba(255, 241, 236)",
    color: "rgba(249, 116, 75, 1)",
    status: StatusEnum.PROJECT_WAIT,
    text: "暂停中"
  },
  {
    bg: "rgba(234, 240, 255)",
    color: "rgba(53, 106, 253, 1)",
    status: StatusEnum.PROJECT_PENDING_EXECUTION,
    text: "未开始"
  },
  {
    text: "已结束",
    bg: "rgba(239, 239, 239)",
    color: "rgba(101, 102, 102, 1)",
    status: StatusEnum.PROJECT_DONE
  },
  {
    text: "已取消",
    bg: "rgba(239, 239, 239)",
    color: "rgba(101, 102, 102, 1)",
    status: StatusEnum.PROJECT_CANCEL
  },
  {
    text: "待结束",
    bg: "rgba(229, 249, 244)",
    color: "rgba(0, 194, 144, 1)",
    status: StatusEnum.PROJECT_WAIT_FINSH
  }
];
