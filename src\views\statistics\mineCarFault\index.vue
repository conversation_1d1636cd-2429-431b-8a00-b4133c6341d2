<template>
  <div class="page-statistics">
    <SearchForm @search="handleSearch" @otherValue="handleOtherValue" />
    <div class="main">
      <div class="card-list">
        <DataCard
          v-for="(item, idx) in DataCardData"
          :key="item.title"
          :config="item"
          :color="ThemeColors?.[idx]"
          :weeklyCurrentDate="weeklyCurrentDate"
        />
      </div>
      <div class="chart-list">
        <SalesChart :config="lineChartConfig" />
        <PieChart :config="pieChartConfig" />
      </div>
      <LATitle title="故障详情" style="margin-bottom: 10px" />
      <StatisticsTable :api="getMiningCarFaultDetailList" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import dayjs from "dayjs";
import { ref, provide, inject } from "vue";
import LATitle from "@/components/LATitle.vue";
import SearchForm from "./components/SearchForm.vue";
import DataCard from "./components/DataCard.vue";
import SalesChart from "./components/SalesChart.vue";
import PieChart from "./components/PieChart.vue";
import StatisticsTable from "./components/StatisticsTable.vue";

import {
  getMiningCarFaultCount,
  getMiningCarFaultCountLine,
  getMiningCarFaultDuration,
  getMiningCarFaultDurationPie,
  getMiningCarFaultDurationAvg,
  getMiningCarFaultResponseAvg,
  getMiningCarFaultDetailList
} from "@/api/modules/statistics";

const ThemeColors = inject<string>("ThemeColors");

export interface SearchParams {
  reportType: string;
  date: string;
  areaId?: string;
  carId?: string;
  chartType?: string;
  weekStartDateCurrent?: string;
  weekEndDateCurrent?: string;
  weekStartDateYoy?: string;
  weekEndDateYoy?: string;
}

const searchParams = ref<SearchParams>({
  reportType: "day",
  date: dayjs().format("YYYY-MM-DD")
});

provide("searchParams", searchParams);

const handleSearch = (data: SearchParams) => {
  searchParams.value = data;
  console.log("searchParams", data);
};
const weeklyCurrentDate = ref("");
const handleOtherValue = (data: any) => {
  console.log("OtherValues", data);
  weeklyCurrentDate.value = data;
};

const DataCardData = [
  {
    title: "故障次数",
    api: getMiningCarFaultCount,
    dataKey: "faultsCount",
    unit: "次",
    dayAvg: true
  },
  {
    title: "持续时长",
    api: getMiningCarFaultDuration,
    dataKey: "faultDuration",
    unit: "小时",
    dayAvg: true
  },
  {
    title: "平均持续时长",
    api: getMiningCarFaultDurationAvg,
    dataKey: "faultDurationAvg",
    unit: "分钟",
    dayAvg: false
  },
  {
    title: "平均响应时长",
    api: getMiningCarFaultResponseAvg,
    dataKey: "respAvgTime",
    unit: "分钟",
    dayAvg: false
  }
];

const lineChartConfig = {
  title: "故障次数",
  api: getMiningCarFaultCountLine,
  unit: "次"
};

const pieChartConfig = {
  title: "持续时长",
  api: getMiningCarFaultDurationPie,
  tableField: {
    name: "持续时长",
    value: "数量",
    ratio: "占比"
  }
};
</script>

<style lang="scss" scoped>
.page-statistics {
  width: 100%;
}
.main {
  padding: 20px;
  background: #ffffff;
  border-radius: 8px;
}
.card-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}
.chart-list {
  display: flex;
  flex-wrap: wrap;
  gap: 9px;
  margin: 20px auto;
}
</style>
