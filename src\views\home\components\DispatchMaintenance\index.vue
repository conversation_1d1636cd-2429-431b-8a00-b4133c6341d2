<template>
  <div class="dispatch-maintenance">
    <LACard :showHeader="false" shadow="never">
      <LATitle :bottom="0" title="维修保养" />
      <div v-if="!hasPermission" class="maintenance-container">
        <div class="repair-order order">
          <div class="order-title">维修工单</div>
          <div class="order-content" style="color: rgba(242, 85, 85, 1)">{{ data?.maintenanceCount }}</div>
        </div>
        <div class="maintenance-order order">
          <div class="order-title">保养工单</div>
          <div class="order-content">{{ data?.repairCount }}</div>
        </div>
      </div>
      <PlaceholderImage v-else type="noPermission" />
    </LACard>
  </div>
</template>

<script lang="ts" setup>
/**
 * @file 首页-维修保养
 * <AUTHOR>
 * @date 2025/1/22
 */
import LATitle from "@/components/LATitle.vue";
import LACard from "@/components/LACard/index.vue";
import PlaceholderImage from "@/components/PlaceholderImage.vue";
import { computed } from "vue";
import { useMaintainStatistics } from "@/views/home/<USER>";

const { data, error } = useMaintainStatistics();
// 401则为没有权限
const hasPermission = computed(() => {
  return error.value?.status === 401;
});
</script>

<style scoped>
.maintenance-container {
  flex: 1;
  display: flex;
  gap: 10px;
  padding-top: 20px;

  .order-title {
    white-space: nowrap;
    font-size: 14px;
    color: #656666;
  }

  .order-content {
    font-size: 28px;
    font-weight: bold;
  }

  .order {
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 20px;
    flex: 1;
  }

  .repair-order {
    background-color: rgba(242, 85, 85, 0.08);
  }

  .maintenance-order {
    background-color: rgba(41, 177, 255, 0.08);
  }
}
</style>
