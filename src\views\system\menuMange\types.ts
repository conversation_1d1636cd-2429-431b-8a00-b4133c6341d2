/**
 * @file 系统管理-菜单管理-资源类型|新增编辑表单|详情 字段定义
 * <AUTHOR>
 * @date 2024/11/7
 */

// 资源类型
export enum ResourceType {
  /* 菜单*/
  MENU = "ResourceMenu",
  BUTTON = "ResourceButton"
}

// 详情字段
export const detailMap = new Map([
  ["名称", "cnName"],
  ["上级菜单", "pname"],
  ["icon", "image"],
  ["路径", "url"],
  ["序号", "sort"]
]);

// 新增编辑菜单字段
export const addMenuFieldsMap: any = new Map([
  ["菜单名称", "name"],
  ["菜单Code", "menuCode"],
  ["上级菜单", "pName"],
  ["Icon", "image"],
  ["菜单路径", "url"],
  ["菜单序号", "sort"],
  ["菜单类型", "type"]
]);

// 新增编辑按钮字段
export const addButtonFieldsMap = new Map([
  ["按钮名称", "name"],
  ["按钮编码", "menuCode"]
]);
