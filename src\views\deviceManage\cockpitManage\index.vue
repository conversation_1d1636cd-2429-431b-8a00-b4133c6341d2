<template>
  <form-create v-model:api="fApi" :option="option" :rule="rule"></form-create>
</template>
<script lang="tsx" setup>
/**
 * @file 设备管理-驾驶舱管理
 * <AUTHOR>
 * @date 2024/11/20
 */
import { ref } from "vue";
import { columnMap } from "./types";
import { cockpitManageFormCreate } from "./components/formCreate";
import formCreate from "@form-create/element-ui";
import { deleteCockpits, getCockpitsList, saveCockpits } from "@/api/modules/device";
const fApi = ref();

const option = {
  form: { inline: true },
  resetBtn: false,
  submitBtn: false
};

const rule = ref([
  {
    type: "SearchFormOperation",
    field: "v:search",
    wrap: { style: "marginBottom: 0" },
    children: [
      {
        type: "input",
        field: "search",
        props: {
          size: "default",
          placeholder: "驾驶舱名称"
        }
      },
      // 新增
      {
        type: "AddBtn",
        slot: "suffix",
        props: {
          btn: { content: "新增驾驶舱", auth: "add" },
          dialog: {
            title: "新增驾驶舱" // 绑定到弹窗根节点的样式
          },

          size: "default",
          submitRequest: saveCockpits
        },
        children: [cockpitManageFormCreate]
      }
    ]
  },
  {
    type: "ProTable",
    props: {
      columns: [
        {
          prop: columnMap.get("名称"),
          label: "名称"
        },
        {
          prop: columnMap.get("机器码"),
          label: "机器码"
        },
        {
          prop: columnMap.get("授权到期时间"),
          label: "授权到期时间"
        },
        { prop: "operation", label: "操作", fixed: "right" }
      ],
      fetch: getCockpitsList,
      operations: [
        { content: "修改", action: "edit", auth: "update" },
        { content: "删除", action: "delete", auth: "delete", props: { style: { color: "rgba(242, 85, 85, 1)" } } }
      ]
    },

    children: [
      {
        type: "EditBtn",
        props: {
          action: "edit",
          dialog: {
            title: "修改驾驶舱"
          },
          submitRequest: saveCockpits
        },
        children: [cockpitManageFormCreate]
      },
      {
        type: "ConfirmDialog",
        on: {
          // 监听弹窗组件抛出的的afterSubmit事件，用于刷新页面
          afterSubmit: () => {
            // 刷新，调用组件内部请求方法
            fApi.value.exec("v:search", "onSearch");
          }
        },
        props: {
          action: "delete",
          title: "是否删除驾驶舱",
          message: "删除后不可恢复",
          subtitle: row => {
            return row[columnMap.get("名称")];
          },
          // 模拟请求param：参数
          submitRequest: deleteCockpits
        }
      }
    ]
  }
]);
</script>
<style lang="scss" scoped>
:deep(.el-row) {
  height: var(--page-height);
}

.el-form-item {
  margin-right: 4px !important;
}
</style>
