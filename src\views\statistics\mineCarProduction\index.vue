<template>
  <div class="page-statistics">
    <SearchForm @search="handleSearch" @otherValue="handleOtherValue" />
    <div class="main">
      <div class="card-list">
        <DataCard
          v-for="(item, idx) in DataCardData"
          :key="item.title"
          :config="item"
          :color="ThemeColors?.[idx]"
          :weeklyCurrentDate="weeklyCurrentDate"
        />
      </div>
      <div class="chart-list">
        <SalesChart v-for="item in chartConfig" :key="item.title" :config="item" />
      </div>
      <LATitle title="生产详情" style="margin-bottom: 10px" />
      <StatisticsTable :api="getMiningCarFaultDetailList" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import dayjs from "dayjs";
import { ref, provide, inject } from "vue";
import LATitle from "@/components/LATitle.vue";
import SearchForm from "./components/SearchForm.vue";
import DataCard from "./components/DataCard.vue";
import SalesChart from "./components/SalesChart.vue";
import StatisticsTable from "./components/StatisticsTable.vue";

import {
  getMiningCarFaultCount,
  getMiningCarFaultCountLine,
  getMiningCarFaultDuration,
  getMiningCarFaultDurationAvg,
  getMiningCarFaultResponseAvg,
  getMiningCarFaultDetailList
} from "@/api/modules/statistics";

const ThemeColors = inject<string>("ThemeColors");

export interface SearchParams {
  reportType: string;
  date: string;
  areaId?: string;
  carId?: string;
  chartType?: string;
  weekStartDateCurrent?: string;
  weekEndDateCurrent?: string;
  weekStartDateYoy?: string;
  weekEndDateYoy?: string;
}

const searchParams = ref<SearchParams>({
  reportType: "day",
  date: dayjs().format("YYYY-MM-DD")
});

provide("searchParams", searchParams);

const handleSearch = (data: SearchParams) => {
  searchParams.value = data;
  console.log("searchParams", data);
};

const weeklyCurrentDate = ref("");
const handleOtherValue = (data: any) => {
  console.log("OtherValues", data);
  weeklyCurrentDate.value = data;
};

const DataCardData = [
  {
    title: "产量",
    api: getMiningCarFaultCount,
    dataKey: "faultsCount",
    unit: "吨"
  },
  {
    title: "生产计划",
    api: getMiningCarFaultDuration,
    dataKey: "faultDuration",
    unit: ""
  },
  {
    title: "生产任务",
    api: getMiningCarFaultDurationAvg,
    dataKey: "faultDurationAvg",
    unit: ""
  },
  {
    title: "车次",
    api: getMiningCarFaultResponseAvg,
    dataKey: "faultDurationAvg",
    unit: "次"
  },
  {
    title: "里程",
    api: getMiningCarFaultResponseAvg,
    dataKey: "faultDurationAvg",
    unit: "km"
  },
  {
    title: "矿车均车次",
    api: getMiningCarFaultResponseAvg,
    dataKey: "faultDurationAvg",
    unit: "次"
  }
];

const chartConfig = [
  {
    title: "产量",
    api: getMiningCarFaultCountLine,
    unit: "吨"
  },
  {
    title: "车次",
    api: getMiningCarFaultCountLine,
    unit: "次"
  }
];
</script>

<style lang="scss" scoped>
.page-statistics {
  width: 100%;
}
.main {
  padding: 20px;
  background: #ffffff;
  border-radius: 8px;
}
.card-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}
.chart-list {
  display: flex;
  flex-wrap: wrap;
  gap: 9px;
  margin: 20px auto;
}
</style>
