/**
 * @file 调度管理-调度计划-类型声明文件
 * <AUTHOR>
 * @date 2024/12/9
 */

// table字段声明
export const columnMap: any = new Map([
  ["任务类型", "type"],
  ["任务类型名称", "typeName"],
  ["计划编号", "projectNumber"],
  ["班次名称", "flightsName"],
  ["班次", "flightsId"],
  ["计划开始时间", "projectStartTime"],
  ["计划结束时间", "projectEndTime"],
  ["地磅", "weighingCodes"]
]);
// 计划明细表格
export const detailColumnMap: any = new Map([
  ["铲点", "loadingPointName"],
  ["卸点", "unloadingPointName"],
  ["物料类型", "materialName"],
  ["状态", "status"],
  ["编号", "number"],
  ["开始时间", "startTime"]
]);
// 新增/修改form字段声明
export const formMap = new Map([
  ["任务类型", "type"],
  ["计划编号", "projectNumber"],
  ["班次", "flightsId"],
  ["计划开始时间", "projectStartTime"],
  ["计划结束时间", "projectEndTime"],
  ["铲点", "loadingPoint"],
  ["卸点", "unloadingPoint"],
  ["物料类型", "materialId"],
  ["是否需要过磅", "weighingFlag"]
]);

// 调度计划状态枚举
export enum PlanStatusEnum {
  /** 未开始 */
  PROJECT_PENDING_EXECUTION = 0,
  /** 执行中 */
  PROJECT_IN_PROGRESS = 1,
  /** 已结束 */
  PROJECT_DONE = 2,
  /** 待结束 */
  PROJECT_CANCEL = 3,
  /** 执行失败 */
  PROJECT_ERROR = -1
}
export const PlanStatusEnumMap = [
  {
    text: "未开始",
    bg: "rgba(234, 240, 255)",
    color: "rgba(53, 106, 253, 1)",
    status: PlanStatusEnum.PROJECT_PENDING_EXECUTION
  },
  {
    text: "执行中",
    bg: "rgba(229, 249, 244)",
    color: "rgba(0, 194, 144, 1)",
    status: PlanStatusEnum.PROJECT_IN_PROGRESS
  },
  {
    text: "执行完成",
    bg: "rgba(239, 239, 239)",
    color: "rgba(101, 102, 102, 1)",
    status: PlanStatusEnum.PROJECT_DONE
  },
  {
    text: "待结束",
    bg: "rgba(229, 249, 244)",
    color: "rgba(0, 194, 144, 1)",
    status: PlanStatusEnum.PROJECT_CANCEL
  },
  {
    text: "执行失败",
    bg: "rgba(255, 241, 236)",
    color: "rgba(242, 85, 85, 1)",
    status: PlanStatusEnum.PROJECT_ERROR
  }
];
