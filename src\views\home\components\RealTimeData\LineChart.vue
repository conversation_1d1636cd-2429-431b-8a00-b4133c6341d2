<template>
  <!-- 产量图表 -->
  <div class="chart-container">
    <div v-if="!hasPermission" class="charts-content">
      <div class="statistics">
        <div class="title">今日产量(吨)</div>
        <div class="total">
          {{ Number(dispatchStatistics?.weightStats.number || 0).toLocaleString() }}
        </div>
      </div>
      <div class="charts">
        <ECharts :option="weightOption" class="chart" resize />
      </div>
      <div class="statistics">
        <div class="title">今日完成车次(次)</div>
        <div class="total">
          {{ Number(dispatchStatistics?.taskCount.number || 0).toLocaleString() }}
        </div>
      </div>
      <div class="charts">
        <ECharts :option="tripOption" class="chart" resize />
      </div>
    </div>
    <PlaceholderImage v-else type="noPermission" />
  </div>
</template>

<script lang="ts" setup>
import { computed, type ComputedRef } from "vue";
import { ECOption } from "@/components/ECharts/config";
import ECharts from "@/components/ECharts/index.vue";
import { useDispatchStatistics } from "@/views/home/<USER>";
import api from "@/views/home/<USER>/RealTimeData/api.json";
const { data: dispatchStatistics, error } = useDispatchStatistics();
import { isType } from "@/utils";
import PlaceholderImage from "@/components/PlaceholderImage.vue";
// // 401则为没有权限
const hasPermission = computed(() => {
  return isType(dispatchStatistics.value) === "null" && error.value?.status === 401;
});

// 基础配置
const baseOption: Partial<ECOption> = {
  grid: { top: 10, right: 0, bottom: 0, left: 0 },
  xAxis: {
    type: "category",
    boundaryGap: false,
    axisLine: { show: false },
    axisTick: { show: false },
    axisLabel: { show: false }
  },
  yAxis: {
    type: "value",
    splitLine: { show: false }
  },
  tooltip: {
    trigger: "axis",
    confine: true,
    backgroundColor: "#fff",
    borderColor: "rgba(202, 217, 252, 1)",
    padding: [0],
    textStyle: {
      color: "rgba(101, 102, 102, 1)"
    }
  }
} as const;
// 产量图表配置
const weightOption = computed(() => ({
  ...baseOption,
  xAxis: {
    ...baseOption.xAxis,
    data: dispatchStatistics.value?.weightStats.list.map(item => item.name) || []
  },
  series: [
    {
      data: dispatchStatistics.value?.weightStats.list.map(item => item.number || 0) || [],
      name: "产量",
      type: "line",
      smooth: true,
      showSymbol: false,
      itemStyle: { color: "rgba(53, 106, 253, 1)" }, // 设置系列的颜色
      lineStyle: { color: "rgba(53, 106, 253, 1)" },
      areaStyle: { color: "rgb(234, 240, 255,1)" }
    }
  ],
  tooltip: {
    ...baseOption.tooltip,
    axisPointer: {
      lineStyle: {
        type: "solid",
        width: 1,
        color: "rgba(202, 217, 252, 1)"
      }
    },
    formatter: params => {
      const title = params[0].name;
      const value = params[0].value === undefined || params[0].value === null ? "暂无数据" : params[0].value + "吨";
      return `<div style="border-bottom: 1px solid rgba(255, 255, 255, .1);text-align: center;padding: 5px 15px">${title}</div>
              <div style="padding:5px 15px">${params[0].marker} 产量：<span style="font-weight: bold">${value}</span></div>`;
    }
  }
})) as unknown as ComputedRef<ECOption>;
// 车次图表配置
const tripOption = computed(() => ({
  ...baseOption,
  xAxis: {
    ...baseOption.xAxis,
    data: dispatchStatistics.value?.taskCount.list.map(item => item.name) || []
  },
  series: [
    {
      data: dispatchStatistics.value?.taskCount.list.map(item => item.number || 0) || [],
      name: "车次",
      type: "line",
      smooth: true,
      showSymbol: false,
      itemStyle: { color: "rgba(0, 194, 144, 1)" }, // 设置系列的颜色

      lineStyle: { color: "rgba(0, 194, 144, 1)" },
      areaStyle: { color: "rgba(229, 249, 244,1)" }
    }
  ],
  tooltip: {
    ...baseOption.tooltip,
    axisPointer: {
      lineStyle: {
        type: "solid",
        width: 1,
        color: "rgba(173, 235, 219, 1)"
      }
    },
    formatter: params => {
      const title = params[0].name;
      const value = params[0].value === undefined || params[0].value === null ? "暂无数据" : params[0].value + "次";
      return `<div style="border-bottom: 1px solid rgba(255, 255, 255, .1);text-align: center;padding: 5px 15px">${title}</div>
              <div style="padding:5px 15px">${params[0].marker} 车次：<span style="font-weight: bold">${value}</span></div>`;
    }
  }
})) as unknown as ComputedRef<ECOption>;
</script>

<style lang="scss" scoped>
.chart-container {
  .charts-content {
    padding: 0 8px;
    display: flex;
    overflow: hidden;
    gap: 50px;
    .statistics {
      margin-top: 10px;
      display: flex;
      flex-direction: column;
      gap: 10px;

      .title {
        font-weight: bold;
        font-size: 14px;
        color: rgba(101, 102, 102, 1);
      }

      .total {
        font-size: 28px;
        line-height: 100%;
        font-weight: bold;
        color: rgba(26, 26, 26, 1);
      }
    }

    .charts {
      flex: 1;
      overflow: hidden;
    }
  }
}
</style>
