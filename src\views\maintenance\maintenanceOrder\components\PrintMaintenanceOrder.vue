<template>
  <div id="printMaintenanceOrderPage" style="width: 750px; padding: 20px 20px 0 20px">
    <div class="page-title">保养工单</div>
    <div class="title-container">
      <span class="title">工单信息</span>
      <div class="split-line"></div>
    </div>
    <div class="order-message">
      <div v-for="item in labels" :key="item.filed" class="msg-item">
        <div class="item-label">{{ item.label }}</div>
        <div v-if="item.filed !== 'empty'" class="item-value">{{ printData[item.filed] || "-" }}</div>
        <div v-else class="empty_line"></div>
      </div>
    </div>

    <!--  保养内容  -->
    <div class="title-container">
      <span class="title">保养项目</span>
      <div class="split-line"></div>
    </div>
    <table>
      <thead>
        <tr>
          <th class="maintenance-position">保养部位</th>
          <th>保养方式</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="item in contentsLIst" :key="item.id">
          <td>{{ item[maintenanceResultFieldsMap.get("保养部位")] }}</td>
          <td>{{ item[maintenanceResultFieldsMap.get("保养方式")] }}</td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script setup>
import { computed } from "vue";
import {
  // 保养工单字段
  columnMap,
  // 所需材料字段
  maintenanceResultFieldsMap
} from "../types";
/**
 * @file 打印保养工单信息
 * <AUTHOR>
 * @date 2025/2/6
 */
const props = defineProps({
  printData: {
    type: Object,
    default: () => ({})
  }
});
// 订单信息label项
const labels = [
  { filed: columnMap.get("保养工单号"), label: "保养工单号" },
  { filed: columnMap.get("矿车名称"), label: "矿车名称" },
  { filed: columnMap.get("保养间隔"), label: "保养间隔" },
  { filed: columnMap.get("工单生成时间"), label: "工单生成时间" },
  { filed: columnMap.get("保养负责人"), label: "保养负责人" }
];

// 保养内容列表
const contentsLIst = computed(() => {
  return props.printData.contents || [];
});
</script>

<style scoped>
@import "../../../../../public/css/printMaintenanceOrder.css";
</style>
