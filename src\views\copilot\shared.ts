export const getStatusText = (status: string, deviceType: string) => {
  switch (deviceType) {
    case "deviceCockpits":
      switch (+status) {
        case 1:
          return "空闲";
        case 0:
          return "离线";
        default:
          return "离线";
      }
    case "deviceChargingPile":
      switch (+status) {
        case 1000:
          return "正常";
        case 1001:
          return "充电中";
        case 1002:
          return "故障中";
        case 1003:
          return "测试中";
        case 1004:
          return "离线中";
        default:
          return "离线中";
      }
    case "deviceMineTrain":
      switch (+status) {
        case 1000:
          return "就绪";
        case 1001:
          return "忙碌中";
        case 1002:
          return "充电中";
        case 1003:
          return "故障中";
        case 1004:
          return "休眠中";
        case 1005:
          return "测试中";
        case 1006:
          return "离线中";
        case 1007:
          return "调用中";
        case 1008:
          return "手动模式";
        case 1009:
          return "暂停中";
      }
    case "deviceBulldozers":
      switch (+status) {
        case -1:
          return "故障";
        case 0:
          return "离线";
        case 1:
          return "允许泊车";
        case 2:
          return "等待装载";
        case 3:
          return "装载中";
        case 4:
          return "空闲中";
        default:
          return "离线";
      }
  }
};
// 获取矿卡驱动模式
export const getTruckDriveMode = (status: number) => {
  if (+status === 0) return "自动";
  if (+status === 1) return "手动";
  if (+status === 2) return "远程";
  return "";
};

// isTruckAutoDrive
export const isTruckAutoDrive = (status: number) => {
  return +status === 0;
};
