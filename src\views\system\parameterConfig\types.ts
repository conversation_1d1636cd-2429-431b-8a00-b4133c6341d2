/**
 * @file 系统管理-参数配置-类型声明文件
 * <AUTHOR>
 * @date 2024/11/14
 */

// 参数配置的状态枚举定义
export enum ParameterStatus {
  // 启用
  ENABLE = 0,
  // 禁用
  DISABLED = 1
}

// 配置的状态额外的UI信息
export const parameterStatusInfo = [
  // 启用
  {
    bg: "rgba(0, 194, 144, .1)",
    status: ParameterStatus.ENABLE,
    text: "启用",
    color: "rgba(0, 194, 144, 1)"
  },
  // 禁用
  {
    status: ParameterStatus.DISABLED,
    text: "禁用",
    bg: "rgba(241, 242, 242, 1)",
    color: "rgba(101, 102, 102, 1)"
  }
];

// table列表字段
export const columnMap = new Map([
  ["参数名称", "name"],
  ["参数值", "content"],
  ["触发事件", "triggerFunction"],
  ["状态", "status"]
]);
// form字段
export const formMap = new Map([
  ["参数名称", "name"],
  ["参数值", "content"],
  ["触发事件", "triggerFunction"],
  ["状态", "status"]
]);
