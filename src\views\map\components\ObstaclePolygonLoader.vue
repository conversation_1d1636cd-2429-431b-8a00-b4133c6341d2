<template>
  <vc-entity v-for="(obstacle, index) in obstacles" :key="obstacle.id || index">
    <vc-graphics-polygon
      :hierarchy="obstacle.hierarchy"
      :material="[255, 187, 0, 25.5]"
      :height="0"
      :outline="true"
      :outline-color="[255, 187, 0, 255]"
    />
  </vc-entity>
</template>

<script lang="ts" setup>
/*
  障碍物多边形加载器
*/
import mittBus from "@/utils/mittBus";
import { useDispatchObstacle } from "@/views/copilot/request";
import { computed, onMounted, onUnmounted } from "vue";
import { VcGraphicsPolygon, VcEntity } from "vue-cesium";

const { data: obstacleData } = useDispatchObstacle<any>()!;

// 解析 range 字符串为坐标数组
const parseRange = (range: string) => {
  if (!range) return [];
  return range.split(",").map(coord => {
    const [lng, lat] = coord.trim().split(" ").map(Number);
    return { lng, lat };
  });
};

// 计算坐标数组的中心点
const calculateCenter = (coordinates: { lng: number; lat: number }[]) => {
  if (coordinates.length === 0) return { lng: 0, lat: 0 };

  const sum = coordinates.reduce(
    (acc, coord) => ({
      lng: acc.lng + coord.lng,
      lat: acc.lat + coord.lat
    }),
    { lng: 0, lat: 0 }
  );

  return {
    lng: sum.lng / coordinates.length,
    lat: sum.lat / coordinates.length
  };
};

// 处理障碍物数据，将 range 解析为 hierarchy
const obstacles = computed(() => {
  return (
    obstacleData.value?.map(item => ({
      id: item.id,
      hierarchy: parseRange(item.range),
      description: `Obstacle ${item.id}`,
      deviceId: item.deviceId,
      status: item.status,
      originalData: item
    })) || []
  );
});

onMounted(() => {
  mittBus.on("obstacleLocation", (obstacle: any) => {
    // 解析障碍物的 range 并计算中心点
    if (obstacle.range) {
      const coordinates = parseRange(obstacle.range);
      const center = calculateCenter(coordinates);

      // 通过事件总线发送相机移动请求
      mittBus.emit("moveCameraToLocation", {
        lng: center.lng,
        lat: center.lat,
        height: 10000, // 默认高度
        obstacle: obstacle
      });
    }
  });
});
onUnmounted(() => {
  mittBus.off("obstacleLocation");
});
</script>
