/**
 * @file 系统管理-字典管理-表单文件
 * <AUTHOR>
 * @date 2024/11/8
 */

import { formMap } from "../types";

export const dictManageFormCreate = {
  type: "form-create",
  props: {
    rule: [
      {
        type: "input",
        field: formMap.get("字典名称"),
        title: "字典名称",
        validate: [
          { required: true, message: "请输入字典名称" },
          {
            pattern: /^.{1,20}$/,
            message: "字符限长20位"
          }
        ]
      },
      {
        type: "input",
        field: formMap.get("字典编码"),
        title: "字典编码",
        validate: [
          { required: true, message: "请输入字典编码" },
          {
            pattern: /^.{1,20}$/,
            message: "字符限长20位"
          }
        ]
      }
    ],
    option: {
      submitBtn: false,
      onSubmit(formData, api) {
        // 通知 table 搜索数据变化，刷新数据
        api.top.bus.$emit("searchFormChanged");
      }
    }
  }
};
export const dictListFormCreate = {
  type: "form-create",
  props: {
    rule: [
      {
        type: "input",
        field: formMap.get("上级字典名称"),
        props: {
          disabled: true
        },
        title: "上级字典",
        validate: [{ required: true, message: "请输入上级字典名称" }]
      },
      {
        type: "input",
        field: formMap.get("字典名称"),
        title: "字典名称",
        validate: [{ required: true, message: "请输入字典名称" }]
      },
      {
        type: "input",
        field: formMap.get("字典编码"),
        title: "字典编码",
        validate: [{ required: true, message: "请输入字典编码" }]
      }
    ],
    option: {
      submitBtn: false,
      onSubmit(formData, api) {
        // 通知 table 搜索数据变化，刷新数据
        api.top.bus.$emit("searchFormChanged");
      }
    }
  }
};
