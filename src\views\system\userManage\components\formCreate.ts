/**
 * @file 系统管理-用户管理-表单声明文件
 * <AUTHOR>
 * @date 2024/11/12
 */

import { formMap } from "@/views/system/userManage/types";
import LASelect from "@/components/LASelect";
import LACascader from "@/components/LACascader";
import { getRoleList, getOrgList } from "@/api/modules";

export const userFormCreate = {
  type: "form-create",
  props: {
    rule: [
      {
        type: "input",
        field: formMap.get("账号"),
        props: {
          disabled: true
        },
        update(val, rule, api, init) {
          // 通过id唯一性控制显示隐藏
          rule.props.disabled = api.formData().id;
        },
        title: "账号",
        validate: [
          { required: true, message: "请输入账号" },
          {
            pattern: /^.{1,20}$/,
            message: "字符限长20位"
          }
        ]
      },
      {
        type: "input",
        field: formMap.get("真实姓名"),
        title: "真实姓名",
        validate: [
          { required: true, message: "请输入真实姓名" },
          {
            pattern: /^.{1,20}$/,
            message: "字符限长20位"
          }
        ]
      },
      {
        type: "input",
        field: formMap.get("手机号码"),
        title: "手机号码",
        validate: [
          { required: true, message: "请输入手机号码" },
          {
            pattern: /^\d{11}$/,
            message: "长度11位、数字组成"
          }
        ]
      },
      {
        component: LASelect,
        field: formMap.get("角色id"),
        props: {
          replaceFields: { key: "id", label: "roleName", value: "id" },
          fetch: getRoleList,
          multiple: true
        },
        title: "角色",
        validate: [{ required: true, message: "请选择角色" }]
      },
      {
        component: LACascader,
        field: formMap.get("所属部门"),
        props: {
          props: { expandTrigger: "hover", label: "fullName", value: "id" },
          fetch: getOrgList
        },
        title: "所属部门"
      },
      {
        type: "input",
        field: formMap.get("职位"),
        title: "职位",
        validate: [
          {
            pattern: /^.{1,20}$/,
            message: "字符限长20位"
          }
        ]
      }
    ],
    option: {
      submitBtn: false,
      onSubmit(formData, api) {
        // 通知 table 搜索数据变化，刷新数据
        api.top.bus.$emit("searchFormChanged");
      }
    }
  }
};
