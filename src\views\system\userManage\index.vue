<template>
  <form-create v-model:api="fApi" :option="option" :rule="rule"></form-create>
</template>
<script lang="tsx" setup>
/**
 * @file 系统管理-用户管理
 * <AUTHOR>
 * @date 2024/11/14
 */
import { ref } from "vue";
import { getUserList, addUser, deleteUser, resetPassword, updateUserStatus } from "@/api/modules";
import { columnMap, Status, statusEnum } from "./types";
import formCreate from "@form-create/element-ui";
import { userFormCreate } from "@/views/system/userManage/components/formCreate";
import { useUserStore } from "@/stores/modules/user";

// 获取个人信息
const userInfo = useUserStore().userInfo;

const fApi = ref();
const option = {
  form: { inline: true },
  resetBtn: false,
  submitBtn: false
};

const rule = ref([
  {
    type: "SearchFormOperation",
    field: "v:search",
    wrap: { style: "marginBottom: 0" },
    children: [
      {
        type: "input",
        field: "search",
        props: {
          size: "default",
          placeholder: "账号/手机号/姓名"
        }
      },
      {
        type: "AddBtn",
        slot: "suffix",
        props: {
          btn: { content: "新增用户", auth: "add" },
          dialog: { title: "新增用户" },
          size: "default",
          submitRequest: data => {
            // console.log("提交:", data);
            // 提交的请求
            return addUser({
              ...data,
              roleId: data.roleId.join(","),
              ...(data.systemOrgId && { systemOrgId: data.systemOrgId.at(-1) })
            });
          }
        },
        children: [userFormCreate]
      }
    ]
  },
  {
    type: "ProTable",
    props: {
      columns: [
        {
          prop: columnMap.get("账号"),
          label: "账号",
          render(scope: any) {
            return (
              <div>
                {scope.row.id === userInfo.id ? (
                  <el-tag type="primary" effect="plain" size="small" style={{ marginRight: "2px", padding: "0 4px" }}>
                    我
                  </el-tag>
                ) : null}
                {scope.row[columnMap.get("账号")] || ""}
              </div>
            );
          }
        },
        {
          prop: columnMap.get("真实姓名"),
          label: "真实姓名"
        },
        {
          prop: columnMap.get("手机号码"),
          label: "手机号码"
        },
        {
          prop: columnMap.get("角色"),
          label: "角色"
        },
        {
          prop: columnMap.get("所属部门"),
          label: "所属部门"
        },
        {
          prop: columnMap.get("职位"),
          label: "职位",
          render(scope: any) {
            return <>{scope.row[columnMap.get("职位")] || "-"}</>;
          }
        },
        {
          prop: "status",
          label: "状态",
          tag: true,
          enum: [...statusEnum]
        },
        { prop: "operation", label: "操作", fixed: "right" }
      ],
      fetch: getUserList,
      operationDisabled: row => {
        // 超管不可操作
        return row.roleCode === "ROLE_ADMIN";
      },
      operations: row => {
        const data = [
          {
            content: "修改",
            action: "edit",
            auth: "update",
            show: true
          },
          {
            content: "解冻",
            action: "unfreeze",
            auth: "unfreeze",
            show: row.status === Status.FREEZE && row.id !== userInfo.id
          },
          {
            content: "冻结",
            action: "freeze",
            auth: "freeze",
            show: row.status !== Status.FREEZE && row.id !== userInfo.id
          },
          {
            content: "删除",
            action: "delete",
            show: true,
            props: { style: { color: "rgba(242, 85, 85, 1)" } }
          },
          {
            content: "重置密码",
            action: "reset",
            auth: "reset",
            show: true
          }
        ];
        return data.filter(op => op.show).map(op => op);
      }
    },
    children: [
      {
        type: "EditBtn",
        props: {
          action: "edit",
          dialog: { title: "修改用户" },
          formatter: data => {
            return { ...data, roleId: data.roleId.split(",") };
          },
          submitRequest: data => {
            // 提交的请求
            return addUser({
              ...data,
              roleId: data.roleId.join(","),
              ...(data.systemOrgId && { systemOrgId: data.systemOrgId.at(-1) })
            });
          }
        },
        children: [userFormCreate]
      },
      {
        type: "ConfirmDialog",
        on: {
          // 监听弹窗组件抛出的的afterSubmit事件，用于刷新页面
          afterSubmit: () => {
            // 刷新，调用组件内部请求方法
            fApi.value.exec("v:search", "onSearch");
          }
        },
        props: {
          action: "unfreeze",
          title: "是否解冻账号",
          message: "解冻后,该用户可正常登录系统",
          subtitle: row => {
            return row.userName;
          },
          // 模拟请求param：参数
          submitRequest: param => {
            console.log(param);
            // 模拟提交的请求为2秒
            return updateUserStatus({ id: param.id, status: Status.NORMAL });
          }
        }
      },
      {
        type: "ConfirmDialog",
        on: {
          // 监听弹窗组件抛出的的afterSubmit事件，用于刷新页面
          afterSubmit: () => {
            // 刷新，调用组件内部请求方法
            fApi.value.exec("v:search", "onSearch");
          }
        },
        props: {
          action: "freeze",
          title: "是否冻结账号",
          message: "冻结后用户不能正常登录系统",
          subtitle: row => {
            return row.userName;
          },
          // 模拟请求param：参数
          submitRequest: param => {
            // 模拟提交的请求为2秒
            return updateUserStatus({ id: param.id, status: Status.FREEZE });
          }
        }
      },
      {
        type: "ConfirmDialog",
        on: {
          // 监听弹窗组件抛出的的afterSubmit事件，用于刷新页面
          afterSubmit: () => {
            // 刷新，调用组件内部请求方法
            fApi.value.exec("v:search", "onSearch");
          }
        },
        props: {
          action: "delete",
          // 模拟请求param：参数
          submitRequest: deleteUser
        }
      },
      {
        type: "ConfirmDialog",
        on: {
          // 监听弹窗组件抛出的的afterSubmit事件，用于刷新页面
          afterSubmit: () => {
            // 刷新，调用组件内部请求方法
            fApi.value.exec("v:search", "onSearch");
          }
          // openDialog: (row, api) => {
          //   api.rule.props.subtitle = row.userName;
          //   // console.log(api.rule.props.subtitle);
          // }
        },

        props: {
          title: "是否重置密码",
          message: "重置密码后恢复默认密码",
          action: "reset",
          subtitle: row => {
            return row.userName;
          },
          // 模拟请求param：参数
          submitRequest: param => {
            // 模拟提交的请求为2秒
            return resetPassword(param);
          }
        }
      }
    ]
  }
]);
</script>
<style lang="scss" scoped>
:deep(.el-row) {
  height: var(--page-height);
}

.el-form-item {
  margin-right: 4px !important;
}
</style>
