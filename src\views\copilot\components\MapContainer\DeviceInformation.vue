<template>
  <div class="device-information">
    <el-space :spacer="spacer" class="container" :size="1">
      <div v-for="item of displayData" :key="item.key">
        <div style="text-align: center">{{ item.value }}{{ item.suffix }}</div>
        <div style="font-size: 13px">{{ item.text }}</div>
      </div>
    </el-space>
  </div>
</template>

<script lang="ts" setup>
/**
 * @file 底部设备数据
 * <AUTHOR>
 * @date 2024/11/25
 */
import { ElDivider } from "element-plus";
import { h } from "vue";
const spacer = h(ElDivider, { direction: "vertical", style: { height: "32px", background: "rgba(255,255,255,.1)" } });
const displayData = [
  { suffix: "m", text: "横向误差", value: 3.5, key: "horizon" },
  { suffix: "m", text: "距离误差", value: 3.5, key: "2" },
  { suffix: "°", text: "方向盘角度输入", value: 3.5, key: "3" },
  { suffix: "°", text: "方向盘角度输出", value: 3.5, key: "4" },
  { suffix: "km/h", text: "车辆速度输入", value: 3.5, key: "5" },
  { suffix: "km/h", text: "车辆速度输入出", value: 3.5, key: "6" },
  { suffix: "", text: "姿态误差", value: 3.5, key: "7" }
];
</script>

<style lang="scss" scoped>
.container {
  border-radius: 66px 66px 0 0;
  background: linear-gradient(180deg, rgba(4, 5, 54, 0.8) 0%, rgba(35, 36, 90, 0) 100%);
  //background: linear-gradient(to bottom, rgb(21, 23, 150) 0%, rgba(44, 45, 103, 0) 100%);
  padding: 0 32px;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}
.device-information {
  width: 694px;
  height: 66px;
  z-index: 10;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto;
}
</style>
