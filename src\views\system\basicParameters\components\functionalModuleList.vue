<template>
  <LACard shadow="nerver" title="物料名称">
    <div class="scroll-list">
      <div
        v-for="(item, index) in loadTable"
        :key="item.id"
        ref="loadRefs"
        :class="['load-name', { 'active-load': index === isActive }]"
        @click="handleClick(index)"
      >
        {{ item.name }}
      </div>
    </div>
  </LACard>
</template>

<script lang="tsx" setup>
/**
 * @file 系统管理-基础参数-矿卡载重左侧功能模块
 * <AUTHOR>
 * @date 2024/11/21
 */
import { ref } from "vue";
interface LoadTableItem {
  id: number;
  name: string;
  [key: string]: any;
}
const emit = defineEmits(["update:modelValue"]);
const props = defineProps<{
  loadTable: LoadTableItem[];
}>();

// 当前选中项
const isActive = ref(0);
const handleClick = index => {
  isActive.value = index;
  // console.log("数据=>", props.loadTable[index], "索引=》", index);
  emit("update:modelValue", props.loadTable[index]);
};
</script>

<style lang="scss" scoped>
.scroll-list {
  height: 100%;
  overflow: hidden;
  overflow-y: auto;
}
.el-card {
  height: 67vh;
  overflow-y: auto;
  width: 200px;
}
:deep(.el-card__header) {
  height: 50px;
  box-sizing: border-box;
  padding: 0;

  .card-header {
    padding: 5px 25px;
    font-weight: bold;
    height: 100%;
  }
}
.active-load {
  background: rgba(238, 243, 255, 1);
  color: rgba(53, 106, 253, 1) !important;
}
.load-name {
  color: #656666;
  height: 30px;
  line-height: 30px;
  border-radius: 4px;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 0 15px;
  overflow-y: auto;
  cursor: pointer;
  &:hover {
    background: rgba(238, 243, 255, 1);
    color: rgba(53, 106, 253, 1) !important;
  }
}
</style>
