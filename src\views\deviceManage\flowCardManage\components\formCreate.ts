/**
 * @file 设备管理-流量卡管理-表单创建文件
 * <AUTHOR>
 * @date 2024/11/18
 */
import { formMap } from "../types";
import LASelect from "@/components/LASelect";

import { getAllBulldozersList, getAllMineTrainList, getAllAncillaryCarList } from "@/api/modules/device";

export const flowCardMangeFormCreate = {
  type: "form-create",
  props: {
    option: {
      submitBtn: false,
      onSubmit(formData, api) {
        console.log("formData", formData);
        // 通知 table 搜索数据变化，刷新数据
        api.top.bus.$emit("searchFormChanged");
      }
    },
    rule: [
      {
        type: "input",
        field: formMap.get("流量卡编码"),
        title: "流量卡编码",
        validate: [{ required: true, message: "请输入流量卡编码" }]
      },
      {
        type: "input",
        field: formMap.get("ICCID"),
        title: "ICCID",
        validate: [{ required: true, message: "请输入ICCID" }]
      },

      // LASelect组件式
      {
        component: LASelect,
        field: formMap.get("所属设备"),
        name: "deviceId",
        props: {
          /**
           * <AUTHOR>
           * @date 2025/1/13
           * @description LASelect处理数据的方法
           * @param {Object} row 当前行的数据
           * @param {String} val 当前输入的值
           * @param {Array} list 当前下拉列表的数据
           * @param {Function} api form-create的api对象
           * @return {Array} 处理后的下拉列表数据
           */
          filterFetch: (row, val, list, api) => {
            // 过滤已经有cardName且row.id不是val的项
            const filteredList = list.filter((item: any) => {
              return item.id === val || !item.cardName;
            });
            return filteredList;
          },
          replaceFields: { key: "id", label: "name", value: "id" },
          fetch: (): any => {
            return Promise.all([getAllMineTrainList(), getAllBulldozersList(), getAllAncillaryCarList()]).then((res: any[]) => {
              // console.log(res);
              // 处理矿卡
              const mineTrainList = res[0].data;
              // 处理挖掘机
              const bulldozerList = res[1].data;
              // 处理辅助车辆
              const ancillaryCarList = res[2].data;
              // 合并数组
              const mergedList = [...mineTrainList, ...bulldozerList, ...ancillaryCarList];
              return mergedList;
            });
          }
          // isGroup模式下的显示数据结构
          // list: [
          //   {
          //     deviceName: "Popular cities",
          //     options: [
          //       {
          //         id: "123131",
          //         value: "Shanghai",
          //         deviceName: "Shanghai"
          //       },
          //       {
          //         id: "5656",
          //         value: "Beijing",
          //         deviceName: "Beijing"
          //       }
          //     ]
          //   },
          //   {
          //     deviceName: "City name",
          //     options: [
          //       {
          //         id: "786587",
          //
          //         value: "Chengdu",
          //         deviceName: "Chengdu"
          //       },
          //       {
          //         id: "2314124",
          //
          //         value: "Shenzhen",
          //         deviceName: "Shenzhen"
          //       },
          //       {
          //         id: "9769",
          //         value: "Guangzhou",
          //         deviceName: "Guangzhou"
          //       },
          //       {
          //         id: "63463",
          //
          //         value: "Dalian",
          //         deviceName: "Dalian"
          //       }
          //     ]
          //   }
          // ],
        },
        title: "所属设备"
      }
    ]
  }
};
