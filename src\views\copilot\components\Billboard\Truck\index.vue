<template>
  <div class="card-container">
    <div style="display: flex; align-items: center; gap: 4px; margin: auto">
      <SvgIcon
        name="IconTruck"
        icon-style="width:20px; height:20px"
        :color="selectedDeviceInfo?.rawData?.isActivateTheme && 'var(--el-color-primary)'"
      />
      <el-text size="large" tag="b">无人矿车-{{ selectedDeviceInfo?.rawData?.name }}</el-text>
    </div>
    <el-divider style="background-color: var(--el-bg-color); height: 2px; margin: 14px 0 0 0" />
    <TaskSteps style="height: 60px" :list="[selectedDeviceInfo?.detail?.task].filter(Boolean)" :show-name="false" />
    <div
      style="
        overflow: hidden;
        margin-top: 14px;
        padding-top: 2px;
        display: flex;
        background-color: var(--el-bg-color);
        gap: 2px;
        flex: 1;
      "
    >
      <!-- 矿卡信息 矿卡操作 -->
      <TruckTabs style="flex: 1" :data="selectedDeviceInfo?.detail?.mineTrain" />
      <!-- 实时故障 -->
      <TabsCard :tabs="[{ key: 'fault', title: '实时故障' }]" style="flex: 1">
        <template #fault>
          <div style="padding: 16px" v-if="faultMessages.length">
            <el-scrollbar style="font-size: 13px">
              <template v-for="item of faultMessages" :key="item.taskCode">
                <div class="message-box">
                  <div
                    style="
                      overflow: hidden;
                      white-space: nowrap;
                      text-overflow: ellipsis;
                      display: flex;
                      justify-content: space-between;
                      width: 100%;
                      gap: 10px;
                    "
                  >
                    <span>故障码：{{ item.taskCode }}</span>
                    <span>{{ formatTime(item.createDate) }}</span>
                  </div>
                  <span
                    style="width: 100%; overflow: hidden"
                    :style="{ color: item.level.toString() === '1' ? 'var(--el-color-error)' : 'var(--el-color-warning)' }"
                  >
                    <span>{{ item.level }}级故障</span>&nbsp;
                    <span>{{ item.content }}</span>
                  </span>
                </div>
                <el-divider style="margin: 0" />
              </template>
            </el-scrollbar>
          </div>
          <el-empty v-else image-size="0" style="height: 245px; background-color: var(--card-bg-color)" />
        </template>
      </TabsCard>
      <!-- 运行日志 -->
      <TabsCard :tabs="[{ key: 'log', title: '运行日志' }]" style="flex: 1">
        <template #log>
          <div style="padding: 16px" v-if="log.length">
            <el-scrollbar style="font-size: 13px">
              <template v-for="item of log" :key="item.taskCode">
                <div class="log-box">
                  <div style="flex: 1">{{ item.content }}</div>
                  <div>{{ formatTime(item.createDate) }}</div>
                </div>
                <el-divider />
              </template>
            </el-scrollbar>
          </div>
          <el-empty v-else image-size="0" style="height: 245px; background-color: var(--card-bg-color)" />
        </template>
      </TabsCard>
    </div>
  </div>
</template>
<script lang="ts" setup>
import TruckTabs from "./TruckTabs.vue";
import TabsCard from "../../TabsCard.vue";
import TaskSteps from "../TaskSteps.vue";
import dayjs from "dayjs";
import SvgIcon from "@/components/SvgIcon/index.vue";
import { useDeviceDispatchFaults, useDeviceDispatchLogs } from "@/views/copilot/request";
import { useDeviceSelection } from "@/views/copilot/store";
const { selectedDeviceInfo } = useDeviceSelection();
import { ref, computed } from "vue";
interface ErrorMessage {
  id: string;
  title: string;
  level: number;
  content: string;
  status: number;
  deviceId: string;
  deviceName: string;
  taskId: string;
  taskCode: string;
  createAccount: string | null;
  createDate: string;
  resolutionTime: string | null;
}
const { data } = useDeviceDispatchFaults<ErrorMessage>()!;
const { data: logData } = useDeviceDispatchLogs<ErrorMessage>()!;

// 实时故障
const faultMessages = computed(() => {
  if (!data.value) return [];
  const response = data.value;
  return response.records;
});
// 运行日志
const log = ref<any[]>([]);

// 格式化时间为 HH:mm:ss
const formatTime = (time: string) => {
  return dayjs(time).format("HH:mm:ss");
};
</script>

<style lang="scss" scoped>
.card-container {
  flex: 1;
  display: flex;
  height: 100%;
  flex-direction: column;
  box-sizing: border-box;
  padding: 16px;
}
.message-box,
.log-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  padding-bottom: 16px;
}
.log-box {
  align-items: flex-start;
  gap: 8px;
}
</style>
