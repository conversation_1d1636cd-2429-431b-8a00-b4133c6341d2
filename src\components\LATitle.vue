<template>
  <div :style="{ marginBottom: `${bottom}px` }" class="word">
    <div class="title decoration">
      {{ title }}
      <!--默认插槽-->
    </div>
    <slot></slot>
  </div>
</template>

<script lang="ts" setup>
import { defineProps } from "vue";
defineProps({
  title: {
    type: String,
    default: "标题"
  },
  bottom: {
    type: Number || String,
    default: 20
  }
});
</script>

<style lang="scss" scoped>
.word {
  width: 100%;
  display: flex;
  align-items: center;
}
.title {
  font-size: 16px;
  font-weight: bold;
  line-height: 100%;
  color: #1a1a1a;
  display: flex;
  align-items: center;
  gap: 4px;

  &.decoration::before {
    content: "";
    display: inline-block;
    width: 4px;
    height: 16px;
    background: rgba(53, 106, 253, 1);
    border-radius: 2px;
  }
}
</style>
