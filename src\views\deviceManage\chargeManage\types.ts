/**
 * @file 设备管理-充电桩管理-类型声明文件
 * <AUTHOR>
 * @date 2024/11/20
 */

// table字段声明
export const columnMap: any = new Map([
  ["编码", "code"],
  ["名称", "name"],
  ["状态", "status"],
  ["型号", "modelNumber"],
  ["1#充电接口编码", "interfaceCodeA"],
  ["2#充电接口编码", "interfaceCodeB"]
]);

// 新增/修改form字段声明
export const formMap = new Map([
  ["充电桩编码", "code"],
  ["充电桩名称", "name"],
  ["充电桩型号", "modelNumber"],
  ["1#充电接口编码", "interfaceCodeA"],
  ["2#充电接口编码", "interfaceCodeB"]
]);
// 充电桩状态枚举
export enum StatusEnum {
  /** 正常*/
  CHARGING_PILE_READY = "1000",
  /** 使用中*/
  CHARGING_PILE_BUSY = "1001",
  /** 故障中*/
  CHARGING_PILE_FAILURE = "1002",
  /** 测试中*/
  CHARGING_PILE_TEST = "1003",
  /** 离线中*/
  CHARGING_PILE_OFFLINE = "1004"
}

// 矿卡状态
export const ChargeStatusEnum = [
  {
    bg: "rgba(234, 240, 255)",
    status: StatusEnum.CHARGING_PILE_READY,
    color: "rgba(53, 106, 253, 1)",
    text: "正常"
  },
  {
    bg: "rgba(229, 251, 251)",
    status: StatusEnum.CHARGING_PILE_BUSY,
    color: "rgba(0, 209, 217, 1)",
    text: "充电中"
  },
  {
    bg: "rgba(255, 241, 236)",
    status: StatusEnum.CHARGING_PILE_FAILURE,
    color: "rgba(249, 116, 75, 1)",
    text: "故障中"
  },
  {
    bg: "rgba(229, 249, 244)",
    status: StatusEnum.CHARGING_PILE_TEST,
    color: "rgba(0, 194, 144, 1)",
    text: "测试中"
  },
  {
    bg: "rgba(239, 239, 239)",
    status: StatusEnum.CHARGING_PILE_OFFLINE,
    color: "rgba(101, 102, 102, 1)",
    text: "离线中"
  }
];
