<template>
  <vc-datasource-geojson
    v-if="osmGeojsonUrl"
    ref="datasourceRef"
    :data="osmGeojsonUrl"
    @ready="onDatasourceReady"
    :show="show"
    stroke="rgb(0 243 169)"
    fill="rgba(41, 177, 255, .5)"
    :marker-size="0"
    @mouseover="onMouseover"
    @mouseout="onMouseout"
    @mousedown="onMousedown"
  />

  <!-- 停车位地面标签 -->
  <vc-primitive-ground
    v-for="(label, index) in parkingLabels"
    :key="`parking-label-${index}`"
    :appearance="label.appearance"
    :asynchronous="false"
    :show="show"
    :classification-type="2"
  >
    <vc-geometry-instance>
      <vc-geometry-polygon :polygon-hierarchy="label.polygonCoords"></vc-geometry-polygon>
    </vc-geometry-instance>
  </vc-primitive-ground>

  <FreePointsLoader ref="freePointsLoaderRef" />
</template>

<script lang="ts" setup>
// lanelet2 地图加载
import { ref, watch } from "vue";
import { VcDatasourceGeojson, VcPrimitiveGround, VcGeometryInstance, VcGeometryPolygon, useVueCesium } from "vue-cesium";
import type { VcViewerProvider } from "vue-cesium/es/utils/types";
import { VcReadyObject } from "vue-cesium/es/utils/types";
import { useMapChangeChannel, useMapActionChannel } from "@/views/map/channel";
import { OsmConverter } from "@/views/map/utils/osmConverter";
import FreePointsLoader from "./FreePointsLoader.vue";
import { AddFreePoint } from "@/views/map/utils/addFreePoint";

// 创建贴地标签图像的函数
function createGroundLabelImage(
  text: string,
  angle: number = 0,
  options: {
    font?: string;
    padding?: number;
    minWidth?: number;
    minHeight?: number;
  } = {}
) {
  const { font = "bold 20px Arial", padding = 10, minWidth = 120, minHeight = 50 } = options;

  // 创建canvas元素
  const canvas = document.createElement("canvas");
  const ctx = canvas.getContext("2d");
  if (!ctx) return "";

  // 设置字体以测量文本尺寸
  ctx.font = font;
  const textMetrics = ctx.measureText(text);
  const textWidth = textMetrics.width;
  const textHeight = parseInt(font.match(/\d+/)?.[0] || "20");

  // 计算旋转后需要的canvas尺寸（考虑旋转后的边界框）
  const rotatedWidth = Math.abs(textWidth * Math.cos(angle)) + Math.abs(textHeight * Math.sin(angle));
  const rotatedHeight = Math.abs(textWidth * Math.sin(angle)) + Math.abs(textHeight * Math.cos(angle));

  const canvasWidth = Math.max(rotatedWidth + padding * 2, minWidth);
  const canvasHeight = Math.max(rotatedHeight + padding * 2, minHeight);

  canvas.width = canvasWidth;
  canvas.height = canvasHeight;

  // 重新设置字体（canvas尺寸改变后需要重新设置）
  ctx.font = font;
  ctx.textAlign = "center";
  ctx.textBaseline = "middle";

  // 设置透明背景
  ctx.clearRect(0, 0, canvasWidth, canvasHeight);

  // 移动到canvas中心并旋转
  ctx.save();
  ctx.translate(canvasWidth / 2, canvasHeight / 2);
  ctx.rotate(angle);

  // 绘制文本描边（增强可见性）
  ctx.strokeStyle = "rgba(0, 0, 0, 0.9)";
  ctx.lineWidth = 4;
  ctx.strokeText(text, 0, 0);

  // 绘制白色文本
  ctx.fillStyle = "#FFFFFF";
  ctx.fillText(text, 0, 0);

  ctx.restore();

  // 返回DataURL
  return canvas.toDataURL();
}

const props = withDefaults(defineProps<{ show?: boolean; zoomTo?: boolean }>(), { show: true, zoomTo: true });
const $vc: VcViewerProvider = useVueCesium();
const datasourceRef = ref();
const freePointsLoaderRef = ref();
// osm 转为 geojson之后的数据
const osmGeojsonUrl = ref();
// 停车位地面标签数据
const parkingLabels = ref<
  Array<{
    polygonCoords: number[][];
    appearance: any;
    id: string;
    text: string;
    isOccupied: boolean;
    angle: number;
  }>
>([]);
// 重置相机视角到全局OSM地图
let flyToOSM = () => {};
// Add new ref for AddFreePoint instance
const addFreePoint = ref<AddFreePoint>();
// 监听外部地图操作事件
watch(useMapActionChannel().data, newData => {
  switch (newData.type) {
    case "reset":
      flyToOSM();
      break;
    case "location":
      {
        const { active, removeLastPoint } = newData.data;
        if (active && !addFreePoint.value) {
          addFreePoint.value = new AddFreePoint($vc, point => {
            freePointsLoaderRef.value.addPoint(point);
          });
        }
        if (removeLastPoint) {
          freePointsLoaderRef.value.removeLastPoint();
        }
        addFreePoint.value?.setLocationActive(active);
      }
      break;
  }
});

watch(useMapChangeChannel().data, newData => {
  switch (newData.type) {
    // 地图改变时触发
    case "map-change":
      loadOsm(newData.data);
      break;
  }
});
loadOsm();
const onDatasourceReady = ({ viewer, cesiumObject }: VcReadyObject) => {
  flyToOSM = () => {
    viewer.flyTo(cesiumObject as Cesium.GeoJsonDataSource, {
      duration: 2
    });
  };
  setEntitiesColor((cesiumObject as Cesium.GeoJsonDataSource).entities.values);
  if (props.zoomTo) {
    viewer.zoomTo(cesiumObject as Cesium.GeoJsonDataSource);
  }

  // 演示动态更新地面标签（每5秒随机更新停车位状态）
  setInterval(() => {
    parkingLabels.value.forEach(label => {
      const newText = `P-${Math.floor(Math.random() * 999)
        .toString()
        .padStart(3, "0")}`;
      const newOccupiedStatus = !label.isOccupied;
      updateGroundLabel(label.id, newText, newOccupiedStatus);
    });
  }, 5000);
};
// 鼠标经过 geojson feature 时修改鼠标样式
const onMouseover = (event: any) => {
  addFreePoint.value?.handleMouseOver(event);
};
// 鼠标离开 geojson feature 时恢复鼠标样式
const onMouseout = () => {
  addFreePoint.value?.handleMouseOut();
};

// 鼠标按下时添加点位
const onMousedown = (event: any) => {
  addFreePoint.value?.handleMouseDown(event);
};

async function loadOsm(mapUrl: string = "/lanelet2_map.osm") {
  const osmContent = await OsmConverter.urlToContent(mapUrl);
  const { blobUrl } = OsmConverter.convert(osmContent);
  osmGeojsonUrl.value = blobUrl;
}

function setEntitiesColor(entities: any[]) {
  // todo 根据类型更改 polygon fill 颜色
  entities.forEach(entity => {
    const subtype = entity.properties.subtype?.getValue();
    const type = entity.properties.type?.getValue();
    // 算法组输出停车区为线段格式，转为多边形显示
    if (type === "parking_space") {
      // 计算多边形中心点
      if (entity.polygon?.hierarchy?.getValue()) {
        const positions: Cesium.Cartesian3[] = entity.polygon.hierarchy.getValue().positions;
        // 计算中心点
        let centerX = 0,
          centerY = 0,
          centerZ = 0;
        positions.forEach((pos: Cesium.Cartesian3) => {
          centerX += pos.x;
          centerY += pos.y;
          centerZ += pos.z;
        });
        centerX /= positions.length;
        centerY /= positions.length;
        centerZ /= positions.length;

        // 设置实体位置为多边形中心点
        entity.position = new Cesium.ConstantPositionProperty(new Cesium.Cartesian3(centerX, centerY, centerZ));

        // 创建地面标签
        const labelText = `P-${Math.floor(Math.random() * 999)
          .toString()
          .padStart(3, "0")}`;
        const isOccupied = Math.random() > 0.5; // 随机占用状态

        // 计算停车位角度
        const parkingAngle = calculateParkingAngle(positions);

        // 创建地面标签
        const groundLabel = createGroundLabel(
          new Cesium.Cartesian3(centerX, centerY, centerZ),
          labelText,
          isOccupied,
          { width: 2.5, height: 1.2 }, // 标签尺寸（米）- 更符合停车位标识大小
          parkingAngle
        );

        // 添加到标签数组
        parkingLabels.value.push(groundLabel);
      }

      if (entity.polygon) {
        entity.polygon.show = true;
        entity.polygon.height = 0;
        entity.polygon.extrudedHeight = 0;
      }
    }
    if (subtype && entity.polygon) {
      // 根据字符串 16进制后创建一个颜色
      // entity.polygon.material = Cesium.Color.fromCssColorString("rgba(41, 177, 255, .5)");
      // console.log("entity", entity);
      if (entity.properties.type === "parking_space") {
        // console.log("entity", entity);
      }
    }
  });
}

// 计算停车位角度的函数
function calculateParkingAngle(positions: Cesium.Cartesian3[]): number {
  if (positions.length < 4) {
    return 0;
  }

  // 去除重复的闭合点，只取前4个点
  const validPositions = positions.slice(0, 4);

  // 将世界坐标转换为经纬度
  const corners = validPositions.map((pos, index) => {
    const cartographic = Cesium.Cartographic.fromCartesian(pos);
    return cartographic;
  });

  // 计算四条边的长度和角度
  const edges: Array<{
    length: number;
    angle: number;
    angleDegrees: number;
    deltaLng: number;
    deltaLat: number;
    edgeIndex: number;
  }> = [];

  for (let i = 0; i < 4; i++) {
    const start = corners[i];
    const end = corners[(i + 1) % 4];

    const deltaLng = Cesium.Math.toDegrees(end.longitude) - Cesium.Math.toDegrees(start.longitude);
    const deltaLat = Cesium.Math.toDegrees(end.latitude) - Cesium.Math.toDegrees(start.latitude);
    const length = Math.sqrt(deltaLng * deltaLng + deltaLat * deltaLat);
    const angle = Math.atan2(deltaLat, deltaLng);
    const angleDegrees = Cesium.Math.toDegrees(angle);

    const edge = {
      length,
      angle,
      angleDegrees,
      deltaLng,
      deltaLat,
      edgeIndex: i
    };

    edges.push(edge);
  }

  // 找到最长的边（停车位长边）
  const longestEdge = edges.reduce((prev, current) => (current.length > prev.length ? current : prev));

  // 对于停车位，我们可能需要调整角度以确保文字方向正确
  // 如果角度超过90度或小于-90度，我们调整它使文字不会倒置
  let adjustedAngle = longestEdge.angle;
  if (adjustedAngle > Math.PI / 2) {
    adjustedAngle -= Math.PI;
  } else if (adjustedAngle < -Math.PI / 2) {
    adjustedAngle += Math.PI;
  }
  // 返回调整后的角度
  return adjustedAngle + 0.5;
}

// 创建旋转的矩形坐标
function createRotatedRectangle(centerLng: number, centerLat: number, width: number, height: number, angle: number): number[][] {
  // 地球半径相关的转换
  const EARTH_RADIUS = 6378137;
  const metersPerLngDegree = (2 * Math.PI * EARTH_RADIUS * Math.cos((centerLat * Math.PI) / 180)) / 360;
  const metersPerLatDegree = 111319.5;

  // 转换为度
  const halfWidthDeg = width / 2 / metersPerLngDegree;
  const halfHeightDeg = height / 2 / metersPerLatDegree;

  // 计算旋转后的四个角点
  const cos = Math.cos(angle);
  const sin = Math.sin(angle);

  const corners = [
    [-halfWidthDeg, -halfHeightDeg],
    [halfWidthDeg, -halfHeightDeg],
    [halfWidthDeg, halfHeightDeg],
    [-halfWidthDeg, halfHeightDeg]
  ];

  const rotatedCorners = corners.map(([x, y]) => [centerLng + (x * cos - y * sin), centerLat + (x * sin + y * cos)]);

  // 添加第一个点以闭合多边形
  rotatedCorners.push([...rotatedCorners[0]]);

  return rotatedCorners;
}

// 创建地面标签的函数
function createGroundLabel(
  centerPosition: Cesium.Cartesian3,
  text: string,
  isOccupied: boolean = false,
  labelSize: { width: number; height: number },
  angle: number = 0
) {
  // 将世界坐标转换为经纬度
  const cartographic = Cesium.Cartographic.fromCartesian(centerPosition);
  const longitude = Cesium.Math.toDegrees(cartographic.longitude);
  const latitude = Cesium.Math.toDegrees(cartographic.latitude);

  // 创建不旋转的矩形坐标（标签区域保持水平，只旋转文字）
  const polygonCoords = createRotatedRectangle(longitude, latitude, labelSize.width, labelSize.height, 0);

  // 生成标签图像（只显示白色文字，无背景）
  const labelImage = createGroundLabelImage(text, angle, {
    font: "bold 20px Arial",
    padding: 4,
    minWidth: 100,
    minHeight: 40
  });

  // 创建材质外观，提高层级
  const appearance = new Cesium.MaterialAppearance({
    material: new Cesium.Material({
      fabric: {
        type: "Image",
        uniforms: {
          image: labelImage
        }
      }
    }),
    faceForward: false,
    translucent: true
  });

  return {
    polygonCoords,
    appearance,
    id: `parking-${text}`,
    text,
    isOccupied,
    angle
  };
}

// 更新地面标签的函数
function updateGroundLabel(labelId: string, newText: string, isOccupied: boolean = false) {
  const labelIndex = parkingLabels.value.findIndex(label => label.id === labelId);
  if (labelIndex !== -1) {
    const label = parkingLabels.value[labelIndex];

    // 重新生成图像（只显示白色文字，无背景）
    const newImage = createGroundLabelImage(newText, label.angle, {
      font: "bold 20px Arial",
      padding: 4,
      minWidth: 100,
      minHeight: 40
    });

    // 更新材质
    label.appearance.material.uniforms.image = newImage;
    label.text = newText;
    label.isOccupied = isOccupied;
  }
}
</script>
