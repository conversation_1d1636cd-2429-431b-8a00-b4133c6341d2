<template>
  <form-create v-model:api="fApi" :option="option" :rule="rule"></form-create>
</template>

<script lang="ts" setup>
/**
 * @file 设备管理-地磅管理
 * <AUTHOR>
 * @date 2025/7/22
 */
import { ref } from "vue";
import { columnMap } from "./types";
import { weighbridgeMachineFormCreate } from "./components/formCreate.tsx";
import formCreate from "@form-create/element-ui";
import { deleteBulldozers, getWeighbridgeList, saveWeighbridge, deleteWeighbridge } from "@/api/modules/device";

const fApi = ref();
const option = {
  form: { inline: true },
  resetBtn: false,
  submitBtn: false
};

const rule = ref([
  {
    type: "SearchFormOperation",
    field: "v:search",
    wrap: { style: "marginBottom: 0" },
    children: [
      {
        type: "input",
        field: "search",
        props: {
          size: "default",
          placeholder: "地磅名称/编码"
        }
      },
      // 新增
      {
        type: "AddBtn",
        slot: "suffix",
        props: {
          btn: { content: "新增地磅", auth: "add" },
          dialog: {
            title: "新增地磅"
          },

          size: "default",
          submitRequest: params => {
            return saveWeighbridge({
              ...params,
              crushingStationList: params.crushingStationList.join(",")
            });
          }
        },
        children: [weighbridgeMachineFormCreate]
      }
    ]
  },
  {
    type: "ProTable",
    props: {
      columns: [
        {
          prop: columnMap.get("名称"),
          label: "名称"
        },
        {
          prop: columnMap.get("编码"),
          label: "编码"
        },

        {
          prop: columnMap.get("绑定破碎站"),
          label: "绑定破碎站"
        },
        {
          prop: columnMap.get("最大装载率"),
          label: "最大装载率(t/h)"
        },
        {
          prop: columnMap.get("经度"),
          label: "经度"
        },
        {
          prop: columnMap.get("纬度"),
          label: "纬度"
        },
        {
          prop: columnMap.get("海拔"),
          label: "海拔"
        },
        {
          prop: columnMap.get("朝向"),
          label: "朝向"
        },
        {
          prop: columnMap.get("osmID"),
          label: "osmID"
        },
        { prop: "operation", label: "操作", fixed: "right" }
      ],
      fetch: getWeighbridgeList,
      operations: row => {
        // 如果是启用状态则没启用按钮,如果是停用状态则没停用按钮
        const op = [
          { content: "修改", action: "edit", auth: "update" },
          { content: "停用", action: "stop", auth: "stop" },
          { content: "启用", action: "start", auth: "start" },
          { content: "删除", action: "delete", auth: "delete", props: { style: { color: "rgba(242, 85, 85, 1)" } } }
        ];
        // 停用状态
        if (row.status === 1) {
          op.splice(1, 1);
        }
        // 启用状态
        if (row.status === 3) {
          op.splice(2, 1);
        }

        return op;
      }
    },

    children: [
      // 修改
      {
        type: "EditBtn",
        props: {
          action: "edit",
          dialog: {
            title: "修改地磅"
          },
          formatter: (data: any): object => {
            return {
              id: data.id,
              machineName: data.machineName,
              machineCode: data.machineCode,
              maxCapacity: data.maxCapacity,
              longitude: data.longitude,
              ele: data.ele,
              latitude: data.latitude,
              orientations: data.orientations,
              osmId: data.osmId,
              crushingStationList: data.crushingStationList.length ? data.crushingStationList.map(item => item.id).split(",") : []
            };
          },
          submitRequest: params => {
            // crushingStationList字段改为,分隔
            return saveWeighbridge({
              ...params,
              crushingStationList: params.crushingStationList.join(",")
            });
          }
        },
        children: [weighbridgeMachineFormCreate]
      },
      // 停用
      {
        type: "ConfirmDialog",
        on: {
          // 监听弹窗组件抛出的的afterSubmit事件，用于刷新页面
          afterSubmit: () => {
            // 刷新，调用组件内部请求方法
            fApi.value.exec("v:search", "onSearch");
          }
        },
        props: {
          action: "stop",
          subtitle: row => {
            return row.code;
          },
          title: "是否停用地磅",
          message: "停用后,本地磅不可使用",
          // 模拟请求param：参数
          submitRequest: deleteBulldozers
        }
      },
      // 启用
      {
        type: "ConfirmDialog",
        on: {
          // 监听弹窗组件抛出的的afterSubmit事件，用于刷新页面
          afterSubmit: () => {
            // 刷新，调用组件内部请求方法
            fApi.value.exec("v:search", "onSearch");
          }
        },
        props: {
          action: "start",
          subtitle: row => {
            return row.code;
          },
          title: "是否启用地磅",
          message: "启用后,本地磅可正常使用",
          // 模拟请求param：参数
          submitRequest: deleteBulldozers
        }
      },
      // 删除
      {
        type: "ConfirmDialog",
        on: {
          // 监听弹窗组件抛出的的afterSubmit事件，用于刷新页面
          afterSubmit: () => {
            // 刷新，调用组件内部请求方法
            fApi.value.exec("v:search", "onSearch");
          }
        },
        props: {
          action: "delete",
          subtitle: row => {
            return row.code;
          },
          title: "是否删除地磅",
          message: "删除后不可恢复",
          // 模拟请求param：参数
          submitRequest: deleteWeighbridge
        }
      }
    ]
  }
]);
</script>
<style lang="scss" scoped>
:deep(.el-row) {
  height: var(--page-height);
}

.el-form-item {
  margin-right: 4px !important;
}
</style>
