<template>
  <form-create v-model:api="fApi" :option="option" :rule="rule"></form-create>
</template>
<script lang="tsx" setup>
/**
 * @file 设备管理-破碎站管理
 * <AUTHOR>
 * @date 2024/11/25
 */
import { ref } from "vue";

import { columnMap } from "./types";
import { crushingStationFormCreate } from "./components/formCreate";
import formCreate from "@form-create/element-ui";
import { getCrusherList, saveCrusher, deleteCrusher } from "@/api/modules/device";

const fApi = ref();
const option = {
  form: { inline: true },
  resetBtn: false,
  submitBtn: false
};

const rule = ref([
  {
    type: "SearchFormOperation",
    field: "v:search",
    wrap: { style: "marginBottom: 0" },
    children: [
      {
        type: "input",
        field: "search",
        props: {
          size: "default",
          placeholder: "破碎站名称/编码"
        }
      },
      // 新增
      {
        type: "AddBtn",
        slot: "suffix",
        props: {
          btn: { content: "新增破碎站", auth: "add" },
          dialog: {
            title: "新增破碎站", // 绑定到弹窗根节点的样式
            class: "dialog-custom-width"
          },
          size: "default",
          submitRequest: saveCrusher
        },
        children: [crushingStationFormCreate]
      }
    ]
  },
  {
    type: "ProTable",
    props: {
      columns: [
        {
          prop: columnMap.get("名称"),
          label: "名称"
        },
        {
          prop: columnMap.get("编码"),
          label: "编码"
        },
        {
          prop: columnMap.get("允许卸矿最低安全深度(米)"),
          label: "允许卸矿最低安全深度(米)",
          width: "220px"
        },
        {
          prop: columnMap.get("最大喂矿率(t/h)"),
          label: "最大喂矿率(t/h)"
        },
        {
          prop: columnMap.get("经度"),
          label: "经度"
        },
        {
          prop: columnMap.get("纬度"),
          label: "纬度"
        },
        {
          prop: columnMap.get("海拔"),
          label: "海拔(米)"
        },
        {
          prop: columnMap.get("朝向"),
          label: "朝向"
        },

        { prop: "operation", label: "操作", fixed: "right" }
      ],
      fetch: getCrusherList,
      operations: [
        { content: "修改", action: "edit", auth: "update" },
        { content: "删除", action: "delete", auth: "delete", props: { style: { color: "rgba(242, 85, 85, 1)" } } }
      ]
    },
    children: [
      // 修改
      {
        type: "EditBtn",
        props: {
          action: "edit",
          dialog: {
            title: "修改破碎站",
            // 绑定到弹窗根节点的样式
            class: "dialog-custom-width"
          },
          submitRequest: saveCrusher
        },
        children: [crushingStationFormCreate]
      },
      // 删除
      {
        type: "ConfirmDialog",
        on: {
          // 监听弹窗组件抛出的的afterSubmit事件，用于刷新页面
          afterSubmit: () => {
            // 刷新，调用组件内部请求方法
            fApi.value.exec("v:search", "onSearch");
          }
        },
        props: {
          action: "delete",
          subtitle: row => {
            return row.code;
          },
          title: "是否删除破碎站",
          message: "删除后不可恢复",
          // 模拟请求param：参数
          submitRequest: deleteCrusher
        }
      }
    ]
  }
]);
</script>
<style lang="scss" scoped>
:deep(.el-row) {
  height: calc(100vh - 138px) !important;
}

.el-form-item {
  margin-right: 4px !important;
}
</style>
