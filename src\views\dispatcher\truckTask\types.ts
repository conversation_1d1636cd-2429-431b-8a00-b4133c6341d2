/**
 * @file 调度管理-矿卡任务管理-类型声明文件
 * <AUTHOR>
 * @date 2025/2/5
 */

// table字段声明
export const columnMap: any = new Map([
  ["矿车名称", "name"],
  ["任务类型", "code"],
  ["状态", "status"],
  ["起点", "lon"],
  ["终点", "lat"],
  ["开始时间", "ele"],
  ["完成时间", "orientations"]
]);
// 矿卡任务状态枚举
export enum StatusEnum {
  /** 空闲中*/
  TRAIN_READY = 1000,
  /** 运行中*/
  TRAIN_BUSY = 1001,
  /** 充电中*/
  TRAIN_CHARGING = 1002,
  /** 故障中*/
  TRAIN_FAILURE = 1003,
  /** 离线中*/
  TRAIN_OFFLINE = 1006
}
// 矿卡任务详情枚举
export const TaskTypeMap = new Map([
  [StatusEnum.TRAIN_READY, "空闲中"],
  [StatusEnum.TRAIN_BUSY, "运行中"],
  [StatusEnum.TRAIN_CHARGING, "充电中"],
  [StatusEnum.TRAIN_FAILURE, "故障中"],
  [StatusEnum.TRAIN_OFFLINE, "离线中"]
]);
export const TaskDetailType = {
  [StatusEnum.TRAIN_OFFLINE]: {
    text: "离线中",
    color: "rgba(53, 106, 253, 1)"
  },
  [StatusEnum.TRAIN_FAILURE]: {
    text: "故障中",
    color: "rgb(255,5,51)"
  }
};
// 矿卡任务状态
export const truckTaskStatusEnum = [
  {
    bg: "rgba(234, 240, 255)",
    status: StatusEnum.TRAIN_READY,
    color: "rgba(53, 106, 253, 1)",
    text: "空闲中"
  },
  {
    bg: "rgba(229, 249, 244)",
    status: StatusEnum.TRAIN_BUSY,
    color: "rgba(0, 194, 144, 1)",
    text: "运行中"
  },
  {
    bg: "rgba(229, 251, 251)",
    status: StatusEnum.TRAIN_CHARGING,
    color: "rgba(0, 209, 217, 1)",
    text: "充电中"
  },
  {
    bg: "rgba(255, 241, 236)",
    status: StatusEnum.TRAIN_FAILURE,
    color: "rgba(249, 116, 75, 1)",
    text: "故障中"
  },
  {
    bg: "rgba(239, 239, 239)",
    status: StatusEnum.TRAIN_OFFLINE,
    color: "rgba(101, 102, 102, 1)",
    text: "离线中"
  }
];
