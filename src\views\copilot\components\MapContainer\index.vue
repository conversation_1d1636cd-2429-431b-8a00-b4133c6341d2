<template>
  <div style="margin-top: 16px">
    <el-text size="large" tag="b" style="align-self: flex-start">实时地图</el-text>
    <div class="card-container" ref="containerRef">
      <ActionPanel :container="containerRef!" />
      <MessagePanel />
      <DeviceInformation v-if="selectedDeviceId && selectedDeviceType === 'deviceMineTrain'" />
      <Map @ready="onReady" @osm-load-error="onOSMLoadError" />
      <div class="default" v-if="!mapLoaded && !mapError">map</div>
      <div class="default error" v-if="mapError">无可用地图</div> 
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import ActionPanel from "./ActionPanel.vue";
import MessagePanel from "./MessagePanel.vue";
import DeviceInformation from "./DeviceInformation.vue";
import { useDeviceSelection } from "@/views/copilot/request";
import Map from "@/views/map/index.vue";
const containerRef = ref<HTMLElement | null>(null);
const mapLoaded = ref(false);
const mapError = ref(false);
const { selectedDeviceId, selectedDeviceType } = useDeviceSelection();
const onReady = () => {
  mapLoaded.value = true;
};

const onOSMLoadError = () => {
  mapError.value = true;
};
</script>

<style lang="scss" scoped>
.card-container {
  width: 100%;
  height: 563px;
  position: relative;
  box-sizing: border-box;
  padding: 8px;
  #map-root {
    width: 100%;
    height: 100%;
  }
  .default {
    position: absolute;
    left: 0;
    top: 0;
    width: calc(100% - 16px);
    height: calc(100% - 16px);
    border: 1px solid #040536;
    margin: 8px;
    background: #23245a;
    font-weight: bold;
    font-size: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--el-color-primary);
    &.error {
      font-size: 14px;
      color: #747595;
    }
  }
}
</style>
