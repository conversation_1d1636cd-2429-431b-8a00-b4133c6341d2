/**
 * @file 维修保养-保养工单-详情文件
 * <AUTHOR>
 * @date 2025/2/18
 */
import LASelect from "@/components/LASelect.tsx";
import { getAllUserList } from "@/api/modules";
import { assignFormMap, maintenanceResultFormMap, transferFormMap } from "@/views/maintenance/maintenanceOrder/types.ts";

// 转让工单
export const transferFormCreate = {
  type: "form-create",
  name: "transferFormCreate",
  props: {
    option: {
      submitBtn: false,
      onSubmit(formData, api) {
        api.top.bus.$emit("searchFormChanged");
      }
    },
    rule: [
      {
        component: LASelect,
        field: transferFormMap.get("保养负责人"),
        props: {
          replaceFields: { key: "id", label: "employeeName", value: "id" },
          fetch: getAllUserList,
          onChange: (value, data, formCreateInject) => {
            formCreateInject.api.setValue(transferFormMap.get("保养负责人名称"), data.employeeName);
          }
        },
        title: "保养负责人",
        validate: [{ required: true, message: "请选择保养负责人" }]
      }
    ]
  }
};
// 派工
export const dispatchWorkFormCreate = {
  type: "form-create",
  name: "dispatchWorkFormCreate",
  props: {
    option: {
      submitBtn: false,
      onSubmit(formData, api) {
        api.top.bus.$emit("searchFormChanged");
      }
    },
    rule: [
      {
        component: LASelect,
        field: assignFormMap.get("派工负责人"),
        props: {
          replaceFields: { key: "id", label: "employeeName", value: "id" },
          fetch: getAllUserList,
          onChange: (value, data, formCreateInject) => {
            formCreateInject.api.setValue(assignFormMap.get("派工负责人名称"), data.employeeName);
          }
        },
        title: "保养负责人",

        validate: [{ required: true, message: "请选择保养负责人" }]
      }
    ]
  }
};
// 填写保养结果
export const repairResultFormCreate = {
  type: "form-create",
  props: {
    option: {
      submitBtn: false,
      onSubmit(formData, api) {
        api.top.bus.$emit("searchFormChanged");
      }
    },
    rule: [
      {
        type: "input",
        title: "保养备注",
        props: {
          type: "textarea"
        },
        field: maintenanceResultFormMap.get("保养备注"),
        validate: [{ required: true, message: "请输入保养备注" }]
      }
    ]
  }
};
