/**
 * 设备显示数据组合器
 * 结合新的设备管理器和原有API数据，提供统一的显示数据
 * 遵循YAGNI原则，只实现当前需要的功能
 */

import { computed, watchEffect } from "vue";
import { useDeviceData } from "./useDeviceData";
import { useDeviceWingSection } from "@/views/copilot/request";
import { transformDeviceToDisplay } from "@/utils/deviceTransform";
import type { DeviceSpaceData } from "@/utils/deviceTransform";

// 设备状态数据接口
interface DeviceStatusData {
  value: number;
  label: string;
  key: string;
  color: string;
}

// 设备行数据接口
interface DeviceRowSpace {
  type: "device-space";
  height: number;
  data: DeviceSpaceData[];
}

// 状态行数据接口
interface DeviceStatusRowSpace {
  type: "status";
  height: number;
  data: DeviceStatusData[];
}

// 设备空间接口
interface DeviceSpace {
  title: string;
  rows: (DeviceStatusRowSpace | DeviceRowSpace)[];
}

/**
 * 设备显示数据hook
 */
export const useDeviceDisplay = () => {
  // 获取新的设备数据
  const { mineTrains, bulldozers, chargingPiles, crushingStations, cockpits, statistics, initFromAPI } = useDeviceData();

  // 获取原有API数据作为备用
  const { data: apiData } = useDeviceWingSection();

  // 当API数据更新时，同步到设备管理器
  // 使用watchEffect确保响应式更新
  watchEffect(() => {
    if (apiData.value) {
      initFromAPI(apiData.value);
    }
  });

  /**
   * 转换设备数据为显示格式
   */
  const mappedDevices = computed<DeviceSpace[]>(() => {
    // 如果新的设备管理器中没有数据，返回空数组
    const allDevices = [
      ...mineTrains.value,
      ...bulldozers.value,
      ...chargingPiles.value,
      ...crushingStations.value,
      ...cockpits.value
    ];

    if (allDevices.length === 0) {
      return [];
    }

    // 转换设备数据
    const transformedMineTrains = mineTrains.value.map(transformDeviceToDisplay);
    const transformedBulldozers = bulldozers.value.map(transformDeviceToDisplay);
    const transformedChargingPiles = chargingPiles.value.map(transformDeviceToDisplay);
    const transformedCrushingStations = crushingStations.value.map(transformDeviceToDisplay);
    const transformedCockpits = cockpits.value.map(transformDeviceToDisplay);

    return [
      {
        title: "无人矿卡",
        rows: [
          {
            type: "status",
            height: 37,
            data: [
              {
                value: statistics.value.attendanceNumber || 0,
                label: "出勤",
                key: "work",
                color: "var(--el-color-secondary)"
              },
              {
                value: statistics.value.leisureNumber || 0,
                label: "空闲",
                key: "idle",
                color: "var(--el-color-primary)"
              },
              {
                value: statistics.value.rechargeBatteriesNumber || 0,
                label: "充电",
                key: "charging",
                color: "var(--el-color-success)"
              },
              {
                value: statistics.value.faultsNumber || 0,
                label: "故障",
                key: "error",
                color: "var(--el-color-danger)"
              }
            ]
          },
          {
            type: "device-space",
            height: 320,
            data: transformedMineTrains
          }
        ]
      },
      {
        title: "充电桩",
        rows: [
          {
            type: "device-space",
            height: 156,
            data: transformedChargingPiles
          }
        ]
      },
      {
        title: "挖机",
        rows: [
          {
            type: "device-space",
            height: 156,
            data: transformedBulldozers
          }
        ]
      },
      {
        title: "破碎站",
        rows: [
          {
            type: "device-space",
            height: 74,
            data: transformedCrushingStations
          }
        ]
      },
      {
        title: "驾驶舱",
        rows: [
          {
            type: "device-space",
            height: 74,
            data: transformedCockpits
          }
        ]
      }
    ];
  });

  return {
    mappedDevices,
    // 暴露原始数据供调试使用
    rawDeviceData: {
      mineTrains,
      bulldozers,
      chargingPiles,
      crushingStations,
      cockpits,
      statistics
    }
  };
};
