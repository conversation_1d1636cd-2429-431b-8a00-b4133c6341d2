/**
 * 设备工具函数
 * 提供可复用的设备数据处理方法
 * 遵循DRY原则，避免重复代码
 */

import type { Device, DeviceType } from '@/stores/deviceTypes'

/**
 * 安全地合并设备数据
 * 确保类型安全和数据完整性
 */
export const mergeDeviceData = <T extends Device>(
  existingDevice: T | undefined,
  newData: Partial<T>,
  deviceType: DeviceType
): T => {
  if (existingDevice) {
    // 合并现有设备数据
    return {
      ...existingDevice,
      ...newData,
      deviceType // 确保设备类型不被覆盖
    } as T
  } else {
    // 创建新设备，提供默认值
    return {
      id: '',
      name: '',
      code: '',
      status: 0,
      deviceType,
      ...newData
    } as T
  }
}

/**
 * 验证设备数据的完整性
 */
export const validateDeviceData = (device: Partial<Device>): boolean => {
  return !!(device.code && device.deviceType)
}

/**
 * 生成设备的唯一标识符
 */
export const getDeviceKey = (device: Device | { code: string }): string => {
  return device.code
}

/**
 * 检查设备是否在线
 */
export const isDeviceOnline = (device: Device): boolean => {
  // 根据不同设备类型的离线状态判断
  switch (device.deviceType) {
    case 'mineTrain':
      return device.status !== 1006 // TRAIN_OFFLINE
    case 'bulldozer':
      return device.status !== 0 // OFFLINE
    case 'chargingPile':
      return device.status !== 0 // CHARGING_PILE_OFFLINE
    case 'crushingStation':
      return device.status !== 0 // OFFLINE
    case 'cockpit':
      return device.status !== 0 // OFFLINE
    default:
      return device.status !== 0
  }
}

/**
 * 检查设备是否有故障
 */
export const isDeviceFaulty = (device: Device): boolean => {
  switch (device.deviceType) {
    case 'mineTrain':
      const mineTrainDevice = device as any
      return mineTrainDevice.errorFlag === true || 
             mineTrainDevice.errorFlag === 1 || 
             mineTrainDevice.errorFlag?.toString() === "1" ||
             device.status === 1003 // TRAIN_FAILURE
    case 'bulldozer':
      return device.status === -1 // FAULTS
    default:
      return false
  }
}

/**
 * 获取设备的显示名称
 */
export const getDeviceDisplayName = (device: Device): string => {
  return device.name || device.code || 'Unknown Device'
}

/**
 * 批量处理设备数据
 */
export const processDeviceList = <T extends Device>(
  devices: Array<Partial<T>>,
  deviceType: DeviceType,
  existingDevices: Map<string, Device> = new Map()
): Map<string, T> => {
  const result = new Map<string, T>()
  
  devices.forEach(deviceData => {
    if (validateDeviceData(deviceData) && deviceData.code) {
      const existingDevice = existingDevices.get(deviceData.code) as T | undefined
      const mergedDevice = mergeDeviceData(existingDevice, deviceData, deviceType)
      result.set(deviceData.code, mergedDevice)
    }
  })
  
  return result
}

/**
 * 设备数据变化检测
 */
export const hasDeviceChanged = (oldDevice: Device, newDevice: Partial<Device>): boolean => {
  // 检查关键字段是否发生变化
  const keyFields = ['status', 'name', 'electricQuantity', 'errorFlag', 'taskCurrentStep']
  
  return keyFields.some(field => {
    const oldValue = (oldDevice as any)[field]
    const newValue = (newDevice as any)[field]
    return oldValue !== newValue
  })
}

/**
 * 日志记录工具
 */
export const logDeviceUpdate = (action: string, deviceCode: string, data?: any) => {
  console.log(`[DeviceUtils] ${action} - Device: ${deviceCode}`, data)
}
