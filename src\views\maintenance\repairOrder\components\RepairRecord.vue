<template>
  <div v-if="repairRecord.length > 0" class="repair-record">
    <LATitle :bottom="0" title="维修记录" />
    <div v-for="data in repairRecord" :key="data.id" class="repair-record-content">
      <div class="record-content">
        <div class="user-name">{{ data.repairResponsiblePersonName }}</div>
        <div class="describe">{{ repairResults[data.repairResults] }}</div>
        <div class="describe">{{ dayjs(data.createDate).format("YYYY-MM-DD HH:mm:ss") }}</div>
      </div>
      <div v-if="['repaired', 'unRepaired'].includes(data.repairResults)" class="record-content">
        <div class="describe">
          <span class="title">{{ repairContent[data.repairResults] }}:</span>
          {{ repairedResultInfo.get(data.repairResults as RepairStatusEnum) }}
        </div>
      </div>
      <div v-if="['repaired', 'unRepaired', 'closed'].includes(data.repairResults)" class="record-content">
        <div class="describe">
          <span class="title">维修备注:</span>
          {{ data.repairContent }}
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
/**
 * @file 维修保养-维修工单-详情-维修记录组件
 * <AUTHOR>
 * @date 2025/2/11
 */

import LATitle from "@/components/LATitle.vue";
import { PropType, watch } from "vue";
import dayjs from "dayjs";
import { repairedResultInfo, RepairStatusEnum } from "@/views/maintenance/repairOrder/types.ts";
interface RepairRecord {
  id: number;
  repairResponsiblePersonName: string;
  repairResults: string;
  createDate: string;
  repairContent: string;
}
const props = defineProps({
  // 接收数据
  repairRecord: {
    type: Array as PropType<RepairRecord[]>,
    default: () => []
  }
});
// 维修结果/维修备注的字段
const repairContent = {
  repaired: "维修结果",
  unRepaired: "维修结果",
  closed: "维修备注"
};
// 工单操作描述（repairResults的三种操作描述："transfer"：'转让了工单'，"unRepaired"\"repaired":'填写了维修结果'，"closed":'关闭了工单'）
const repairResults = {
  transfer: "转让了工单",
  unRepaired: "填写了维修结果",
  repaired: "填写了维修结果",
  closed: "关闭了工单"
};
</script>

<style scoped>
.title,
.user-name {
  font-weight: bold;
  color: #1a1a1a;
}
div {
  line-height: 100%;
}
.repair-record {
  font-size: 14px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid var(--el-border-color-lighter);

  .repair-record-content {
    border-bottom: 1px solid var(--el-border-color-lighter);
    padding-bottom: 20px;
    margin-top: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
    &:last-child {
      border-bottom: none;
    }
  }
  .record-content {
    display: flex;
    gap: 10px;
  }
  .describe {
    color: rgba(101, 102, 102, 1);
    display: flex;
    gap: 10px;
  }
}
</style>
