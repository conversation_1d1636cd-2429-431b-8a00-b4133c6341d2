import formCreate from "@form-create/element-ui";
import MaintenanceMethodDetail from "@/views/maintenance/maintenanceOrder/components/MaintenanceMethodDetail.vue";
import Detail from "./Detail.vue";

formCreate.component("Detail", Detail);
formCreate.component("MaintenanceMethodDetail", MaintenanceMethodDetail);
export const detailFormCreate = {
  type: "form-create",
  props: {
    rule: [
      {
        type: "Detail"
      }
    ],
    option: {
      submitBtn: false
    }
  }
};
export const MaintenanceMethodDetailFormCreate = {
  type: "form-create",
  props: {
    rule: [
      {
        type: "MaintenanceMethodDetail"
      }
    ],
    option: {
      submitBtn: false
    }
  }
};
