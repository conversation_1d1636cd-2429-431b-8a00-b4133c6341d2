<template>
  <el-tag v-if="tag.style" :style="tag.style" type="info">{{ tag.text }}</el-tag>
  <span v-else>{{ tag.text }}</span>
</template>

<script lang="tsx" setup>
/**
 * @file 显示状态tag标签
 * <AUTHOR>
 * @date 2024/11/6
 */
import { computed } from "vue";

const props = defineProps<{
  enum: {
    color?: string;
    bg?: string;
    text: string;
    [key: string]: any;
  }[];
  prop: string;
  value: string | number;
}>();

const tag = computed(() => {
  const item = props.enum.find(item => item[props.prop]?.toString() === props.value?.toString());
  if (!item) return { text: "-", style: null };
  if (!item.color) return { text: item.text, style: null };
  return {
    style: {
      fontSize: "14px",
      height: "28px",
      padding: "0 14px",
      "font-family":
        "'sourceHanSans', 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif",
      "--el-color-info": item.color,
      "--el-color-info-light-9": item.bg,
      "--el-color-info-light-8": "transparent"
    },
    text: item.text
  };
});
</script>
