import { createGlobalState, useIntervalFn } from "@vueuse/core";
import { computed, ref, onMounted, onUnmounted } from "vue";

// 单个异步数据源的配置接口
interface AsyncStoreOptions<T, R = T> {
  // 请求函数
  fetcher: () => Promise<T>;
  // 数据转换函数
  transform?: (data: T) => R;
  // 自动刷新间隔(ms)，0表示不自动刷新
  refreshInterval?: number;
  // 是否立即获取数据
  immediate?: boolean;
}
interface CustomError extends Error {
  status?: number;
  response?: { [key: string]: any };
}
// 创建单个异步数据源
function createSingleAsyncStore<T, R = T>(options: AsyncStoreOptions<T, R>) {
  const data = ref<R | null>(null);
  const error = ref<CustomError | null>(null);
  const isLoading = ref(false);
  const lastUpdated = ref<Date | null>(null);
  const isEnabled = ref(true);

  // 获取数据的核心函数
  async function fetchData() {
    if (isLoading.value || !isEnabled.value) return;

    isLoading.value = true;
    error.value = null;

    try {
      const rawData = await options.fetcher();
      data.value = options.transform ? options.transform(rawData) : (rawData as unknown as R);
      lastUpdated.value = new Date();
    } catch (e) {
      error.value = e as CustomError;
      console.error("Fetch error:", e);
    } finally {
      isLoading.value = false;
    }
  }

  // 设置自动刷新
  let intervalControl: { pause: () => void; resume: () => void } | null = null;
  if (options.refreshInterval && options.refreshInterval > 0) {
    intervalControl = useIntervalFn(
      () => {
        if (isEnabled.value) {
          fetchData();
        }
      },
      options.refreshInterval,
      { immediate: false }
    );
  }

  // 自动处理组件生命周期
  onMounted(() => {
    if (options.immediate && isEnabled.value) {
      fetchData();
    }
    intervalControl?.resume();
  });

  onUnmounted(() => {
    intervalControl?.pause();
  });

  // 计算属性：检查数据是否已过期
  const isStale = computed(() => {
    if (!lastUpdated.value || !options.refreshInterval || !isEnabled.value) return false;
    return Date.now() - lastUpdated.value.getTime() > options.refreshInterval;
  });

  return {
    data,
    error,
    isLoading,
    isStale,
    lastUpdated,
    isEnabled,
    refresh: fetchData,
    pauseAutoRefresh: intervalControl?.pause,
    resumeAutoRefresh: intervalControl?.resume
  };
}

// 集中管理所有异步数据源的store
export const createAsyncStoreManager = () => {
  const stores = new Map<string, ReturnType<typeof createSingleAsyncStore>>();
  console.log("集中管理所有异步数据源的store", stores);
  /**
   * 注册一个新的store
   *
   * @param key - 存储在 stores Map 中的唯一标识符
   * @param options - 用于创建 store 的配置选项
   * @returns 返回注册后的 store 对象
   */
  function register<T, R = T>(key: string, options: AsyncStoreOptions<T, R>) {
    if (stores.has(key)) {
      console.warn(`Store with key ${key} already exists. It will be overwritten.`);
    }
    const store = createSingleAsyncStore(options);
    stores.set(key, store);
    return store;
  }

  /**
   * 根据 key 获取一个异步数据源
   *
   * @param key  存储在 stores Map 中的唯一标识符
   * @param throwError  可选参数，如果为 true 且不存在对应 key 的 store，则抛出错误；默认为 true
   * @returns 返回对应的 store 对象，如果不存在且不抛出错误，则返回 undefined
   * <AUTHOR>
   * @date 2025/1/21
   */
  function getStore(key: string, throwError = true) {
    const store = stores.get(key);
    if (!store && throwError) {
      throw new Error(`Store with key ${key} not found`);
    }
    return store;
  }
  // 刷新所有异步数据源
  function refreshAll() {
    return Promise.all(Array.from(stores.values()).map(store => store.refresh()));
  }
  // 暂停所有异步数据源的自动刷新
  function pauseAll() {
    stores.forEach(store => store.pauseAutoRefresh?.());
  }
  // 恢复所有异步数据源的自动刷新
  function resumeAll() {
    stores.forEach(store => store.resumeAutoRefresh?.());
  }

  return {
    register,
    getStore,
    refreshAll,
    pauseAll,
    resumeAll
  };
};

// 创建全局单例
export const useAsyncStoreManager = createGlobalState(() => createAsyncStoreManager());
