export interface DeviceItem {
  // 设备编码
  code: string;
  // 模式名称 人工 自动
  modeText: string;
  // 电量值
  battery: number;
  // 低电
  lowBattery: boolean;
  // 设备当前状态
  status: number | string;
  // 状态名称
  statusText: string;
  // 是否故障
  isFault: boolean;
  // 显示模式名称
  showMode: boolean;
  // 是否显示电量标识
  showBattery: boolean;
}
export interface StatusItem {
  // 设备状态值
  value: number;
  // 状态名称
  label: string;
  // 标识
  key: string;
}

export type DeviceStatusRowSpace = {
  type: "status";
  height: number;
  replaceFields: { [key in keyof StatusItem]: string };
  data: StatusItem[];
};
export type DeviceRowSpace = {
  type: "device-space";
  height: number;
  replaceFields: { [key in keyof DeviceItem]: string };
  data: DeviceItem[];
};

export interface DeviceSpace {
  title: string;
  rows: (DeviceStatusRowSpace | DeviceRowSpace)[];
}
