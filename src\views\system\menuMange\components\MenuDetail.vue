<template>
  <div class="menu-detail" style="flex: 1">
    <!--  显示当前点击菜单的详情  -->
    <div v-if="menuData && !menuData.isRoot" class="detail-container">
      <div v-for="item in detailItems" :key="item.field" class="detail-item">
        <div class="label">
          {{ item.label }}
        </div>
        <RenderValue v-bind="item" />
      </div>
      <template v-if="menuData.leafFlag">
        <div class="section-title">功能权限</div>
        <!--插槽接收menuData-->
        <slot />
      </template>
    </div>

    <!--  如果没有数据则显示一个占位图  -->
    <el-empty v-else description="选择一个菜单进行查看">
      <template #image>
        <img alt="" src="@/assets/images/ChooseOneOption.png" />
      </template>
    </el-empty>
  </div>
</template>

<script lang="tsx" setup>
/**
 * @file 系统管理-菜单管理-菜单详情
 * <AUTHOR>
 * @date 2024/10/31
 */
import { ResourceType, detailMap } from "./types";
import { defineComponent } from "vue";
import * as allIcon from "@/layouts/components/icon/index";
import { showValue } from "@/utils/helpers";

const props = defineProps({
  // 当前点击的菜单的数据
  menuData: { type: Object, default: () => {} }
});
// 详情展示项
const detailItems = [
  { field: detailMap.get("名称"), label: "菜单名称:" },
  {
    field: detailMap.get("类型"),
    label: "类型:",
    formatter: field => {
      if (field === "ExternalResourceMenu") {
        return <span style="color:#7766f9">外部接口菜单</span>;
      }
      if (field === ResourceType.MENU) {
        return props.menuData.isNotTenantResource ? <span style="color:#5AD1E9">非租户菜单</span> : "菜单";
      }
    }
  },
  {
    field: detailMap.get("上级菜单"),
    label: "上级菜单:",
    formatter: (field: string) => {
      return field || "无";
    }
  },
  {
    field: detailMap.get("icon"),
    label: "菜单图标:",
    formatter: field => {
      // 暂时用默认图
      return field ? <img style="width:26px;background:#5689FE" src={allIcon[field] ? allIcon[field] : ""} alt="" /> : null;
    }
  },
  { field: detailMap.get("路径"), label: "菜单路径:" },
  { field: detailMap.get("序号"), label: "菜单序号:" }
];
// 渲染内容，如果该项提供formatter且为函数则显示函数执行之后的结果，否则直接显示详情数据中对应的字段
const RenderValue = defineComponent({
  props: ["field", "formatter"],
  // eslint-disable-next-line vue/no-setup-props-destructure
  setup({ field, formatter }) {
    return () => {
      let value = formatter?.(props.menuData[field]) || props.menuData[field];
      return <span>{showValue(value)}</span>;
    };
  }
});
</script>

<style lang="scss" scoped>
:deep(.table-main) {
  width: 100%;
  height: 100% !important;
}

.detail-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}
.detail-item {
  font-size: 14px;
  display: flex;
  color: rgba(101, 102, 102, 1);
  gap: 8px;
  .label {
    font-weight: bold;
    width: auto;
    min-width: 65px;
    //禁止换行
    white-space: nowrap;
    text-align: left;
    color: rgba(26, 26, 26, 1);
  }
}
:deep(.el-empty) {
  height: 100%;
}
:deep(.el-empty__image) {
  width: 100px !important;
  height: 100px !important;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  position: relative;
  margin-top: 19px;
  padding: 5px 10px;
  color: #1a1a1a;
  &::after {
    position: absolute;
    top: 50%;
    left: 0;
    display: block;
    width: 4px;
    height: 16px;
    content: "";
    transform: translateY(-50%);
    border-radius: 2px;
    background-color: rgba(0, 209, 217, 1);
  }
}
</style>
