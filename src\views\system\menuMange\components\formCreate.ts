import { addButtonFieldsMap, addMenuFieldsMap } from "@/views/system/menuMange/types";
// 新增编辑菜单
export const menuFormCreate = {
  type: "form-create",
  props: {
    rule: [
      {
        type: "input",
        field: addMenuFieldsMap.get("菜单名称"),
        title: "菜单名称",
        validate: [{ required: true, message: "请输入菜单名称" }]
      },
      {
        type: "input",
        field: addMenuFieldsMap.get("菜单Code"),
        title: "菜单Code",
        validate: [{ required: true, message: "请输入菜单Code" }]
      },
      {
        type: "input",
        field: addMenuFieldsMap.get("上级菜单"),
        // update(val: any, rule) {
        //   if (!val) rule.value = "所有";
        // },
        title: "上级菜单",
        props: {
          disabled: true
        },
        validate: [{ required: true, message: "请输入上级菜单" }]
      },
      {
        type: "input",
        field: addMenuFieldsMap.get("Icon"),
        title: "Icon"
      },
      {
        type: "input",
        field: addMenuFieldsMap.get("菜单路径"),
        title: "菜单路径",

        validate: [{ required: true, message: "请输入菜单路径" }]
      },
      {
        type: "input",
        field: addMenuFieldsMap.get("菜单序号"),
        title: "菜单序号",

        validate: [{ required: true, message: "请输入菜单序号" }]
      },
      {
        type: "radio",
        field: addMenuFieldsMap.get("菜单类型"),
        title: "菜单类型",
        options: [
          { value: "tenant", label: "普通菜单" },
          { value: "ExternalResourceMenu", label: "外部接口菜单" }
        ]
      }
    ],
    option: {
      n: "menuFormCreate",

      submitBtn: false,
      form: {
        labelWidth: "85px"
      },
      // forceCoverValue: true, //开启v-model强制同步
      onSubmit(formData, api) {
        api.top.exec("v:tree", "fetchData");
      }
    }
  }
};

// 权限按钮表单
export const featurePermissionsFormCreate = {
  type: "form-create",
  props: {
    rule: [
      {
        type: "input",
        field: addButtonFieldsMap.get("按钮名称"),
        title: "名称",
        props: {
          placeholder: "输入名称"
        },
        validate: [
          { required: true, message: "请输入名称" },
          {
            pattern: /^.{1,20}$/,
            message: "字符限长20位"
          }
        ]
      },
      {
        type: "input",
        field: addButtonFieldsMap.get("按钮编码"),
        title: "编码",
        props: {
          placeholder: "输入编码"
        },
        validate: [
          { required: true, message: "请输入编码" },
          {
            pattern: /^.{1,20}$/,
            message: "字符限长20位"
          }
        ]
      }
    ],
    option: {
      submitBtn: false,
      form: {
        labelWidth: "85px"
      },
      // forceCoverValue: true, //开启v-model强制同步
      onSubmit(formData, api) {
        // 通知 table数据变化，刷新数据
        setTimeout(() => {
          api.top.children[0].bus.$emit("searchFormChanged");
        }, 100);
      }
    }
  }
};
