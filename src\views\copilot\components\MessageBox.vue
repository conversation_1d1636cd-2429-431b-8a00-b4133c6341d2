<template>
  <el-dialog
    v-model="visible"
    width="480px"
    height="192px"
    align-center
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="handleClose"
    class="copilot-dialog"
    :style="{
      backgroundColor: 'rgba(59, 60, 167, 1)',
      '--el-border-color-lighter': props.messageBox ? 'transparent' : 'rgba(221, 226, 232, 0.1)'
    }"
  >
    <template #title>
      <div style="display: flex; align-items: center; gap: 8px">
        <img v-if="props.messageBox" :src="IconTip" alt="IconTip" style="width: 20px; height: 20px" />
        <span style="font-weight: 600; color: rgba(255, 255, 255, 1)">{{ props.title }}</span>
      </div>
    </template>
    <div class="dialog-content" :style="props.dialogContentStyle">
      <slot>
        <span class="dialog-message">{{ props.message }}</span>
      </slot>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button
          @click="handleCancel"
          :size="props.messageBox ? 'default' : 'large'"
          plain
          :style="{ '--el-button-border-color': 'rgba(255, 255, 255, .4)', width: props.messageBox ? '58px' : '108px' }"
        >
          {{ props.cancelButtonText }}
        </el-button>
        <el-button
          type="primary"
          :style="{ '--el-color-primary': 'rgba(53, 106, 253, 1)', width: props.messageBox ? '58px' : '148px' }"
          :size="props.messageBox ? 'default' : 'large'"
          :loading="processing"
          @click="handleConfirm"
        >
          {{ props.confirmButtonText }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { ElMessage } from "element-plus";
import IconTip from "../imgs/IconTip.svg?url";

interface Props {
  // 是否为消息框， 还是dialog
  messageBox: boolean;
  title?: string;
  message: string;
  confirmButtonText?: string;
  cancelButtonText?: string;
  dialogContentStyle?: string;
  beforeClose?: () => Promise<boolean | string>;
}

const props = withDefaults(defineProps<Props>(), {
  title: "提示",
  confirmButtonText: "确定",
  cancelButtonText: "取消",
  messageBox: true
});

const emit = defineEmits<{
  (e: "confirm"): void;
  (e: "cancel"): void;
  (e: "close"): void;
}>();

// 显示状态
const visible = ref(false);
// 处理中状态
const processing = ref(false);

// 确认操作
const handleConfirm = async () => {
  if (props.beforeClose) {
    processing.value = true;
    try {
      const canClose = await props.beforeClose();
      if (typeof canClose === "boolean" && canClose) {
        visible.value = false;
        emit("confirm");
        ElMessage({ type: "success", message: "操作成功" });
      } else {
        ElMessage({ type: "error", message: canClose || "操作未完成，请重试" });
      }
    } catch (error) {
      console.error(error);
      ElMessage({ type: "error", message: "操作失败，请重试" });
    } finally {
      processing.value = false;
    }
  } else {
    visible.value = false;
    emit("confirm");
  }
};

// 取消操作
const handleCancel = () => {
  visible.value = false;
  emit("cancel");
};

// Dialog关闭前的处理
const handleClose = (done: () => void) => {
  if (processing.value) {
    ElMessage({
      type: "warning",
      message: "操作进行中，请稍候..."
    });
    return;
  }
  done();
  emit("close");
};

// 打开对话框
const show = () => {
  visible.value = true;
};

// 暴露方法给父组件
defineExpose({ show });
</script>
<style lang="scss">
.copilot-dialog {
  .el-dialog__header {
    border-bottom: 1px solid var(--el-border-color-lighter);
  }
}
</style>
<style lang="scss" scoped>
.dialog-content {
  margin-top: -20px;
  margin-left: 20px;
  padding-right: 50px;
}
</style>
