/* 全局通用css modules 样式文件*/

/* 按钮 */
.shadowButton {
  box-shadow:
    0 5px 5px 0 rgba(86, 137, 254, 0.25),
    0 10px 10px 0 rgba(86, 137, 254, 0.1);
  //filter: drop-shadow(0px 10px 8px rgba(86, 137, 254, 0.26));

  &:focus {
    background: var(--el-button-bg-color);
  }
}
//按钮组
.shadowButtonGroup {
  background: #fff;
  border-radius: 6px;
  margin-bottom: 10px;
  overflow: hidden;
  & > :global(.el-button--text) {
    color: rgba(26, 26, 26, 1);
  }

  & > :global(.el-button) {
    position: relative;
    padding: 9px 24px;
    border-radius: initial;
    //按钮之间的间隔调整
    & + :global(.el-button) {
      margin-left: 0;
    }

    //激活按钮和激活按钮的下一个不添加竖线
    &:global(.active)::before,
    &:global(.active) + :global(.el-button)::before {
      background: transparent !important;
    }

    //非第一个的所有按钮前加上竖线
    &:not(:first-child)::before {
      position: absolute;
      left: 0;
      display: inline-block;
      width: 1px;
      height: 10px;
      content: "";
      background: #cad9fc;
    }
  }
}

/* 页面中单独的标题样式 */
.topPageTitle {
  font-size: 20px;
  font-weight: bold;
  position: absolute;
  top: -35px;
  left: 10px;
  color: #1a1a1a;

  &:before {
    display: inline-block;
    width: 4px;
    height: 18px;
    margin-right: 5px;
    content: "";
    border-radius: 2px 2px 2px 2px;
    background: #5689fe;
  }
}

/*解决form表单项在火狐浏览器缩放条件下label会换行的问题*/
.LAForm {
  :global(.el-form-item .el-form-item__label) {
    white-space: nowrap;
  }
}
:global(.el-dropdown-menu__item) {
  padding: 7px 20px !important;
}
.LATableDropdown {
  :global(.el-dropdown-menu__item div[data-auth="delete"] .el-button--text) {
    color: #f25555;
  }

  :global(.el-dropdown-menu__item .el-button--text) {
    color: #1a1a1a;
  }
}
