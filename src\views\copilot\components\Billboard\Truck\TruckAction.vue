<template>
  <div class="truck-actions">
    <!-- <el-scrollbar style="width: 100%; height: 100%"> -->
    <!-- Action buttons grid -->
    <div class="action-grid">
      <div
        class="button"
        :class="{ disabled: !isDisabled(action) }"
        v-for="action in actions.filter(item => item.visible === undefined || item.visible?.())"
        :key="action.key"
        @click="action.onClick(action.key)"
      >
        <SvgIcon :name="action.icon" icon-style="width:20px; height:20px;" />
        {{ action.label }}
      </div>
    </div>
    <!-- </el-scrollbar> -->
  </div>

  <MessageBox
    ref="messageBoxRef"
    v-bind="messageProps"
    :beforeClose="handleBeforeClose"
    @confirm="handleConfirm"
    @cancel="handleCancel"
  />

  <!-- 一键发车任务 -->
  <AddToTaskMessageBox ref="addToTaskMessageBoxRef" />
  <!-- 新增任务 -->
  <TaskCreator ref="taskCreatorRef" />
  <!-- 切为空车、重车 -->
  <ToggleLoaded ref="toggleLoadedRef" />
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { ElMessage } from "element-plus";
import SvgIcon from "@/components/SvgIcon/index.vue";
import MessageBox from "@/views/copilot/components/MessageBox.vue";
// 一键发车
import AddToTaskMessageBox from "./components/AddToTaskMessageBox.vue";
// 新增任务
import TaskCreator from "./components/TaskCreator.vue";
// 切为重车
import ToggleLoaded from "./components/ToggleLoaded.vue";
import { useDeviceControl } from "@/views/copilot/request";
import { useDeviceSelection } from "@/views/copilot/store";
import { isTruckAutoDrive } from "@/views/copilot/shared";
import { StatusEnum as TruckStatusEnum } from "@/views/deviceManage/miningTruckManage/types";
const { changeLantern, backTask, chargeTask, clearFault: clearDeviceFault, pauseOrContinue, cancelTask } = useDeviceControl();
const { selectedDeviceInfo } = useDeviceSelection();

// 车辆状态
const messageBoxRef = ref();
const addToTaskMessageBoxRef = ref();
const taskCreatorRef = ref();
const toggleLoadedRef = ref();
const messageProps = ref({ title: "提示", message: "" });
// 当前选中的操作
const currentAction = ref("");

// 完整的按钮动作配置
const actions = [
  {
    key: "dispatch",
    label: "一键发车",
    icon: "IconDispatch",
    disabled: () => {
      const { status, driveMode } = selectedDeviceInfo?.value?.rawData!;
      // 不可点击：非自动模式、矿卡状态为运行中、充电中、故障中、离线中
      return (
        !isTruckAutoDrive(driveMode) ||
        [
          TruckStatusEnum.TRAIN_BUSY,
          TruckStatusEnum.TRAIN_CHARGING,
          TruckStatusEnum.TRAIN_FAILURE,
          TruckStatusEnum.TRAIN_OFFLINE
        ].includes(status)
      );
    },
    onClick: (key: string) => {
      currentAction.value = key;
      addToTaskMessageBoxRef.value.show();
    }
  },
  {
    key: "cancel",
    label: "取消任务",
    icon: "IconCancel",
    disabled: () => {
      const { taskCode } = selectedDeviceInfo?.value?.rawData!;
      // 不可点击：矿卡状态为空闲中、离线中
      return !taskCode;
    },
    onClick: (key: string) => {
      currentAction.value = key;
      messageProps.value = {
        title: "是否取消当前任务",
        message: "取消后，该矿车变成空闲状态，可执行其他任务"
      };
      showMessageBox();
    }
  },
  {
    key: "toggleUnload",
    label: "切为空车",
    icon: "IconToggleUnloaded",
    disabled: () => {
      const { taskCode } = selectedDeviceInfo?.value?.rawData!;
      // 不可点击：矿卡状态为空闲中、离线中
      return !taskCode;
    },
    onClick: (key: string) => {
      currentAction.value = key;
      messageProps.value = {
        title: `是否切为空车-${selectedDeviceInfo?.value?.rawData?.name}`,
        message: "切为空车后，车辆可被调度执行装卸任务"
      };
      showMessageBox();
    }
  },
  {
    key: "toggleLoaded",
    label: "切为重车",
    icon: "IconToggleLoaded",
    disabled: () => {
      const { taskCode } = selectedDeviceInfo?.value?.rawData!;
      // 不可点击：矿卡状态为空闲中、离线中
      return !taskCode;
    },
    onClick: (key: string) => {
      currentAction.value = key;
      toggleLoadedRef.value.show();
    }
  },
  {
    key: "clearFault",
    label: "故障清除",
    icon: "IconClearFault",
    disabled: () => {
      const { errorFlag } = selectedDeviceInfo?.value?.rawData!;
      // 不可点击：矿卡状态为空闲中、离线中
      return +errorFlag === 0;
    },
    onClick: (key: string) => {
      currentAction.value = key;
      messageProps.value = {
        title: "是否清除所有故障",
        message: "清除后，该矿车所有故障信息将被清除"
      };
      showMessageBox();
    }
  },
  {
    key: "emergencyStop",
    label: "紧急停车",
    icon: "IconEmergencyStop",
    visible: () => {
      // 手刹（0:关闭,1:开启）
      const { handbrake } = selectedDeviceInfo?.value?.rawData!;
      return handbrake === 0;
    },
    disabled: () => {
      const { status } = selectedDeviceInfo?.value?.rawData!;
      // 不可点击：矿卡状态为离线中
      return [TruckStatusEnum.TRAIN_OFFLINE].includes(status);
    },
    onClick: (key: string) => {
      currentAction.value = key;
      messageProps.value = {
        title: "是否紧急停车",
        message: "确认后，该矿车将立刻停止行驶"
      };
      showMessageBox();
    }
  },
  {
    key: "vehicleRecovery",
    label: "车辆恢复",
    icon: "IconVehicleRecovery",
    disabled: () => {
      const { status } = selectedDeviceInfo?.value?.rawData!;
      // 不可点击：矿卡状态为离线中
      return [TruckStatusEnum.TRAIN_OFFLINE].includes(status);
    },
    visible: () => {
      // 手刹（0:关闭,1:开启）
      const { handbrake } = selectedDeviceInfo?.value?.rawData!;
      return handbrake !== 0;
    },
    onClick: (key: string) => {
      currentAction.value = key;
      messageProps.value = { title: "是否恢复车辆", message: "恢复后，该矿车将继续行驶" };
      showMessageBox();
    }
  },
  {
    key: "returnToBase",
    label: "收车回场",
    icon: "IconReturnToBase",
    disabled: () => {
      const { status, driveMode } = selectedDeviceInfo?.value?.rawData!;
      // 不可点击：非自动模式、矿卡状态为充电中、故障中、离线中
      return (
        !isTruckAutoDrive(driveMode) ||
        [TruckStatusEnum.TRAIN_CHARGING, TruckStatusEnum.TRAIN_FAILURE, TruckStatusEnum.TRAIN_OFFLINE].includes(status)
      );
    },
    onClick: (key: string) => {
      currentAction.value = key;
      messageProps.value = {
        title: "是否收车回场",
        message: "收车回场后，该矿车将回到停车位"
      };
      showMessageBox();
    }
  },
  {
    key: "charge",
    label: "收车充电",
    icon: "IconCharge",
    disabled: () => {
      const { status, driveMode } = selectedDeviceInfo?.value?.rawData!;
      // 不可点击：非自动模式、矿卡状态为充电中、故障中、离线中
      return (
        !isTruckAutoDrive(driveMode) ||
        [TruckStatusEnum.TRAIN_CHARGING, TruckStatusEnum.TRAIN_FAILURE, TruckStatusEnum.TRAIN_OFFLINE].includes(status)
      );
    },
    onClick: (key: string) => {
      currentAction.value = key;
      messageProps.value = {
        title: "是否收车充电",
        message: "收车充电后，该矿车将行驶到充电桩"
      };
      showMessageBox();
    }
  },
  {
    key: "lightOn",
    label: "车辆开灯",
    icon: "IconLightOn",
    disabled: () => {
      const { status } = selectedDeviceInfo?.value?.rawData!;
      // 不可点击：矿卡状态为离线中
      return [TruckStatusEnum.TRAIN_OFFLINE].includes(status);
    },
    onClick: (key: string) => {
      currentAction.value = key;
      messageProps.value = {
        title: "是否打开车灯",
        message: "打开车灯后，该矿车近光灯会被打开"
      };
      showMessageBox();
    }
  },
  {
    key: "lightOff",
    label: "车辆关灯",
    icon: "IconLightOff",
    disabled: () => {
      const { status } = selectedDeviceInfo?.value?.rawData!;
      // 不可点击：矿卡状态为离线中
      return [TruckStatusEnum.TRAIN_OFFLINE].includes(status);
    },
    onClick: (key: string) => {
      currentAction.value = key;
      messageProps.value = {
        title: "是否关闭车灯",
        message: "关闭车灯后，该矿车近光灯会被关闭"
      };
      showMessageBox();
    }
  },
  {
    key: "createTask",
    label: "新增任务",
    icon: "IconTruckCreateTask",
    disabled: () => {
      const { status } = selectedDeviceInfo?.value?.rawData!;
      // 不可点击：矿卡状态为离线中
      return [TruckStatusEnum.TRAIN_OFFLINE].includes(status);
    },
    onClick: (key: string) => {
      currentAction.value = key;
      taskCreatorRef.value.show();
    }
  }
];

// 方法
const isDisabled = (action: any) => {
  return typeof action.disabled === "function" ? action.disabled() : action.disabled;
};

// 关闭前检查
const handleBeforeClose = async () => {
  return true;
};

// 打开对话框
const showMessageBox = () => {
  messageBoxRef.value.show();
};

// 确认回调
const handleConfirm = async () => {
  try {
    switch (currentAction.value) {
      case "clearFault":
        await clearDeviceFault();
        break;
      case "cancel":
        await cancelTask();
        break;
      case "emergencyStop":
        await pauseOrContinue({ status: 0 });
        break;
      case "vehicleRecovery":
        await pauseOrContinue({ status: 1 });
        break;
      case "returnToBase":
        await backTask();
        break;
      case "charge":
        await chargeTask();
        break;
      case "lightOn":
        await changeLantern({ status: 1 });
        break;
      case "lightOff":
        await changeLantern({ status: 0 });
        break;
    }
    ElMessage({
      type: "success",
      message: "操作成功"
    });
  } catch (error) {
    console.error("操作失败:", error);
  }
};

// 取消回调
const handleCancel = () => {
  currentAction.value = "";
};
</script>

<style scoped>
.truck-actions {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 8px;
  height: calc(100% - 40px);
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.emergency-controls {
  display: flex;
  gap: 8px;
}

.button {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 40px;
  gap: 2px;
  background: linear-gradient(180deg, #191a47 0%, #0e2d82 100%);
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #040536;
  cursor: pointer;
  user-select: none;

  &.disabled {
    cursor: not-allowed;
    background: transparent;
    color: #747595;
    filter: grayscale(1);
  }

  &:not(.disabled):hover {
    border: 1px solid #ffffff;
  }
}
</style>
