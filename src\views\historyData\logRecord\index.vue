<template>
  <form-create v-model:api="fApi" :option="option" :rule="rule"></form-create>
</template>

<script lang="ts" setup>
/**
 * @file 历史数据-日志记录
 * <AUTHOR>
 * @date 2025/7/21
 */
import { ref } from "vue";
import { columnMap } from "./types";
import formCreate from "@form-create/element-ui";
import LASelect from "@/components/LASelect.tsx";

import { getDispatchRunLog, getLogType } from "@/api/modules/historyData.ts";
const fApi = ref();
const option = {
  form: { inline: true },
  resetBtn: false,
  submitBtn: false
};

const rule = ref([
  {
    type: "SearchFormOperation",
    field: "v:search",
    wrap: { style: "marginBottom: 0" },
    children: [
      {
        component: LASelect,
        field: "logType",
        style: { width: "180px", lineHeight: "initial" },
        props: {
          fetch: () => {
            return getLogType().then((res: any) => {
              return res.data.map(item => {
                return {
                  label: item,
                  value: item
                };
              });
            });
          },
          placeholder: "日志分类"
        }
      },
      {
        type: "input",
        field: "content",
        props: {
          size: "default",
          placeholder: "日志描述"
        }
      },
      {
        type: "LADateTimeRangePicker",
        name: "time",
        style: { lineHeight: "initial", height: "32px" },
        props: {
          type: "daterange",
          format: "YYYY-MM-DD",
          placeholder: ["起始日期", "截止日期"]
        },
        on: {
          "update:start": val => {
            if (val) {
              fApi.value.form["startTime"] = val;
            } else {
              fApi.value.form["startTime"] = undefined;
            }
          },
          "update:end": val => {
            if (val) {
              fApi.value.form["endTime"] = val;
            } else {
              fApi.value.form["endTime"] = undefined;
            }
          }
        }
      }
    ]
  },
  {
    type: "ProTable",
    props: {
      columns: [
        {
          prop: columnMap.get("日志分类"),
          label: "日志分类"
        },
        {
          prop: columnMap.get("日志描述"),
          label: "日志描述"
        },

        {
          prop: columnMap.get("记录时间"),
          label: "记录时间"
        }
      ],
      fetch: getDispatchRunLog
    }
  }
]);
</script>

<style lang="scss" scoped>
:deep(.el-row) {
  height: calc(100vh - 138px) !important;
  .el-select--large .el-select__wrapper {
    min-height: initial;
  }
}

.el-form-item {
  margin-right: 4px !important;
}
</style>
<style lang="scss">
.task-record-detail {
  width: 400px !important;
  .el-form {
    height: 100%;
    .el-row {
      display: inherit;
      height: 100%;
    }
  }
}
</style>
