/**
 * @file 项目通用级联组件
 * - 提供fetch方法参数，支持外部传入
 * <AUTHOR>
 * @date 2022/4/28
 */
import { defineComponent, ref } from "vue";
import { ElCascader, CascaderOption, CascaderProps } from "element-plus";

export default defineComponent({
  props: {
    /**
     * 级联列表的可选项数据源
     * - 如果外部提供了fetch方法，则该属性无效
     */
    options: { type: Array as () => CascaderOption[], default: () => [] },
    /**
     * 请求级联列表数据的方法
     * - 如果外部提供了options属性，则该属性无效
     * @param params 请求参数
     * @returns {Promise<any>}
     */
    fetch: { type: Function, default: null },
    /**
     * 请求级联列表数据的参数
     * - 如果外部提供了fetch方法，则该属性无效
     */
    params: { type: Object, default: () => ({}) },
    /**
     * 禁用选项
     */
    disabledOptions: { type: Array, default: () => [] }
  },
  emits: ["change"],
  setup(props, { emit, attrs }) {
    // 级联列表的可选项数据源
    const options = ref(props.options || []);
    // 如果外部提供了fetch方法，则请求对应的级联列表的数据
    if (props.fetch) {
      props.fetch(props.params || {}).then(res => {
        if (res.data && Array.isArray(res.data)) {
          handleDisabledOptions(res.data);
          options.value = res.data;
        }
      });
    }
    // 递归数据匹配disabledOptions id添加disabled 属性
    function handleDisabledOptions(arr) {
      arr.forEach(item => {
        const value = item[(attrs.props as CascaderProps).value || "value"];
        if (props.disabledOptions.includes(value)) {
          item.disabled = true;
        }
        const children = item[(attrs.props as CascaderProps).children || "children"];
        if (children) handleDisabledOptions(children);
      });
    }
    /**
     * 通过选中的值在options中查找选中项的所有数据
     * @param values 所选中的值
     * @param field 查找的字段名称
     * <AUTHOR>
     * @date 2022/7/14
     */
    function findOptions(values: string[], field) {
      // 存储结果变量
      const chooseOptions: any[] = [];
      let arr = options.value;
      for (const optionVal of values) {
        const option: any = arr.find(item => item[field] === optionVal);
        chooseOptions.push(option);
        arr = option.children;
      }
      return chooseOptions;
    }

    /**
     * 级联菜单的change处理事件
     * @param value 选中的值
     * <AUTHOR>
     * @date 2022/7/14
     */
    function onChange(value) {
      /*
       * attrs.props.emitPath 属性
       * 在选中节点改变时，是否返回由该节点所在的各级菜单的值所组成的数组，若设置 false，则只返回该节点的值，默认为true
       * */
      // 目前只适配单选且emitPath不为false时，val为选中的各级菜单值组成的数组
      // @ts-ignore
      if (attrs.props.multiple !== true && attrs.props.emitPath !== false) {
        // 查找选中的各级菜单值组成的option数组
        // @ts-ignore
        const chooseOptions = findOptions(value, attrs.props.value || "value");
        emit("change", value, chooseOptions);
      } else {
        emit("change", value);
      }
    }

    return () => <ElCascader size="large" ref="cascader" onChange={onChange} style={{ width: "100%" }} options={options.value} />;
  }
});
