<template>
  <!-- 筛选数据的输入框 -->
  <LASearchInput
    v-if="needFilter"
    v-model="filterText"
    :placeholder="searchPlaceholder"
    clearable
    size="default"
    style="margin-bottom: 10px"
    use-blue-icon
  />
  <!-- 树组件 -->
  <el-tree
    ref="treeRef"
    v-loading="loading"
    :current-node-key="modelValue"
    :data="data"
    :default-checked-keys="checked"
    :empty-text="emptyText"
    :expand-on-click-node="expandOnClickNode"
    :filter-node-method="filterNodeMethod || defaultFilterNodeMethod"
    :node-key="nodeKey"
    :props="treeProps"
    :render-content="renderContent"
    :show-checkbox="showCheckbox"
    highlight-current
    v-bind="$attrs"
    @check="checkedChange"
    @current-change="currentChange"
  >
    <!--  默认插槽,每一行显示的内容  -->
    <template #default="{ node, data }: any">
      <div :class="['custom-tree-node', data.isRoot ? 'custom-root-node' : '']">
        <div style="display: flex; align-items: center">
          <!--    icon图标插槽      -->
          <slot name="icon" v-bind="{ data }"></slot>

          <!--是否启用tree图标-->
          <div v-if="isTreeShowIcon" class="menu-icon"></div>

          <!--{{ data.name }}-->
          <!--    label显示文字插槽      -->
          <slot name="label" v-bind="{ data }">
            <div class="text" style="margin-top: 1px; text-overflow: ellipsis; overflow: hidden">
              {{ data[treeProps.label] }}
            </div>
          </slot>
        </div>
        <div class="control">
          <!--    操作按钮插槽      -->
          <!--          <slot name="control" v-bind="{ data }"> </slot>-->
          <Operation
            v-if="(typeof operations === 'function' && operations(data).length) || operations.length"
            :form-create-inject="formCreateInject"
            :is-show-icon="isShowIcon"
            :mitt-bus="mittBus"
            :operations="typeof operations === 'function' ? operations(data) : operations"
            :row="data"
          />
        </div>
      </div>
    </template>
  </el-tree>
  <slot />
</template>

<script lang="ts" setup>
/**
 * @file 展示树级结构的树组件
 * <AUTHOR>
 * @date 2024/10/30
 */
import { nextTick, provide, ref, watch } from "vue";
import type { ElTree } from "element-plus";
import LASearchInput from "@/components/LASearchInput/index.vue";
import Operation from "@/components/ProTable/components/operation/index.vue";
import { Api } from "@form-create/element-ui";
import mitt from "mitt";

const mittBus = mitt();
provide("mittBus", mittBus);
// 树组件实例
const treeRef = ref();
type Operations = { content?: string; onClick: (row: { [key: string]: any }, api: Api) => void; key: any }[];
const props = withDefaults(
  // 属性定义
  defineProps<{
    searchPlaceholder?: string;
    isTreeShowIcon?: false;
    // 数组件需要展示的数据
    treeData?;
    // 数据为空时显示的文字,默认为暂无数据
    emptyText?: string;
    // 每一行数据的唯一标识,默认为id
    nodeKey?: string;
    // 指定某些属性对应数据中的字段名称,默认的字段名称见下面
    treeProps?: {
      label?;
      children?;
      disabled?;
      isLeaf?;
      class?;
    };
    // 针对树节点渲染的render函数，如果传递则默认插槽无效。
    // 接收参数(h, { node, data, store })
    renderContent?;
    // 是否在点击节点的时候展开或者收缩节点,如果为 false，则只有点箭头图标的时候才会展开或者收缩节点,默认为false
    expandOnClickNode?: boolean;
    // 是否显示复选框,默认不显示
    showCheckbox?: boolean;
    // 是否需要过滤数据,如果需要过滤数据,则会在树组件上面显示一个输入框,默认不需要
    needFilter?: boolean;
    // 过滤数据的方法,在过滤数据时需要执行,如果不传入则执行默认的过滤方法
    filterNodeMethod?;
    // 当前复选框选中项
    checked?: [];
    // 当前激活的项
    modelValue?: string | number;
    // 自动获取数据方法（如果传入此方法则自动获取数据）
    fetch?;
    // 处理自动获取的数据
    handleData?;
    formCreateInject: { api: Api };
    isShowIcon?: boolean;
    operations: ((data) => Operations) | Operations;
  }>(),
  //  默认值定义
  {
    searchPlaceholder: "请输入关键字",
    treeData: [],
    emptyText: "暂无数据",
    nodeKey: "id",
    treeProps: () => ({
      label: "label",
      children: "children",
      disabled: "disabled",
      isLeaf: "isLeaf",
      class: "class"
    }),
    expandOnClickNode: false,
    showCheckbox: false,
    needFilter: false,
    isShowIcon: false,
    operations: () => []
  }
);

const emits = defineEmits([
  // 更新外部的v-model:checked 绑定的值,当前选中项
  "update:checked",
  // 	点击节点复选框之后触发的事件
  "checkedChange",
  // 更新外部的v-model 绑定的值,当前激活项
  "update:modelValue",
  // 当前选中节点变化时触发的事件
  "currentChange",
  // 请求发送成功的事件
  "fetchSuccess"
]);
// 数据加载时的loading动画的显示
const loading = ref(false);

// 树组件所需要的data
const data = ref(props.treeData);

// 监听传入的数组件的数据的变化,重新赋值给数组件
watch(
  () => props.treeData,
  val => {
    data.value = val;
  }
);

/**
 * 利用传递的api向后台获取数据
 * <AUTHOR>
 * @date 2022/5/5
 */
const fetchData = async () => {
  loading.value = true;
  const res = await props.fetch().catch(err => {
    console.log("err", err);

    loading.value = false;
    data.value = [];
    return;
  });

  data.value = props.handleData ? props.handleData(res) : res?.data;
  emits("fetchSuccess", data.value);
  loading.value = false;
};
// 如果传递了fetch,则向后台拿取数据
props.fetch && fetchData();

// 绑定过滤的输入框的值
const filterText = ref("");
// 当过滤的输入框的值发生变化的时候,调用树组件的filter方法对数据进行过滤
watch(filterText, val => {
  treeRef.value!.filter(val);
});

/**
 * 树组件过滤数据的时候默认执行的过滤方法.如果传入了filterNodeMethod方法,则使用传入的方法
 * @param value 当前过滤的参数值
 * @param data 数组件的数据
 * @return {boolean} 返回 false 则表示这个节点会被隐藏
 * <AUTHOR>
 * @date 2022/5/5
 */
const defaultFilterNodeMethod = (value, data) => {
  if (!value) return true;
  return data[props.treeProps.label].includes(value);
};

/**
 * 复选框选中变化时触发的事件
 * @param data 当前变化项的数据
 * @param checkedKeys 树组件中勾选的节点的key的集合
 * @param checkedNodes 树组件中勾选节点的Node集合
 * @param halfCheckedKeys 数组件中半选的节点的key的集合
 * @param halfCheckedNodes 数组件中半选的节点的Node的集合
 * <AUTHOR>
 * @date 2022/5/5
 */
const checkedChange = (data, { checkedKeys, checkedNodes, halfCheckedKeys, halfCheckedNodes }) => {
  // 触发"update:checked" 事件的执行,把当前勾选的节点的key的集合通过事件进行抛出
  emits("update:checked", checkedKeys);
  // 触发checkedChange 事件的执行,把剩下的数据进行抛出
  emits("checkedChange", {
    checkedKeys,
    checkedNodes,
    halfCheckedKeys,
    halfCheckedNodes
  });
};

/**
 * 当前选中节点变化时触发的事件
 * @param data 当前选中节点的数据
 * <AUTHOR>
 * @date 2022/5/5
 */
const currentChange = data => {
  // 触发 update:current 事件,把当前激活项的key进行抛出
  emits("update:modelValue", data[props.nodeKey]);
  // 触发currentChange事件,把当前激活项的data抛出
  emits("currentChange", data, props.formCreateInject.api);
};

/**
 * 手动设置复选框选中节点
 * @param keys
 * <AUTHOR>
 * @date 2022/5/5
 */
const setChecked = keys => {
  // 调用树组件的setCheckedKeys方法勾选指定的节点
  treeRef.value?.setCheckedKeys(keys);
  // 将目前树组件勾选的节点的key进行抛出
  emits("update:checked", treeRef.value.getCheckedKeys());
};
// 监听外部传入的checked属性的变化,如果值有变化,则手动设置复选框的选中节点
watch(
  () => props.checked,
  (val, oldVal) => {
    if (JSON.stringify(val) !== JSON.stringify(oldVal)) {
      setChecked(val);
    }
  }
);

/**
 * 手动设置当前激活的项
 * @param key 当前被激活的节点的key
 * @param expandParent 是否自动展开父节点,默认自动展开
 * <AUTHOR>
 * @date 2022/5/5
 * */
const setCurrent = (key, expandParent = true) => {
  return treeRef.value?.setCurrentKey(key, expandParent);
};
// 监听外部传入current属性的变化,然后手动设置树组件的当前激活节点
watch(
  () => props.modelValue,
  val => nextTick(() => setCurrent(val)),
  { immediate: true }
);

defineExpose({
  treeRef,
  setChecked,
  setCurrent,
  fetchData
});
</script>

<style lang="scss" scoped>
::v-deep(.el-input__inner) {
  border-radius: 20px;
}

.custom-tree-node {
  flex: 1;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
  & > div:first-child {
    overflow: hidden;
    flex: 1;
  }
  //隐藏control
  .control {
    opacity: 0;
    pointer-events: none;
    transition: opacity linear 0.1s;
  }

  //鼠标hover显示control
  &:hover {
    color: rgba(86, 137, 254, 1);

    .control {
      opacity: 1;
      pointer-events: unset;
    }
  }
}

//激活项的显示control按钮
::v-deep(.el-tree-node.is-current > .el-tree-node__content .custom-tree-node .control) {
  opacity: 1;
}

::v-deep(.custom-root-node) {
  font-weight: bold;
  color: #1a1a1a;
  height: fit-content;
}

::v-deep(.el-tree-node) {
  padding: 2px 0;

  .el-tree-node__content {
    //display: inherit;
    height: 32px;
    border-radius: 4px;
  }
}
</style>
