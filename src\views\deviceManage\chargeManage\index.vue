<template>
  <form-create v-model:api="fApi" :option="option" :rule="rule"></form-create>
</template>
<script lang="tsx" setup>
/**
 * @file 设备管理-充电桩管理
 * <AUTHOR>
 * @date 2024/11/20
 */
import { ref } from "vue";
import { ChargeStatusEnum, columnMap } from "./types";
import { chargeMangeFormCreate } from "./components/formCreate";
import formCreate from "@form-create/element-ui";
import { deleteChargingPile, getChargingPileList, saveChargingPile } from "@/api/modules/device";

const fApi = ref();
const option = {
  form: { inline: true },
  resetBtn: false,
  submitBtn: false
};

const rule = ref([
  {
    type: "SearchFormOperation",
    field: "v:search",
    wrap: { style: "marginBottom: 0" },
    children: [
      {
        type: "input",
        field: "search",
        props: {
          size: "default",
          placeholder: "充电桩名称/编码"
        }
      },
      // 新增
      {
        type: "AddBtn",
        slot: "suffix",
        props: {
          btn: { content: "新增充电桩", auth: "add" },
          dialog: {
            title: "新增充电桩"
          },

          size: "default",
          submitRequest: saveChargingPile
        },
        children: [chargeMangeFormCreate]
      }
    ]
  },
  {
    type: "ProTable",
    props: {
      columns: [
        {
          prop: columnMap.get("名称"),
          label: "名称"
        },
        {
          prop: columnMap.get("编码"),
          label: "编码"
        },
        {
          prop: "status",
          label: "状态",
          tag: true,
          enum: [...ChargeStatusEnum]
        },
        {
          prop: columnMap.get("型号"),
          label: "型号"
        },
        {
          prop: columnMap.get("1#充电接口编码"),
          label: "1#充电接口编码"
        },
        {
          prop: columnMap.get("2#充电接口编码"),
          label: "2#充电接口编码"
        },
        { prop: "operation", label: "操作", fixed: "right" }
      ],
      fetch: getChargingPileList,
      operations: [
        { content: "修改", action: "edit" },
        { content: "删除", action: "delete", props: { style: { color: "rgba(242, 85, 85, 1)" } } }
      ]
    },

    children: [
      {
        type: "EditBtn",
        props: {
          action: "edit",
          dialog: {
            title: "修改充电桩"
          },
          submitRequest: saveChargingPile
        },
        children: [chargeMangeFormCreate]
      },
      {
        type: "ConfirmDialog",
        on: {
          // 监听弹窗组件抛出的的afterSubmit事件，用于刷新页面
          afterSubmit: () => {
            // 刷新，调用组件内部请求方法
            fApi.value.exec("v:search", "onSearch");
          }
        },
        props: {
          title: "是否删除充电桩",
          message: "删除后不可恢复",
          subtitle: (row: string) => {
            return row[columnMap.get("名称")];
          },
          action: "delete",
          // 模拟请求param：参数
          submitRequest: deleteChargingPile
        }
      }
    ]
  }
]);
</script>
<style lang="scss" scoped>
:deep(.el-row) {
  height: var(--page-height);
}

.el-form-item {
  margin-right: 4px !important;
}
</style>
