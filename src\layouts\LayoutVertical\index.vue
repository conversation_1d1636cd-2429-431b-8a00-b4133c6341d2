<!-- 纵向布局 -->
<template>
  <el-container class="layout">
    <el-aside>
      <div :style="{ width: '176px' }" class="aside-box">
        <div class="logo flex-center">
          <img alt="logo" class="logo-img" src="@/assets/images/logo.png" />
        </div>
        <el-button v-if="isCopilot" class="menu-button" plain size="large" @click="toCopilot">无人中台</el-button>
        <el-scrollbar>
          <el-menu
            :collapse-transition="false"
            :default-active="activeMenu"
            :router="false"
            :unique-opened="accordion"
            @close="handleSelect($event, 'close')"
            @open="handleSelect($event, 'open')"
          >
            <SubMenu :active-menu="selectMenu" :menu-list="menuList" :operate-menu="operateMenu" />
          </el-menu>
        </el-scrollbar>
      </div>
    </el-aside>
    <el-container>
      <el-header style="height: 44px">
        <ToolBarLeft />
        <ToolBarRight />
      </el-header>
      <Main />
    </el-container>
  </el-container>
</template>

<script lang="ts" name="layoutVertical" setup>
/**
 * @file 默认系统布局
 * <AUTHOR>
 * @date 2024/10/31
 */
import { computed, ref } from "vue";
import { useRoute } from "vue-router";
import { useAuthStore } from "@/stores/modules/auth";
import { useGlobalStore } from "@/stores/modules/global";
import Main from "@/layouts/components/Main/index.vue";
import ToolBarLeft from "@/layouts/components/Header/ToolBarLeft.vue";
import ToolBarRight from "@/layouts/components/Header/ToolBarRight.vue";
import SubMenu from "@/layouts/components/Menu/SubMenu.vue";
import { COPILOT_URL } from "@/config";
import router from "@/routers";

const selectMenu = ref();
// 执行的操作
const operateMenu = ref();
const title = import.meta.env.VITE_GLOB_APP_TITLE;
const emit = defineEmits(["activeMenuValue", "operateMenuValue"]);

const route = useRoute();
const authStore = useAuthStore();
const globalStore = useGlobalStore();

// 控制无人中台显示
const isCopilot = computed(() => {
  // 找到menuCode为copilot的一项判断resourceFunction是否包含view字符
  const copilot = authStore.originAuthMenuList.find(item => item.menuCode === "copilot");
  return copilot ? copilot.resourceFunction.includes("view") : false;
});
const accordion = computed(() => globalStore.accordion);
// 动态折叠菜单
// const isCollapse = computed(() => globalStore.isCollapse);
const menuList = computed(() => authStore.showMenuListGet);
// 处理图标的激活态事件
const handleSelect = (index: string, operate: "open" | "close") => {
  selectMenu.value = index;
  operateMenu.value = operate;
};
const activeMenu = computed(() => (route.meta.activeMenu ? route.meta.activeMenu : route.path) as string);
const toCopilot = () => {
  router.replace(COPILOT_URL);
};
</script>

<style lang="scss" scoped>
@import "./index.scss";

.menu-button {
  max-width: 144px;
  width: 100%;
  --el-button-bg-color: var(--el-menu-bg-color);
  --el-button-text-color: #fff;
  --el-button-hover-bg-color: var(--el-menu-bg-color);
  --el-button-border-color: rgba(255, 255, 255, 0.2);
  margin: 16px auto;
  cursor: pointer;
}
</style>
