import router from "@/routers/index";
import { LOGIN_URL } from "@/config";
import { RouteRecordRaw } from "vue-router";
import { ElNotification } from "element-plus";
import { useUserStore } from "@/stores/modules/user";
import { useAuthStore } from "@/stores/modules/auth";

// 引入 views 文件夹下所有 vue 文件
const modules = import.meta.glob("@/views/**/*.vue");

/**
 * @description 初始化动态路由
 */
export const initDynamicRouter = async () => {
  const userStore = useUserStore();
  const authStore = useAuthStore();
  try {
    // 1.获取菜单列表接口调用 && 按钮权限列表接口调用
    await authStore.getAuthMenuList();
    // await authStore.getAuthButtonList();
    // 2.判断当前用户有没有菜单权限
    if (!authStore.authMenuListGet.length) {
      ElNotification({
        title: "无权限访问",
        message: "当前账号无任何菜单权限，请联系系统管理员！",
        type: "warning",
        duration: 3000
      });
      userStore.setToken("");
      await router.replace(LOGIN_URL);
      return Promise.reject("No permission");
    }
    // console.log("路由", authStore.authMenuList);

    // 3.添加动态路由
    authStore.flatMenuListGet.forEach(item => {
      item.children && delete item.children;
      if (item.component && typeof item.component == "string") {
        item.component = modules["/src/views" + item.component + ".vue"];
      }
      // 全屏显示作为顶级路由,如3D大屏
      if (item.meta?.isFull) {
        router.addRoute(item as unknown as RouteRecordRaw);
      } else {
        // 普通路由
        router.addRoute("layout", item as unknown as RouteRecordRaw);
      }
    });

    // 获取路由
    // console.log("处理后的路由", router.getRoutes());
    // console.log("路由", authStore.authMenuList);
    // console.log("扁平化之后的一维数组菜单", authStore.flatMenuListGet);
  } catch (error) {
    // 当按钮 || 菜单请求出错时，重定向到登陆页
    userStore.setToken("");
    router.replace(LOGIN_URL);
    return Promise.reject(error);
  }
};
