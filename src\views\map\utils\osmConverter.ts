import osmtogeojson from "osmtogeojson";
import { Feature, FeatureCollection } from "geojson";

/**
 * OSM标签接口
 */
interface OsmTags {
  [key: string]: string;
}

/**
 * OSM元素类型
 */
export enum OsmElementType {
  NODE = "node",
  WAY = "way",
  RELATION = "relation"
}

/**
 * OSM元素接口
 */
interface OsmElement {
  type: OsmElementType;
  id: number;
  tags?: OsmTags;
  // 其他可能的属性
  [key: string]: any;
}

/**
 * 转换结果接口
 */
export interface ConversionResult {
  geojson: FeatureCollection;
  blobUrl: string;
}

/**
 * OSM转换器类
 */
export class OsmConverter {
  /**
   * 转换OSM文件为GeoJSON
   * @param osmContent OSM文件内容
   * @returns 转换结果
   */
  public static convert(osmContent: string): ConversionResult {
    // 解析OSM XML
    let osmData = this.parseOsmXml(osmContent);
    // osmData =

    const parkingSpaceNodes = this.findParkingSpaceNodes(osmData);
    // 转换为GeoJSON
    const geojson = osmtogeojson(osmData, {
      polygonFeatures({ type, area }) {
        if (area === "yes") return true;
        return ["pedestrian_marking"].includes(type);
      }
    }) as FeatureCollection;

    const newGeojson = this.processParkingSpaceNodes(
      geojson,
      parkingSpaceNodes.map(node => node.polygon)
    );

    this.addOtherRelations(osmData, newGeojson);

    // 处理每个Feature的属性
    // geojson.features.forEach(feature => {
    //   const osmElement = feature.properties as OsmElement;
    //   this.processFeatureProperties(feature, osmElement);
    // });
    return {
      geojson: newGeojson,
      blobUrl: URL.createObjectURL(new Blob([JSON.stringify(newGeojson)]))
    };
  }

  /**
   * 获取OSM文件内容
   * @param url OSM文件URL
   * @returns OSM文件内容
   */
  public static urlToContent(url: string) {
    return fetch(url).then(response => response.text());
  }

  /**
   * 获取要素的样式建议
   * @param feature GeoJSON Feature
   * @returns 样式建议
   */
  public static getSuggestedStyle(feature: Feature) {
    const featureType = feature.properties?.featureType;

    const styles = {
      building: {
        color: "#BC8F8F",
        fillOpacity: 0.6,
        weight: 1
      },
      highway: {
        color: "#A9A9A9",
        weight: 2
      },
      water: {
        color: "#4682B4",
        fillOpacity: 0.5,
        weight: 1
      },
      green: {
        color: "#228B22",
        fillOpacity: 0.4,
        weight: 1
      },
      boundary: {
        color: "#800080",
        weight: 2,
        dashArray: "5, 5"
      },
      other: {
        color: "red",
        weight: 1
      }
    };

    return styles[featureType] || styles.other;
  }

  /**
   * 查找所有停车位相关的节点引用
   * @param osmDoc OSM XML文档
   * @returns 停车位节点引用ID数组
   */
  public static findParkingSpaceNodes(
    osmDoc: Document
  ): { width: number; nodes: { lat: number; lng: number; ele: number }[]; polygon: Feature }[] {
    // 查找所有type=parking_space的way元素
    const parkingWays = osmDoc.querySelectorAll('way tag[k="type"][v="parking_space"]');
    const nodeIds: string[] = [];
    const nodeObjects: { [key: string]: { lat: number; lng: number; ele: number } } = {};
    const elements = Array.from(parkingWays)
      .map(tag => {
        const wayElement = tag.parentElement;
        if (!wayElement) return null;
        // 获取way元素下的所有nd节点引用
        const ndRefs = wayElement.querySelectorAll("nd");
        const nodes = Array.from(ndRefs).map(nd => nd.getAttribute("ref")!);
        nodeIds.push(...nodes);
        return {
          width: +wayElement.querySelector("tag[k='width']")?.getAttribute("v")!,
          nodes,
          id: wayElement.getAttribute("id")!
        };
      })
      .filter(Boolean) as { width: number; nodes: string[]; id: string }[];
    const selector = nodeIds.map(id => `node[id="${id}"]`).join(",");
    if (!selector.trim()) return [];
    osmDoc.querySelectorAll(selector).forEach(node => {
      const nodeElement = node as Element;
      const nodeId = nodeElement.getAttribute("id")!;
      nodeObjects[nodeId] = {
        lat: +nodeElement.getAttribute("lat")!,
        lng: +nodeElement.getAttribute("lon")!,
        ele: +nodeElement.querySelector("tag[k='ele']")?.getAttribute("v")!
      };
    });
    return elements.map(element => {
      const nodes = element.nodes.map(nodeId => nodeObjects[nodeId]);
      return {
        width: element.width,
        nodes,
        polygon: this.lineStringToPolygon(nodes[0], nodes[1], element.width, { id: `parking_space_${element.id}` })
      };
    });
  }

  /**
   * 解析OSM XML字符串
   * @param osmXml OSM XML字符串
   * @returns Document
   */
  private static parseOsmXml(osmXml: string): Document {
    const parser = new DOMParser();
    return parser.parseFromString(osmXml, "text/xml");
  }

  private static getWaysCoordinates(geojson: FeatureCollection) {
    const waysCoordinates = {};
    geojson.features.forEach(feature => {
      const [prefix, wayId] = feature.id?.toString().split("/") || [];
      if (prefix !== "way") return;
      if (feature.geometry.type === "LineString") {
        waysCoordinates[wayId] = feature.geometry.coordinates;
      }
    });
    return waysCoordinates;
  }

  private static mergeToPolygon(way1: number[][], way2: number[][]): number[][] {
    // 1. 计算各个端点之间的距离
    const distances = {
      start_start: this.getDistance(way1[0], way2[0]),
      start_end: this.getDistance(way1[0], way2[way2.length - 1]),
      end_start: this.getDistance(way1[way1.length - 1], way2[0]),
      end_end: this.getDistance(way1[way1.length - 1], way2[way2.length - 1])
    };

    // 2. 找出最小距离的连接方式
    const minDistance = Math.min(...Object.values(distances));

    let coordinates: number[][];

    if (minDistance === distances.start_start) {
      // way1反转 + way2
      coordinates = [...way1.reverse(), ...way2];
    } else if (minDistance === distances.start_end) {
      // way1反转 + way2反转
      coordinates = [...way1.reverse(), ...way2.reverse()];
    } else if (minDistance === distances.end_start) {
      // way1 + way2
      coordinates = [...way1, ...way2];
    } else {
      // way1 + way2反转
      coordinates = [...way1, ...way2.reverse()];
    }

    // 3. 添加首点以闭合多边形
    coordinates.push([...coordinates[0]]);

    return coordinates;
  }

  private static getDistance(point1: number[], point2: number[]): number {
    const dx = point1[0] - point2[0]; // 经度差
    const dy = point1[1] - point2[1]; // 纬度差
    return Math.sqrt(dx * dx + dy * dy);
  }

  private static addOtherRelations(osmDoc: Document, geojson: FeatureCollection) {
    const relations = osmDoc.querySelectorAll("relation");
    if (!relations) return geojson;
    // 存储点的海拔信息，用于最终计算relation的海拔信息
    const nodeElevations = {};
    Array.from(osmDoc.querySelectorAll("node")).forEach(node => {
      const nodeId = node.getAttribute("id")!;
      const elevation = +node.querySelector("tag[k='ele']")?.getAttribute("v")!;
      nodeElevations[nodeId] = +elevation;
    });
    const waysElevations = {};
    Array.from(osmDoc.querySelectorAll("way")).forEach(way => {
      const wayId = way.getAttribute("id")!;
      const elevations = Array.from(way.querySelectorAll("nd")).map(nd => nodeElevations[nd.getAttribute("ref") ?? 0]);
      const elevation = elevations.reduce((a, b) => +a + +b, 0) / elevations.length;
      waysElevations[wayId] = elevation;
    });

    const otherRelations: {
      [key: string]: {
        ways: [string, string];
        properties: OsmTags;
      };
    } = {};
    const waysCoordinates = this.getWaysCoordinates(geojson);
    relations.forEach(relation => {
      const type = relation.querySelector("tag[k='type']")?.getAttribute("v");
      if (type === "multipolygon" || type === "regulatory_element") return;
      relation.querySelector("tag[k='type']")?.setAttribute("v", "multipolygon");
      const members = relation.querySelectorAll("member[type='way']");
      if (members.length !== 2) return;
      const ways = Array.from(members).map(member => member.getAttribute("ref")!) as [string, string];
      const elevation = ways.reduce((acc, wayId) => acc + waysElevations[wayId], 0) / ways.length;
      const properties = Array.from(relation.querySelectorAll("tag")).reduce((acc, tag) => {
        acc[tag.getAttribute("k")!] = tag.getAttribute("v")!;
        return acc;
      }, {} as OsmTags);
      otherRelations[relation.getAttribute("id")!] = {
        ways,
        properties: { ...properties, ele: Number.parseFloat(elevation.toFixed(2)).toString() }
      };
    });

    const polygons = Object.entries(otherRelations).map(([relationId, { ways, properties }]) => {
      const way1 = waysCoordinates[ways[0]];
      const way2 = waysCoordinates[ways[1]];
      let coordinates = this.mergeToPolygon(way1, way2);

      return {
        type: "Feature",
        id: `added/${relationId}`,
        properties: {
          ...properties,
          id: `added/${relationId}`,
          stroke: "none"
        },
        geometry: {
          type: "Polygon",
          coordinates: [coordinates]
        }
      };
    });
    geojson.features.push(...(polygons as Feature[]));
  }

  /**
   * 处理Feature属性
   * @param feature GeoJSON Feature
   * @param osmElement OSM元素
   */
  private static processFeatureProperties(feature: Feature, osmElement: OsmElement) {
    // console.log("feature", feature);
    // if (feature?.geometry?.type === "LineString") {
    //   feature.geometry.type = "Polygon";
    // }
    if (!feature.properties) {
      feature.properties = {};
    }

    // 保存OSM元素类型
    feature.properties.osmType = osmElement.type;

    // 保存OSM ID
    feature.properties.osmId = osmElement.id;

    // 保存所有标签
    if (osmElement.tags) {
      Object.entries(osmElement.tags).forEach(([key, value]) => {
        feature.properties![`tag_${key}`] = value;
      });
    }

    // 添加图形类型标识
    feature.properties.geometryType = feature.geometry.type;
    feature.properties.style = this.getSuggestedStyle(feature);

    // 根据标签推断要素类型
    // feature.properties.featureType = this.inferFeatureType(osmElement.tags || {});
  }

  private static lineStringToPolygon(
    start: { lat: number; lng: number; ele: number },
    end: { lat: number; lng: number; ele: number },
    width: number,
    properties: OsmTags
  ): Feature {
    // 地球半径（米）
    const EARTH_RADIUS = 6378137;

    // 将宽度（米）转换为经度偏移量
    // 经度1度对应的距离 = 2π * R * cos(lat) / 360
    const metersPerLngDegree = (2 * Math.PI * EARTH_RADIUS * Math.cos((start.lat * Math.PI) / 180)) / 360;
    // 纬度1度对应的距离约为111319.5米
    const metersPerLatDegree = 111319.5;

    // 计算经纬度偏移量
    const latOffset = width / 2 / metersPerLatDegree;
    const lngOffset = width / 2 / metersPerLngDegree;

    // 计算线段的方向向量
    const dx = end.lng - start.lng;
    const dy = end.lat - start.lat;
    const length = Math.sqrt(dx * dx + dy * dy);

    // 计算单位法向量（垂直于线段）
    const nx = (-dy / length) * lngOffset;
    const ny = (dx / length) * latOffset;

    // 计算四个角点
    const p1 = {
      lat: start.lat + ny,
      lng: start.lng + nx,
      ele: start.ele
    };
    const p2 = {
      lat: end.lat + ny,
      lng: end.lng + nx,
      ele: end.ele
    };
    const p3 = {
      lat: end.lat - ny,
      lng: end.lng - nx,
      ele: end.ele
    };
    const p4 = {
      lat: start.lat - ny,
      lng: start.lng - nx,
      ele: start.ele
    };

    // 返回GeoJSON格式的多边形
    return {
      type: "Feature",
      id: properties.id.toString(),
      properties: {
        type: "parking_space",
        version: "1",
        width: width.toString(),
        id: properties.id.toString()
      },
      geometry: {
        type: "Polygon",
        coordinates: [
          [
            [p1.lng, p1.lat],
            [p2.lng, p2.lat],
            [p3.lng, p3.lat],
            [p4.lng, p4.lat],
            [p1.lng, p1.lat] // 闭合多边形
          ]
        ]
      }
    };
  }

  private static processParkingSpaceNodes(geojson: FeatureCollection, parkingSpaceNodes: Feature[]) {
    geojson.features = geojson.features.filter(feature => feature.properties?.type !== "parking_space");
    geojson.features.push(...parkingSpaceNodes);
    return geojson;
  }
}
