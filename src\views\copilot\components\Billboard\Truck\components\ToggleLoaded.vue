<template>
  <MessageBox
    ref="messageBoxRef"
    v-bind="messageProps"
    :beforeClose="handleBeforeClose"
    @confirm="handleConfirm"
    @cancel="handleCancel"
    :messageBox="false"
    width="584px"
    class="task-creator-dialog"
    dialogContentStyle="display: flex; flex-direction: column; align-items: center; justify-content: center; --el-border-color: #6768BF; margin-top: inherit; --el-color-danger: var(--el-color-secondary)"
  >
    <el-form :model="formData" :rules="rules" ref="formRef" style="width: 100%" label-width="120px">
      <el-form-item label="任务类型" prop="materialType">
        <el-select
          v-model="formData.materialType"
          placeholder="请选择物料类型"
          size="large"
          popper-class="add-to-task-message-box"
          style="width: 100%"
        >
          <el-option v-for="item of materialTypeOptions" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
    </el-form>
  </MessageBox>
</template>

<script lang="ts" setup>
import { ref, reactive } from "vue";
import MessageBox from "../../../MessageBox.vue";
import { useDeviceSelection } from "@/views/copilot/store";
import { getBulldozersList, addDispatchTask } from "@/api/modules/copilot";
import type { FormInstance, FormRules } from "element-plus";

interface TaskOption {
  name: string;
  id: string;
}

const { selectedDeviceInfo } = useDeviceSelection();
const messageBoxRef = ref();
const messageProps = ref({ title: `切为重车-${selectedDeviceInfo?.value?.rawData?.name}`, message: "" });

const materialTypeOptions = ref<TaskOption[]>([
  { name: "卸载", id: "unload" },
  { name: "充电", id: "charge" },
  { name: "回场", id: "return" }
]);
// 任务列表
const tasks = ref<TaskOption[]>([]);
const formRef = ref<FormInstance>();
const formData = reactive({ materialType: "", unloadPoint: "", remark: "" });

// 表单验证规则
const rules = reactive<FormRules>({
  materialType: [{ required: true, message: "请选择物料类型", trigger: "change" }]
});

// 获取任务列表
const getTasks = async () => {
  const response = (await getBulldozersList().then(res => res?.data || [])) as TaskOption[];
  if (response) {
    tasks.value = response;
  }
};

const handleBeforeClose = () => {
  return new Promise<string | boolean>(async resolve => {
    formRef
      .value!.validate()
      .then(() => resolve(true))
      .catch(() => resolve(false));
  });
};

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields();
  }
  formData.materialType = "";
  formData.unloadPoint = "";
  formData.remark = "";
};

const handleConfirm = async () => {
  if (!formRef.value) return;

  try {
    await addDispatchTask({
      trainId: selectedDeviceInfo.value?.id,
      bulldozersId: formData.materialType === "unload" ? formData.unloadPoint : formData.materialType,
      remark: formData.remark
    });
    resetForm();
    messageBoxRef.value.close();
  } catch (error) {
    console.log("error submit!", error);
  }
};

const handleCancel = () => {
  resetForm();
};

const show = () => {
  messageBoxRef.value.show();
  getTasks();
};

// 暴露方法给父组件
defineExpose({ show });
</script>
<style lang="scss">
.add-to-task-message-box {
  --el-bg-color-overlay: #3b3ca7;
  --el-box-shadow-light: 0px 0px 12px #252688;
  --el-fill-color-light: #2e2f9a;
  box-shadow: none !important;
}
.task-creator-dialog {
  .tip-text {
    color: #fff;
    font-size: 14px;
    padding: 10px 0;
    text-align: center;
  }
}
</style>
