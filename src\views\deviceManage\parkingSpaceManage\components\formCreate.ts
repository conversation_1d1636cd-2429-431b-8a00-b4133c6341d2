/**
 * @file 设备管理-停车位管理-表单创建文件
 * <AUTHOR>
 * @date 2024/11/19
 */
import { formMap } from "../types";
import LASelect from "@/components/LASelect";

import { getAllChargingPileListMap } from "@/api/modules/device";

export const parkingSpaceManageFormCreate = {
  type: "form-create",
  props: {
    option: {
      row: { gutter: 24 },
      global: { "*": { col: { span: 12 } } },
      submitBtn: false,
      onSubmit(formData, api) {
        // 通知 table 搜索数据变化，刷新数据
        api.top.bus.$emit("searchFormChanged");
      }
    },
    rule: [
      {
        type: "input",
        field: formMap.get("停车位名称"),
        title: "停车位名称",
        validate: [
          { required: true, message: "请输入停车位名称" },
          {
            pattern: /^.{1,20}$/,
            message: "字符限长20位"
          }
        ]
      },
      {
        type: "input",
        field: formMap.get("停车位编码"),
        title: "停车位编码",
        validate: [{ required: true, message: "请输入停车位编码" }]
      },
      // LASelect组件式
      {
        component: LASelect,
        field: formMap.get("对应充电桩"),
        props: {
          replaceFields: { key: "id", label: "name", value: "id" },
          filterFetch: (row, val, list, api) => {
            // 过滤已经有spotName且row.id不是val的项
            const filteredList = list.filter((item: any) => {
              return item.id === val || !item.spotName;
            });
            return filteredList;
          },
          fetch: getAllChargingPileListMap
        },
        title: "对应充电桩"
      },
      {
        type: "input",
        field: formMap.get("经度"),
        title: "经度"
      },
      {
        type: "input",
        field: formMap.get("纬度"),
        title: "纬度"
      },
      {
        type: "input",
        field: formMap.get("海拔"),
        title: "海拔",
        children: [{ type: "div", slot: "suffix", children: ["米"] }]
      },
      {
        type: "input",
        field: formMap.get("朝向"),
        title: "朝向"
      }
    ]
  }
};
// 状态更改
export const parkingSpaceManageStatusFormCreate = {
  type: "form-create",
  props: {
    option: {
      submitBtn: false
    },
    rule: [
      {
        type: "radio",
        title: "状态",
        field: "status",
        validate: [{ required: true, message: "请选择状态" }],
        options: [
          { value: 0, label: "未占用" },
          { value: 1, label: "已占用" }
        ]
      }
    ]
  }
};
