<script setup lang="ts">
import logo from "@/assets/images/logo.svg?url";
import { ref, computed } from "vue";
import { useUserStore } from "@/stores/modules/user";
import { useRouter } from "vue-router";
const userStore = useUserStore();
// 获取个人名称
const employeeName = computed(() => userStore.userInfo.employeeName);
defineProps<{ title?: string }>();
const router = useRouter();
const flowValue = ref(100);
</script>

<template>
  <header class="base-header">
    <slot name="left">
      <img :src="logo" alt="logo" class="header-logo" style="width: 198px" @click="router.push('/')" />
      <div class="header-divider"></div>
      <h1 class="header-title">{{ title || "5G远程应急管理系统驾驶舱" }}</h1>
      <!-- 剩余流量 -->
      <div class="header-flow">
        <div class="flow-value" :style="{ width: flowValue * 0.92 + '%' }"></div>
        <div class="flow-text">
          <div class="header-flow-left-title">剩余流量</div>
          <div class="header-flow-left-value">100GB</div>
        </div>
      </div>
    </slot>
    <slot name="right">
      <div class="tool-bar-ri">
        <div class="avatar">
          <img alt="avatar" src="@/assets/images/avatar.gif" />
        </div>
        <span class="username">{{ employeeName }}</span>
      </div>
    </slot>
  </header>
</template>

<style scoped>
.base-header {
  padding: 0 20px;
  height: 56px;
  display: flex;
  align-items: center;
  gap: 10px;
}
.header-logo {
  margin-top: -10px;
  cursor: pointer;
}
.header-title {
  font-size: 21px;
  font-weight: 500;
  color: #fff;
  margin: 0;
}

.header-divider {
  width: 1px;
  height: 18px;
  background-color: rgba(255, 255, 255, 0.1);
}
.header-flow {
  width: 120px;
  height: 32px;
  background: #040536;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;

  font-size: 14px;
  color: #fff;
  position: relative;
  .flow-value {
    position: absolute;
    top: 4px;
    left: 4px;
    width: 0%;
    height: 24px;
    background: rgba(53, 106, 253, 1);
    border-radius: 8px;
  }
  .flow-text {
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2;
  }
}

.tool-bar-ri {
  margin-left: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  .header-icon {
    display: flex;
    align-items: center;
    & > * {
      margin-left: 21px;
      color: var(--el-header-text-color);
    }
  }
  .arrow-down {
    color: var(--el-header-text-color);
  }
  .username {
    margin: 0 4px;
    font-size: 14px;
    color: var(--el-header-text-color);
  }

  .avatar {
    width: 40px;
    height: 40px;
    overflow: hidden;
    cursor: pointer;
    border-radius: 50%;
    img {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
