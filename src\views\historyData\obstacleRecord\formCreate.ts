// 障碍物记录 地图位置显示组件
import MapBox from "@/views/map/index.vue";
import mittBus from "@/utils/mittBus";
export const obstacleRecordForm = {
  type: "form-create",
  name: "ObstacleRecord",
  style: { height: "100%" },
  on: {
    mounted(api) {
      const timer = setTimeout(() => {
        clearTimeout(timer);
        const obstacle = api.formData();
        mittBus.emit("obstacleLocation", obstacle);
      }, 1000);
    }
  },
  props: {
    option: {
      submitBtn: false,
      resetBtn: false
    },
    rule: [
      {
        component: MapBox,
        props: {
          data: {}
        }
      }
    ]
  }
};
