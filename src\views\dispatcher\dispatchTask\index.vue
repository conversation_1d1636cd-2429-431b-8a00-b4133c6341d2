<template>
  <form-create v-model:api="formApi" :option="option" :rule="rule" />
</template>
<script lang="tsx" setup>
/**
 * @file 调度管理-调度任务
 * <AUTHOR>
 * @date 2024/12/9
 */
import { computed, ref } from "vue";
import formCreate from "@form-create/element-ui";
import { columnMap, StatusEnum, TaskStatusEnum } from "@/views/dispatcher/dispatchTask/types";
import LASelect from "@/components/LASelect";
import TaskDetails from "./components/TaskDetails.vue";
import {
  closeDispatchTask,
  continueDispatchTask,
  getAllMaterial,
  getDispatchPlanListByMap,
  waitDispatchTask
} from "@/api/modules/dispatch";

// 任务明细组件
formCreate.component("TaskDetails", TaskDetails);
// 保存表格选项数据
const selData: any = ref([]);

const isContinueDisabled = computed(() => {
  // 只有暂停中才显示
  return selData.value.length === 0 || selData.value?.some((item: any) => item.status !== StatusEnum.PROJECT_WAIT);
});
// 只有暂停中和执行中才显示 如果有其他状态不显示
const isCloseDisabled = computed(() => {
  return (
    selData.value.length === 0 ||
    !selData.value?.every(
      (item: any) => item.status === StatusEnum.PROJECT_IN_PROGRESS || item.status === StatusEnum.PROJECT_WAIT
    )
  );
});
// 只有执行中才显示
const isWaitDisabled = computed(() => {
  return selData.value.length === 0 || selData.value?.some((item: any) => item.status !== StatusEnum.PROJECT_IN_PROGRESS);
});
const option = {
  resetBtn: false,
  submitBtn: false,
  form: { inline: true }
};
const formApi = ref();
const rule = ref([
  {
    type: "SearchFormOperation",
    field: "v:search",
    wrap: { style: "marginBottom: 0px" },
    style: {
      height: "32px !important",
      width: "100%",
      flexWrap: "nowrap"
    },
    children: [
      {
        component: LASelect,
        field: "operateDeck",
        style: { width: "200px", lineHeight: "initial" },
        props: {
          placeholder: "任务类型",
          list: [
            {
              label: "装卸",
              value: "LoadingTasks"
            }
          ]
        }
      },
      {
        component: LASelect,
        field: "status",
        style: { width: "200px", lineHeight: "initial" },
        props: {
          placeholder: "状态",
          list: [
            { label: "未开始", value: 0 },
            { label: "执行中", value: 1 },
            { label: "已结束", value: 2 },
            { label: "暂停中", value: 3 },
            { label: "已取消", value: 4 },
            { label: "待结束", value: 5 }
          ]
        }
      },
      {
        component: LASelect,
        field: "materialId",
        style: { width: "200px", lineHeight: "initial" },
        props: {
          fetch: getAllMaterial,
          replaceFields: { key: "id", label: "name", value: "id" },
          placeholder: "物料类型"
        }
      },
      {
        type: "input",
        field: "search",
        props: {
          size: "default",
          placeholder: "任务编号/铲点/卸点"
        }
      },
      {
        type: "ConfirmDialog",
        on: {
          // 监听弹窗组件抛出的的afterSubmit事件，用于刷新页面
          afterSubmit: () => {
            // 刷新，调用组件内部请求方法
            formApi.value!.exec("v:search", "onSearch");
            // 操作后,取消勾选
            formApi!.value.el("v:table").clearSelection();
          }
        },
        slot: "suffix",
        props: {
          auth: "update",
          disabled: isWaitDisabled,
          buttonText: "暂停",
          title: "是否暂停任务",
          message: "继续后,系统将会暂停向本任务派遣矿卡",
          // 模拟请求param：参数
          submitRequest: () => {
            // 调用组件实例方法
            const ids = formApi!.value.el("v:table").selectedListIds.join(",");
            return waitDispatchTask({ projectDetailsIds: ids });
          }
        }
      },
      {
        type: "ConfirmDialog",
        on: {
          // 监听弹窗组件抛出的的afterSubmit事件，用于刷新页面
          afterSubmit: () => {
            // 刷新，调用组件内部请求方法
            formApi.value!.exec("v:search", "onSearch"); // 操作后,取消勾选
            formApi!.value.el("v:table").clearSelection();
          }
        },
        slot: "suffix",
        props: {
          auth: "update",
          disabled: isContinueDisabled,
          buttonText: "继续",
          title: "是否继续任务",
          message: "继续后,系统将会继续向本任务派遣矿卡",
          // 模拟请求param：参数
          submitRequest: () => {
            // 调用组件实例方法
            const ids = formApi!.value.el("v:table").selectedListIds.join(",");

            // 模拟提交的请求为2秒
            return continueDispatchTask({ projectDetailsIds: ids });
          }
        }
      },
      {
        type: "ConfirmDialog",
        on: {
          // 监听弹窗组件抛出的的afterSubmit事件，用于刷新页面
          afterSubmit: () => {
            // 刷新，调用组件内部请求方法
            formApi.value!.exec("v:search", "onSearch");
            // 操作后,取消勾选
            formApi!.value.el("v:table").clearSelection();
          }
        },
        slot: "suffix",
        props: {
          auth: "update",
          disabled: isCloseDisabled,
          buttonText: "结束",
          title: "是否结束任务",
          message: "结束后,本任务会变成已结束状态",
          // 模拟请求param：参数
          submitRequest: () => {
            // 调用组件实例方法
            // console.log(formApi!.value.el("v:table").selectedListIds);
            const ids = formApi!.value.el("v:table").selectedListIds.join(",");

            // 模拟提交的请求为2秒
            return closeDispatchTask({ projectDetailsIds: ids });
          }
        }
      }
    ],
    on: {
      // 重置时清除选中，隐藏数据
      reset: () => {
        const api = formApi.value!;
        // 获取到表格实例设置选中为空
        api.el("v:table").element.setCurrentRow();
        console.log(selData.value);
      }
    }
  },
  {
    type: "ProTable",
    field: "v:table",
    wrap: {
      style: {
        margin: "0",
        width: "100%",
        height: "45%"
      }
    },
    props: {
      pagination: false,
      highlightCurrentRow: true,
      columns: [
        { type: "selection", label: "计划编号" },
        { prop: columnMap.get("任务编号"), label: "计划编号" },
        { prop: columnMap.get("任务类型名称"), label: "任务类型" },
        {
          prop: columnMap.get("状态"),
          label: "状态",
          tag: true,
          enum: [...TaskStatusEnum]
        },
        { prop: columnMap.get("铲点"), label: "铲点" },
        { prop: columnMap.get("卸点"), label: "卸点" },
        { prop: columnMap.get("物料类型"), label: "物料类型" },
        { prop: columnMap.get("开始时间"), label: "开始时间" }
      ],
      fetch: getDispatchPlanListByMap
    },
    on: {
      // 点击高亮的方法
      currentChange: row => {
        const api = formApi.value!;
        const subTableTitle = api.findRule({ name: "sub-title" });
        subTableTitle.children[0] = `任务明细${row?.number ? "-" + row?.number : ""}`;
        const subTable = api.findRule({ name: "sub-table" });
        subTable.props.data = row;
      },
      // 勾选的方法
      selectionChange: (row: any) => {
        selData.value = row;
      }
    }
  },
  {
    type: "LACard",
    style: {
      padding: 0,
      borderRadius: "10px",
      overflow: "auto"
    },
    props: { showHeader: false, shadow: "never" },
    children: [
      {
        type: "div",
        name: "sub-title",
        children: ["任务明细"],
        style: {
          fontWeight: "bold",
          padding: "20px",
          fontSize: "18px",
          lineHeight: "18px",
          color: "rgba(26, 26, 26, 1)"
        }
      },
      // 下方任务明细
      {
        type: "TaskDetails",
        name: "sub-table"
        // hidden: true,
      }
    ]
  }
]);
</script>
<style lang="scss" scoped>
:deep(.el-row) {
  height: 100%;
  overflow: hidden;
  justify-content: space-between;
  flex-direction: column;
  .el-select--large .el-select__wrapper {
    min-height: initial;
  }

  .cell {
    padding-left: 7px;
  }

  .el-table-column--selection {
    margin-right: 7px;

    .cell {
      padding-left: 20px;
    }
  }

  .el-button--small {
    height: 32px;
  }

  .el-card__body {
    padding: 0;
  }
}
</style>
