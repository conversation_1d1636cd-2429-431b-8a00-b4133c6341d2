<template>
  <form-create v-model:api="fApi" :option="option" :rule="rule"></form-create>
</template>

<script lang="ts" setup>
/**
 * @file 历史数据-障碍物记录
 * <AUTHOR>
 * @date 2025/7/22
 */
import { ref } from "vue";
import { columnMap, obstacleRecordStatusEnum, StatusEnum } from "./types";
import { getObstacleRecordList } from "@/api/modules/historyData";
import { obstacleRecordForm } from "./formCreate";

import formCreate from "@form-create/element-ui";
import LASelect from "@/components/LASelect.tsx";

const fApi = ref();
const option = {
  form: { inline: true },
  resetBtn: false,
  submitBtn: false
};

const rule = ref([
  {
    type: "SearchFormOperation",
    field: "v:search",
    wrap: { style: "marginBottom: 0" },
    children: [
      {
        component: LASelect,
        field: "status",
        style: { width: "200px", lineHeight: "initial" },
        props: {
          list: [
            { label: "未清除", value: StatusEnum.UNCLEARED },
            { label: "已清除", value: StatusEnum.CLEARED }
          ],
          placeholder: "状态"
        }
      },
      {
        type: "LADateTimeRangePicker",
        name: "time",
        style: { lineHeight: "initial", height: "32px" },
        props: {
          type: "daterange",
          format: "YYYY-MM-DD",
          placeholder: ["起始日期", "截止日期"]
        },
        on: {
          "update:start": val => {
            if (val) {
              fApi.value.form["startTime"] = val;
            } else {
              fApi.value.form["startTime"] = undefined;
            }
          },
          "update:end": val => {
            if (val) {
              fApi.value.form["endTime"] = val;
            } else {
              fApi.value.form["endTime"] = undefined;
            }
          }
        }
      }
    ]
  },
  {
    type: "ProTable",
    props: {
      columns: [
        { prop: columnMap.get("记录时间"), label: "记录时间" },
        {
          prop: columnMap.get("状态"),
          label: "状态",
          tag: true,
          enum: [...obstacleRecordStatusEnum]
        },
        { prop: columnMap.get("清除时间"), label: "清除时间" },
        { prop: "operation", label: "操作", fixed: "right" }
      ],
      fetch: getObstacleRecordList,

      operations: [{ content: "地图位置", action: "mapLocation", auth: "mapLocation" }]
    },
    children: [
      {
        type: "EditBtn",
        props: {
          action: "mapLocation",
          dialog: { title: "地图位置", footer: false, style: { width: "1080px", height: "720px" } }
        },
        children: [obstacleRecordForm]
      }
    ]
  }
]);
</script>
<style lang="scss" scoped>
:deep(.el-row) {
  height: calc(100vh - 138px) !important;
  .el-select--large .el-select__wrapper {
    min-height: initial;
  }
}

.el-form-item {
  margin-right: 4px !important;
}
</style>
