<template>
  <div 
    v-if="visible && vehicleStatus"
    class="vehicle-tooltip"
    :style="tooltipStyle"
  >
    <!-- 矿车信息 -->
    <div v-if="vehicleType === 'truck'" class="tooltip-content">
      <div class="vehicle-name">{{ vehicleStatus.name }}</div>
      <div class="vehicle-info">
        <span v-if="vehicleStatus.heavy !== undefined" class="load-status">
          {{ vehicleStatus.heavy ? '重车' : '空车' }}:
        </span>
        <span class="speed">{{ vehicleStatus.speed.toFixed(1) }}km/h</span>
      </div>
      <div v-if="vehicleStatus.errorCode" class="error-code">
        故障码: {{ vehicleStatus.errorCode }}
      </div>
    </div>
    
    <!-- 挖机信息 -->
    <div v-else-if="vehicleType === 'excavator'" class="tooltip-content">
      <div class="vehicle-name">{{ vehicleStatus.name }}</div>
    </div>
    
    <!-- 箭头 -->
    <div class="tooltip-arrow"></div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface VehicleStatus {
  name: string;
  heavy?: boolean;
  speed: number;
  errorCode?: number;
}

interface Props {
  visible: boolean;
  vehicleStatus?: VehicleStatus;
  vehicleType: 'truck' | 'excavator';
  position: { x: number; y: number };
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  vehicleType: 'truck',
  position: () => ({ x: 0, y: 0 })
});

// 计算浮窗位置样式
const tooltipStyle = computed(() => ({
  left: `${props.position.x}px`,
  top: `${props.position.y - 60}px`, // 浮窗显示在图标上方
}));
</script>

<style scoped>
.vehicle-tooltip {
  position: fixed;
  z-index: 10000;
  pointer-events: none;
  transform: translateX(-50%);
}

.tooltip-content {
  background: rgba(0, 0, 0, 0.8);
  width: 130px;
  border-radius: 8px;
  box-shadow: 0px 2px 4px 1px rgba(0, 0, 0, 0.1);
  border: 1px solid #356AFD;
  padding: 8px 10px;
  color: white;
  font-size: 12px;
  line-height: 1.4;
  text-align: center;
}

.vehicle-name {
  font-weight: bold;
  margin-bottom: 4px;
  color: #ffffff;
}

.vehicle-info {
  margin-bottom: 2px;
}

.load-status {
  margin-right: 4px;
}

.speed {
  color: #ffffff;
}

.error-code {
  color: #ff6b6b;
  font-size: 11px;
  margin-top: 2px;
}

.tooltip-arrow {
  position: absolute;
  bottom: -6px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid #356AFD;
}

.tooltip-arrow::before {
  content: '';
  position: absolute;
  bottom: 1px;
  left: -5px;
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid rgba(0, 0, 0, 0.8);
}
</style>
