<template>
  <div class="search-form__operator">
    <slot />
    <el-button v-if="search !== false" size="default" type="primary" @click="onSearch">查询</el-button>
    <el-button v-if="search !== false" size="default" @click="onReset">重置</el-button>
    <slot name="suffix" />
  </div>
</template>

<script lang="ts" setup>
import { Api } from "@form-create/element-ui";

const props = defineProps<{ formCreateInject: { api: Api }; search: unknown; params?: {} }>();
const emit = defineEmits(["reset", "submit"]);

const onSearch = () => {
  emit("submit", props.params);

  const api = props.formCreateInject.api;
  api.submit(params => {
    api.bus.$emit("searchFormChanged", params);
  });
};
const onReset = async () => {
  const api = props.formCreateInject.api;
  api.resetFields();
  api.bus.$emit("resetForm");
  // 调度计划重置时使用清除选中状态
  emit("reset");
  onSearch();
};

defineExpose({
  onSearch,
  onReset
});
</script>

<style lang="scss" scoped>
.search-form__operator {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20px;
  align-items: center;
  gap: 4px;
}

:deep(.el-button + .el-button) {
  margin: 0 !important;
}

:deep(.el-form-item) {
  margin: 0 !important;
}
</style>
