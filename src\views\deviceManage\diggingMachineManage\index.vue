<template>
  <form-create v-model:api="fApi" :option="option" :rule="rule"></form-create>
</template>
<script lang="tsx" setup>
/**
 * @file 设备管理-挖机管理
 * <AUTHOR>
 * @date 2024/12/2
 */
import { ref } from "vue";
import { columnMap } from "./types";
import { diggingMachineFormCreate } from "./components/formCreate";
import formCreate from "@form-create/element-ui";
import { saveBulldozers, deleteBulldozers, getBulldozersList } from "@/api/modules/device";

const fApi = ref();
const option = {
  form: { inline: true },
  resetBtn: false,
  submitBtn: false
};

const rule = ref([
  {
    type: "SearchFormOperation",
    field: "v:search",
    wrap: { style: "marginBottom: 0" },
    children: [
      {
        type: "input",
        field: "search",
        props: {
          size: "default",
          placeholder: "挖机名称/编码"
        }
      },
      // 新增
      {
        type: "AddBtn",
        slot: "suffix",
        props: {
          btn: { content: "新增挖机", auth: "add" },
          dialog: {
            title: "新增挖机" // 绑定到弹窗根节点的样式
          },

          size: "default",
          submitRequest: saveBulldozers
        },
        children: [diggingMachineFormCreate]
      }
    ]
  },
  {
    type: "ProTable",
    props: {
      columns: [
        {
          prop: columnMap.get("名称"),
          label: "名称"
        },
        {
          prop: columnMap.get("编码"),
          label: "编码"
        },

        {
          prop: columnMap.get("绑定流量卡"),
          label: "绑定流量卡"
        },
        {
          prop: columnMap.get("最大装载率"),
          label: "最大装载率(t/h)"
        },
        { prop: "operation", label: "操作", fixed: "right" }
      ],
      fetch: getBulldozersList,
      operations: [
        { content: "修改", action: "edit", auth: "update" },
        { content: "删除", action: "delete", auth: "delete", props: { style: { color: "rgba(242, 85, 85, 1)" } } }
      ]
    },

    children: [
      // 修改
      {
        type: "EditBtn",
        props: {
          action: "edit",
          dialog: {
            title: "修改挖机"
          },
          submitRequest: saveBulldozers
        },
        children: [diggingMachineFormCreate]
      },
      // 删除
      {
        type: "ConfirmDialog",
        on: {
          // 监听弹窗组件抛出的的afterSubmit事件，用于刷新页面
          afterSubmit: () => {
            // 刷新，调用组件内部请求方法
            fApi.value.exec("v:search", "onSearch");
          }
        },
        props: {
          action: "delete",
          subtitle: row => {
            return row.code;
          },
          title: "是否删除挖机",
          message: "删除后不可恢复",
          // 模拟请求param：参数
          submitRequest: deleteBulldozers
        }
      }
    ]
  }
]);
</script>
<style lang="scss" scoped>
:deep(.el-row) {
  height: var(--page-height);
}

.el-form-item {
  margin-right: 4px !important;
}
</style>

<style lang="scss">
.dialog-custom-node {
  .el-dialog__body {
    padding: 20px !important;
  }
  margin-top: 8vh !important;
  width: 80% !important;
  .table-box {
    height: 720px !important;
    border: 1px solid #dfe6ec;
  }
}
</style>
