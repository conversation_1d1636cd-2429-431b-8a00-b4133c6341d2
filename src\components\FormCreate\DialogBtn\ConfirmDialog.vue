<!-- MessageBoxButton.vue -->
<template>
  <el-button
    :style="{ ...btn?.style }"
    v-if="buttonText"
    v-auth="auth"
    size="default"
    @click="open"
    >{{ buttonText ?? currentPageRoles[auth!] ?? auth }}</el-button
  >
</template>

<script lang="tsx" setup>
import { markRaw, ref } from "vue";
import { ElMessageBox, ElNotification } from "element-plus";
import { QuestionFilled } from "@element-plus/icons-vue";
import type { Emitter } from "mitt";
import { inject } from "vue";
import { Api } from "@form-create/element-ui";
import { useAuthStore } from "@/stores/modules/auth";

const emits = defineEmits(["after-submit", "open-dialog"]);
const authStore = useAuthStore();
const currentPageRoles = authStore.currentPageRolesGet;

const props = withDefaults(
  defineProps<{
    // 内容
    message: string;
    // 标题
    title: string;
    // 确认按钮文字
    confirmButtonText?: string;
    // 取消按钮文字
    cancelButtonText?: string;
    // 样式
    center?: boolean;
    // 页面显示按钮文字
    buttonText: string;
    formCreateInject?: any;
    data?: any;
    mittBus?: Emitter<any>;
    // 副标题
    subtitle?: (row: any) => string;
    // 权限
    auth?: string;
    // 操作
    action?: string;
    submitRequest?: (data: { [field: string]: any }, api?: Api) => Promise<any>;
    btn?: {
      style?: any;
    };
  }>(),
  {
    title: "提示",
    center: true,
    message: "确定要执行此操作吗？"
  }
);
// 传递的数据参数param
const param = ref();
// 定义一个函数，处理 formatter 的默认值
function getHandleData(row: { [key: string]: any }) {
  // 使用空对象 {} 作为默认值，避免 null 或 undefined
  const formatter = props.submitRequest || ((data: any) => data);
  // 确保 formatter 是一个函数，如果不是，给一个默认的 formatter
  return typeof formatter === "function" ? formatter(row) : (row: any) => row;
}
const mittBus = props.mittBus || inject<Emitter<any>>("mittBus");
// 接收表格操作事件
mittBus?.off(`action-${props.action}`);
mittBus?.on(`action-${props.action}`, ({ row, api }: { row: { [key: string]: any }; api: Api }) => {
  // 触发点击事件
  param.value = row;
  emits("open-dialog", param.value, props.formCreateInject);
  open();
});
// 关闭前的回调
const beforeClose = (action, instance, done) => {
  if (action === "confirm") {
    // 进行一些操作，如果一切正常则调用 done() 关闭对话框
    if (props.submitRequest) {
      instance.confirmButtonLoading = true;

      props
        .submitRequest(param.value, props?.formCreateInject?.api)
        .then(res => {
          if (res.statusCode === 101) {
            ElNotification({ title: "温馨提示", message: "提交成功！", type: "success", duration: 3000 });
            emits("after-submit", props.formCreateInject, param.value, res);
            instance.confirmButtonLoading = false;
            done(); // 成功时关闭对话框
          } else {
            // 操作失败的响应
            ElNotification({ title: "操作失败", message: res.message, type: "error", duration: 3000 });
            instance.confirmButtonLoading = false;
          }
        })
        .catch(() => {
          instance.confirmButtonLoading = false;
          ElNotification({ title: "操作失败", message: "提交未成功，请稍后重试或联系支持。", type: "error" });
          // 若不调用 done()，对话框不会关闭
        });
    } else {
      instance.confirmButtonLoading = false;
      done(); // 如果没有 submitRequest，直接关闭对话框
    }
  } else {
    instance.confirmButtonLoading = false;
    // 如果用户取消或关闭，直接结束对话
    done();
  }
};
// 打开弹窗
const open = () => {
  ElMessageBox.confirm(props.message, props.subtitle ? `${props.title}-${props.subtitle(param.value)}` : props.title, {
    confirmButtonText: props.confirmButtonText,
    cancelButtonText: props.cancelButtonText,
    buttonSize: "default",
    customClass: "confirmMessageStyle",
    icon: markRaw(QuestionFilled),
    center: props.center,
    beforeClose: beforeClose // 设置 `beforeClose` 回调
  });
};
</script>
