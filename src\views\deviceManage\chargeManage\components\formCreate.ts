/**
 * @file 设备管理-充电桩管理-表单创建文件
 * <AUTHOR>
 * @date 2024/11/20
 */
import { formMap } from "../types";

export const chargeMangeFormCreate = {
  type: "form-create",
  props: {
    option: {
      submitBtn: false,
      onSubmit(formData, api) {
        console.log("formData", formData);
        // 通知 table 搜索数据变化，刷新数据
        api.top.bus.$emit("searchFormChanged");
      }
    },
    rule: [
      {
        type: "input",
        field: formMap.get("充电桩名称"),
        title: "充电桩名称",
        validate: [
          { required: true, message: "请输入充电桩名称" },
          {
            pattern: /^.{1,20}$/,
            message: "字符限长20位"
          }
        ]
      },
      {
        type: "input",
        field: formMap.get("充电桩编码"),
        title: "充电桩编码",
        validate: [{ required: true, message: "请输入充电桩编码" }]
      },
      {
        type: "input",
        field: formMap.get("充电桩型号"),
        title: "充电桩型号"
      },
      {
        type: "input",
        field: formMap.get("1#充电接口编码"),
        title: "1#充电接口编码"
      },
      {
        type: "input",
        field: formMap.get("2#充电接口编码"),
        title: "2#充电接口编码"
      }
    ]
  }
};
