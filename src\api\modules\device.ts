import http from "@/api";

/**
 * @name 综合管理平台-设备管理-api接口统一出口
 */
// 设备管理-矿卡管理-获取矿卡列表
export const getMineTrainList = (params: any) => {
  return http.post("/wcs-server/device/mineTrain/listQueryByPage", params);
};
// 设备管理-矿卡管理-获取所有矿卡
export const getAllMineTrainList = () => {
  return http.post("/wcs-server/device/mineTrain/listMineTrainDetailByMap");
};
// 设备管理-辅车管理-获取所有辅车列表
export const getAllAncillaryCarList = () => {
  return http.post("/wcs-server/device/ancillaryCar/listQueryByMap");
};
// 设备管理-挖机管理-获取所有挖机列表
export const getAllBulldozersList = () => {
  return http.post("/wcs-server/device/bulldozers/listQueryByMap");
};

// 设备管理-矿卡管理-修改矿卡
export const saveMineTrain = (params: any) => {
  return http.post("/wcs-server/device/mineTrain/save", params);
};

// 设备管理-矿卡管理-删除矿卡
export const deleteMineTrain = (params: any) => {
  return http.delete("/wcs-server/device/mineTrain/deleteById", params);
};

// 设备管理-矿卡管理-矿卡授权
export const authorizations = (params: any) => {
  return http.post("/wcs-server/device/mineTrain/authorizations", params);
};

// 设备管理-矿卡管理-获取设备节点
export const getNodalList = (params: any) => {
  return http.post("/wcs-server/device/nodal/listQueryByDeviceId", params);
};
// 设备管理-矿卡管理-修改设备节点
export const saveNodal = (params: any) => {
  return http.post("/wcs-server/device/nodal/save", params);
};
// 设备管理-摄像头管理-获取摄像头列表
export const getCameraList = (params: any) => {
  return http.post("/wcs-server/wcs/deviceCamera/listQueryByPage", params);
};
// 设备管理-摄像头管理-修改摄像头
export const saveCamera = (params: any) => {
  return http.post("/wcs-server/wcs/deviceCamera/saveOrUpdate", params);
};
// 设备管理-摄像头管理-删除摄像头
export const deleteCamera = (params: any) => {
  return http.delete("/wcs-server/wcs/deviceCamera/deleteById", params);
};
// 设备管理-流量卡管理-获取流量卡列表
export const getMobileTrafficList = (params: any) => {
  return http.post("/wcs-server/wcs/deviceMobileTraffic/listQueryByPage", params);
};
// 设备管理-流量卡管理-新增流量卡
export const addMobileTraffic = (params: any) => {
  return http.post("/wcs-server/wcs/deviceMobileTraffic/add", params);
};
// 设备管理-流量卡管理-删除流量卡
export const deleteMobileTraffic = (params: any) => {
  return http.delete("/wcs-server/wcs/deviceMobileTraffic/deleteById", params);
};
// 设备管理-挖机管理-获取挖机列表
export const getBulldozersList = (params: any) => {
  return http.post("/wcs-server/device/bulldozers/listQueryByPage", params);
};
// 设备管理-挖机管理-新增挖机
export const saveBulldozers = (params: any) => {
  return http.post("/wcs-server/device/bulldozers/save", params);
};
// 设备管理-挖机管理-删除挖机
export const deleteBulldozers = (params: any) => {
  return http.delete("/wcs-server/device/bulldozers/deleteById", params);
};

// 设备管理-辅助车辆管理-获取列表
export const getAncillaryCarList = (params: any) => {
  return http.post("/wcs-server/device/ancillaryCar/listQueryByPage", params);
};
// 设备管理-辅助车辆管理-删除
export const deleteAncillaryCar = (params: any) => {
  return http.delete("/wcs-server/device/ancillaryCar/deleteById", params);
};
// 设备管理-辅助车辆管理-新增编辑
export const saveAncillaryCar = (params: any) => {
  return http.post("/wcs-server/device/ancillaryCar/save", params);
};

// 设备管理-破碎站管理-获取列表
export const getCrusherList = (params: any) => {
  return http.post("/wcs-server/device/crushingStation/listQueryByPage", params);
};
// 设备管理-破碎站管理-新增编辑
export const saveCrusher = (params: any) => {
  return http.post("/wcs-server/device/crushingStation/save", params);
};
// 设备管理-破碎站管理-删除
export const deleteCrusher = (params: any) => {
  return http.delete("/wcs-server/device/crushingStation/deleteById", params);
};
// 设备管理-驾驶舱管理-获取列表
export const getCockpitsList = (params: any) => {
  return http.post("/wcs-server/device/cockpits/listQueryByPage", params);
};
// 设备管理-驾驶舱管理-删除
export const deleteCockpits = (params: any) => {
  return http.delete("/wcs-server/device/cockpits/deleteById", params);
};
// 设备管理-驾驶舱管理-新增编辑
export const saveCockpits = (params: any) => {
  return http.post("/wcs-server/device/cockpits/save", params);
};
// 设备管理-充电桩管理-获取列表
export const getChargingPileList = (params: any) => {
  return http.post("/wcs-server/device/chargingPile/listQueryByPage", params);
};
// 设备管理-充电桩管理-获取所有列表(不分页)
export const getAllChargingPileListMap = () => {
  return http.post("/wcs-server/device/chargingPile/listQueryByMap");
};
// 设备管理-充电桩管理-新增编辑
export const saveChargingPile = (params: any) => {
  return http.post("/wcs-server/device/chargingPile/save", params);
};
// 设备管理-充电桩管理-删除
export const deleteChargingPile = (params: any) => {
  return http.delete("/wcs-server/device/chargingPile/deleteById", params);
};
// 设备管理-停车位管理-获取列表
export const getParkingSpotList = (params: any) => {
  return http.post("/wcs-server/device/parkingSpot/listQueryByPage", params);
};
// 设备管理-停车位管理-新增编辑
export const saveParkingSpot = (params: any) => {
  return http.post("/wcs-server/device/parkingSpot/save", params);
};
// 设备管理-停车位管理-删除
export const deleteParkingSpot = (params: any) => {
  return http.delete("/wcs-server/device/parkingSpot/deleteById", params);
};
