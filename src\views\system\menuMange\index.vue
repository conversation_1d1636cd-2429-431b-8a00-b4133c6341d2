<template>
  <form-create v-model:api="fApi" :option="option" :rule="rule">
    <template #type-div="scope">
      <span>{{ scope.name }}</span>
    </template>
  </form-create>
</template>
<script lang="tsx" setup>
import { ref } from "vue";
import { menuFormCreate } from "./components/formCreate";
import MenuDetail from "./components/MenuDetail.vue";
import formCreate from "@form-create/element-ui";
import { ResourceType } from "./types";
import { getMenuList, deleteMenu, addUserMenu } from "@/api/modules";
// 表格组件
import PermissionTable from "./components/PermissionTable.vue";

const fApi = ref();
// 激活项
const option = { resetBtn: false, submitBtn: false };
formCreate.component("PermissionTable", PermissionTable);
formCreate.component("MenuDetail", MenuDetail);

const rule = ref([
  // 左侧树
  {
    type: "LACard",
    props: { showHeader: false },
    children: [
      {
        // 左侧树组件
        type: "LATree",
        field: "v:tree",
        props: {
          // 开启树图标
          isTreeShowIcon: true,
          defaultExpandAll: true,
          // 搜索框
          needFilter: true,
          // 参数定义
          treeProps: { label: "name" },
          nodeKey: "id",
          // 请求接口
          fetch: () => {
            return getMenuList();
          },
          // 数据结构处理
          handleData: res => {
            return [
              {
                id: undefined,
                type: "ResourceMenu",
                name: "全部",
                pName: "无",
                isRoot: true,
                children: res.data
              }
            ];
          },
          // 控制显示小图标
          isShowIcon: true,
          // 下拉选项
          operations: row => {
            const data = [
              {
                content: "新增",
                action: "add",
                auth: "add",
                show: () => {
                  return [ResourceType.MENU, "ExternalResourceMenu"].includes(row.type);
                }
              },
              {
                content: "修改",
                action: "edit",
                auth: "update",
                show: () => {
                  return [ResourceType.MENU, "ExternalResourceMenu"].includes(row.type) && !row.isRoot;
                }
              },
              {
                content: "删除",
                action: "delete",
                auth: "delete",
                props: {
                  style: {
                    color: "rgba(242, 85, 85, 1)"
                  }
                },
                show: () => {
                  return !row.isRoot;
                }
              }
            ];
            return data.filter(op => (op.show !== undefined ? op.show() : true));
          }
        },
        on: {
          // 监听组件抛出的的currentChange事件
          currentChange: (val, api) => {
            // 动态修改传入的参数
            api.getRule("v:detail").props.menuData = val;
            api.getRule("v:table").props.menuData = val;
          }
        },
        children: [
          {
            type: "AddBtn",
            props: {
              n: "menuFormCreate",
              action: "add",
              btn: { show: false },
              dialog: { title: "新增菜单" },
              /**
               * 处理数据弹窗方法,弹窗生成前设置需要的值
               * @param data 获取的表单数据
               * @return {Object} 返回的数据
               * <AUTHOR>
               * @date 2024/11/7
               * */
              formatter: (data: any): object => {
                const options = [
                  { label: "普通菜单", value: "tenant" },
                  { label: "外部接口菜单", value: "ExternalResourceMenu" }
                ];
                // 新增显示的上级菜单的名字为当前菜单
                let params = { pName: data.name };
                // 上级菜单参数
                if (!data.isRoot) params["pid"] = data.id;
                // 菜单类型判断
                if (!options.some(opt => data.type === opt.value)) params["type"] = options[0].value;
                return params;
              },
              submitRequest: finallyParams => {
                const type = finallyParams["type"];
                if (type === "ExternalResourceMenu") {
                  finallyParams.isNotTenantResource = false;
                } else {
                  finallyParams.type = ResourceType.MENU;
                  finallyParams.isNotTenantResource = type !== "tenant";
                }
                // 提交的请求
                return addUserMenu(finallyParams);
              }
            },
            children: [menuFormCreate]
          },
          {
            type: "EditBtn",
            props: {
              n: "menuFormCreate",
              action: "edit",
              dialog: {
                title: "修改菜单"
              },
              formatter: (data: any): object => {
                const options = [
                  { label: "普通菜单", value: "tenant" },
                  { label: "外部接口菜单", value: "ExternalResourceMenu" }
                ];
                let params = { ...data, pName: data.pname || "所有" };

                if (!options.some(opt => data.type === opt.value)) params["type"] = options[0].value;
                return params;
              },
              submitRequest: finallyParams => {
                const type = finallyParams["type"];
                if (type === "ExternalResourceMenu") {
                  finallyParams.isNotTenantResource = false;
                } else {
                  finallyParams.type = ResourceType.MENU;
                  finallyParams.isNotTenantResource = type !== "tenant";
                }
                // 提交的请求
                return addUserMenu(finallyParams);
              }
            },
            children: [menuFormCreate]
          },
          {
            type: "ConfirmDialog",
            on: {
              // 监听弹窗组件抛出的的afterSubmit事件，用于刷新页面
              afterSubmit: () => {
                // 刷新树组件，调用组件内部请求方法
                fApi.value.exec("v:tree", "fetchData");
              }
            },
            props: {
              action: "delete",
              subtitle: row => {
                return row.name;
              },
              title: "是否删除菜单",
              message: "删除后不可恢复",
              // 模拟请求param：参数
              submitRequest: param => {
                console.log(param);
                // 提交请求
                return deleteMenu({ id: param.id });
              }
            }
          }
        ]
      }
    ]
  },
  // 右侧组件详情
  {
    type: "MenuDetail",
    field: "v:detail",
    class: "right-detail",
    style: {
      backgroundColor: "#fff",
      padding: "20px 20px 0 20px",
      width: "100%",
      boxSizing: "border-box",
      borderRadius: "10px",
      height: "100%"
    },
    col: { span: 16 },
    // 使用渲染函数传递插槽内容
    children: [
      {
        // 表格组件
        type: "PermissionTable",
        field: "v:table"
      }
    ]
  }
]);
</script>
<style lang="scss" scoped>
//修改原有下边距
:deep(.el-col-16) {
  > .el-form-item {
    margin-bottom: 0 !important; // 使用 !important 或增加选择器优先级
    height: 100%;
  }
}

//修改表单自带行高
:deep(.custom-tree-node) {
  line-height: normal;

  .menu-icon {
    width: 16px;
    height: 16px;
    margin-right: 5px;
    background: url("./img/menu.png");
  }

  &:hover .menu-icon {
    background: url("./img/menu_active.png");
  }
}

:deep(.el-form-item__content) {
  width: 100%;
  line-height: normal;
}

:deep(.el-card__body) {
  .el-form-item {
    width: 100%;
  }
}

:deep(.el-form-item--label-right) {
  margin-right: 0 !important;
}

:deep(.el-card .el-tree) {
  width: 100%;
}
// 激活样式
:deep(.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content) {
  .menu-icon {
    background: url("./img/menu_active.png");
  }
}
//:deep(.box-card) {
//  //max-width: 550px;
//  width: 30%;
//}
:deep(.el-row) {
  height: 100%;
  flex-wrap: nowrap;
  gap: 4px;
  width: 100%;
}

:deep(.menu-detail .el-row) {
  flex-wrap: wrap;
}

:deep(.el-card) {
  display: flex;
  flex-direction: column;
  border: none;
  border-radius: 8px;
}

:deep(.el-card__body) {
  flex: 1;
  overflow-x: hidden;
  overflow-y: auto;
}
</style>
