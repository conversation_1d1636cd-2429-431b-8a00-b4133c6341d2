/**
 * @file 历史数据-充电记录-类型声明文件
 * <AUTHOR>
 * @date 2025/7/16
 */

// table字段声明
export const columnMap: any = new Map([
  ["记录时间", "createDate"],
  ["状态", "status"],
  ["清除时间", "clearTime"]
]);
// 充电任务状态枚举
export enum StatusEnum {
  /** 未清除*/
  UNCLEARED = 0,

  /** 已清除*/
  CLEARED = 1
}
// 充电任务状态
export const obstacleRecordStatusEnum = [
  {
    bg: "rgba(242, 85, 85, .1)",
    status: StatusEnum.UNCLEARED,
    color: "#F25555",
    text: "未清除"
  },

  {
    bg: "rgba(101, 102, 102, .1)",
    status: StatusEnum.CLEARED,
    color: "#656666",
    text: "已清除"
  }
];
