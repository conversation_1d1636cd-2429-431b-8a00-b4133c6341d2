.tabs-box {
  width: 100%;
  .tabs-menu {
    position: relative;
    width: 100%;
    .el-dropdown {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
    }
    :deep(.el-tabs) {
      --el-tabs-header-height: 36px;
      .el-tabs__header {
        box-sizing: border-box;
        margin: 0;
        border-bottom: none;
        .el-tabs__nav-wrap {
          position: absolute;
          width: calc(100% - 70px);
          .el-tabs__nav {
            display: flex;
            border: none;
            .el-tabs__item {
              display: flex;
              align-items: center;
              justify-content: center;
              color: #fff;
              border: none;
              .tabs-icon {
                margin: 1.5px 4px 0 0;
                font-size: 15px;
              }
              .is-icon-close {
                margin-top: 1px;
              }
              &.is-active {
                background-color: var(--el-bg-color-page);
                opacity: 1;
                color: #1a1a1a;
                border-radius: 6px 6px 0 0;
                // 新装置
                box-shadow:
                  6px 6px 0 0 #eff1f5,
                  -6px 6px 0 0 #eff1f5;
                --el-color-primary: #1a1a1a;
              }
            }
            .is-closable {
              padding: 0 10px;
            }
            .is-closable::before {
              content: "";
              position: absolute;
              top: 50%;
              right: 100%;
              width: 0.5px;
              height: 14px;
              background-color: #41438a;
              transform: translateY(-50%);
            }
            .is-active::before {
              content: "";
              position: absolute;
              left: -12px;
              bottom: 0;
              width: 12px;
              height: 100%;
              background: #1f2158;
              border-radius: 0 0 10px 0;
            }
            .is-active::after {
              content: "";
              position: absolute;
              right: -12px;
              bottom: 0;
              width: 12px;
              height: 100%;
              background: #1f2158;
              border-radius: 0 0 0 10px;
            }
          }
        }
      }
    }
  }
}
