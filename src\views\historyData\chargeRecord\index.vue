<template>
  <form-create v-model:api="fApi" :option="option" :rule="rule"></form-create>
</template>

<script lang="ts" setup>
/**
 * @file 历史数据-充电记录
 * <AUTHOR>
 * @date 2025/7/15
 */
import { ref } from "vue";
import { columnMap, chargingTaskStatusEnum } from "./types";
import { getAllChargingPileListMap, getAllMineTrainList } from "@/api/modules/device";

import formCreate from "@form-create/element-ui";
import { getChargeList } from "@/api/modules/historyData.ts";
import LASelect from "@/components/LASelect.tsx";

const fApi = ref();
const option = {
  form: { inline: true },
  resetBtn: false,
  submitBtn: false
};

const rule = ref([
  {
    type: "SearchFormOperation",
    field: "v:search",
    wrap: { style: "marginBottom: 0" },
    children: [
      {
        component: LASelect,
        field: "name",
        style: { width: "200px", lineHeight: "initial" },
        props: {
          fetch: getAllChargingPileListMap,
          replaceFields: { key: "id", label: "name", value: "id" },

          placeholder: "充电桩名称"
        }
      },
      {
        component: LASelect,
        field: "name",
        style: { width: "200px", lineHeight: "initial" },
        props: {
          fetch: getAllMineTrainList,
          replaceFields: { key: "id", label: "name", value: "id" },
          placeholder: "矿卡名称"
        }
      },
      {
        component: LASelect,
        field: "status",
        style: { width: "200px", lineHeight: "initial" },
        props: {
          list: [
            {
              label: "充电中",
              value: 0
            },
            {
              label: "已结束",
              value: 1
            }
          ],
          placeholder: "状态"
        }
      },
      {
        type: "LADateTimeRangePicker",
        name: "time",
        style: { lineHeight: "initial", height: "32px" },
        props: {
          type: "daterange",
          format: "YYYY-MM-DD",
          placeholder: ["起始日期", "截止日期"]
        },

        on: {
          "update:start": val => {
            if (val) {
              fApi.value.form["startTime"] = val;
            } else {
              fApi.value.form["startTime"] = undefined;
            }
          },
          "update:end": val => {
            if (val) {
              fApi.value.form["endTime"] = val;
            } else {
              fApi.value.form["endTime"] = undefined;
            }
          }
        }
      }
    ]
  },
  {
    type: "ProTable",
    props: {
      columns: [
        {
          prop: columnMap.get("充电桩名称"),
          label: "充电桩名称"
        },
        {
          prop: columnMap.get("矿车名称"),
          label: "矿车名称",
          render: scope => {
            return scope.row.name;
          }
        },
        {
          prop: columnMap.get("状态"),
          label: "状态",
          tag: true,
          enum: [...chargingTaskStatusEnum]
        },
        {
          prop: columnMap.get("开始充电时间"),
          label: "开始充电时间"
        },
        {
          prop: columnMap.get("充前电量"),
          label: "充前电量"
        },
        {
          prop: columnMap.get("当前电量"),
          label: "当前电量"
        },
        {
          prop: columnMap.get("预计充满剩余时长"),
          label: "预计充满剩余时长"
        },
        {
          prop: columnMap.get("结束充电时间"),
          label: "结束充电时间"
        },
        {
          prop: columnMap.get("充后电量"),
          label: "充后电量"
        },
        {
          prop: columnMap.get("充电时长"),
          label: "充电时长",
          render: ({ row }) => {
            const hours = Math.floor(row.chargingTime / 3600000);
            const minutes = Math.floor((row.chargingTime % 3600000) / 60000);
            const seconds = Math.floor((row.chargingTime % 60000) / 1000);

            if (hours === 0 && minutes === 0) {
              return `${seconds}秒`;
            } else if (hours !== 0) {
              if (minutes === 0) {
                return `${hours}时0分${seconds}秒`;
              } else {
                return `${hours}时${minutes}分${seconds}秒`;
              }
            } else {
              return `${minutes}分${seconds}秒`;
            }
          }
        }
      ],
      fetch: getChargeList
    }
  }
]);
</script>
<style lang="scss" scoped>
:deep(.el-row) {
  height: calc(100vh - 138px) !important;
  .el-select--large .el-select__wrapper {
    min-height: initial;
  }
}

.el-form-item {
  margin-right: 4px !important;
}
</style>
