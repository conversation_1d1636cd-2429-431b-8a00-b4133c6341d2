.el-container {
  width: 100%;
  height: 100%;
  :deep(.el-header) {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 55px;
    padding: 0 15px 0 0;
    background-color: var(--el-header-bg-color);
    border-bottom: 1px solid var(--el-header-border-color);
    .logo {
      width: 210px;
      margin-right: 30px;
      .logo-img {
        width: 28px;
        object-fit: contain;
      }
      .logo-text {
        margin-left: 6px;
        font-size: 21.5px;
        font-weight: bold;
        color: var(--el-header-logo-text-color);
        white-space: nowrap;
      }
    }
    .el-menu {
      flex: 1;
      height: 100%;
      overflow: hidden;
      border-bottom: none;
      .el-sub-menu__hide-arrow {
        width: 65px;
        height: 55px;
      }
      .el-menu-item.is-active {
        color: #ffffff !important;
      }
      .is-active {
        background-color: var(--el-color-primary) !important;
        border-bottom-color: var(--el-color-primary) !important;
        &::before {
          width: 0;
        }
        .el-sub-menu__title {
          color: #ffffff !important;
          background-color: var(--el-color-primary) !important;
          border-bottom-color: var(--el-color-primary) !important;
        }
      }
    }
  }

  @media screen and (width <= 730px) {
    .logo {
      display: none !important;
    }
  }
}
