<template>
  <el-scrollbar style="height: calc(100vh - 56px)">
    <div style="display: flex; overflow: hidden; gap: 8px; padding: 16px" class="flex-col">
      <DeviceSpace v-for="space of mappedDevices" :key="space.title" v-bind="space" />
    </div>
  </el-scrollbar>
</template>

<script lang="ts" setup>
import DeviceSpace from "./DeviceSpace.vue";
import { useDeviceWingSection } from "../../request";
import { computed } from "vue";
import { getStatusText, getTruckDriveMode } from "../../shared";
import { StatusEnum as TruckStatusEnum } from "@/views/deviceManage/miningTruckManage/types";
import { StatusEnum as ChargingPileStatusEnum } from "@/views/deviceManage/chargeManage/types";
import { StatusEnum as BulldozerStatusEnum } from "@/views/deviceManage/diggingMachineManage/types";
import { StatusEnum as CrusherStatusEnum } from "@/views/deviceManage/crushingStationManage/types";
import { StatusEnum as CockpitStatusEnum } from "@/views/deviceManage/cockpitManage/types";
interface ApiDeviceResponse {
  attendanceNumber: number;
  leisureNumber: number;
  rechargeBatteriesNumber: number;
  faultsNumber: number;
  // 无人矿卡
  mineTrainDtoList: Array<{
    id: string;
    name: string;
    code: string;
    status: number;
    driveMode: string;
    electricQuantity: number;
    taskFlag: boolean;
    taskCurrentStep?: string;
    // 是否故障
    errorFlag?: 1 | 0;
    // 故障等级内容
    errorLevel?: string;
  }>;
  // 充电桩
  chargingPileList: Array<{
    id: string;
    name: string;
    code: string;
    status: number;
  }>;
  // 挖机
  bulldozersList: Array<{
    id: string;
    name: string;
    code: string;
    status: number;
  }>;
  // 破碎站
  crushingStationList: Array<{
    id: string;
    name: string;
    code: string;
    status: number;
  }>;
  // 驾驶舱
  cockpitsList: Array<{
    id: string;
    name: string;
    code: string;
    status: number;
  }>;
}

interface DeviceStatusData {
  value: number;
  label: string;
  key: string;
  color: string;
}

interface DeviceSpaceData {
  code: string;
  modeText: string;
  battery: number;
  status: number;
  statusText: string;
  isFault: boolean;
  lowBattery: boolean;
  showMode: boolean;
  showBattery: boolean;
  deviceType: string;
  rawData: any;
}

interface DeviceStatusRowSpace {
  type: "status";
  height: number;
  data: DeviceStatusData[];
}

interface DeviceRowSpace {
  type: "device-space";
  height: number;
  data: DeviceSpaceData[];
}

interface DeviceSpace {
  title: string;
  rows: (DeviceStatusRowSpace | DeviceRowSpace)[];
}

const { data } = useDeviceWingSection<ApiDeviceResponse>()!;

const mappedDevices = computed<DeviceSpace[]>(() => {
  if (!data.value) return [];

  const apiData = data.value;
  return [
    {
      title: "无人矿卡",
      rows: [
        {
          type: "status",
          height: 37,
          data: [
            {
              value: apiData.attendanceNumber || 0,
              label: "出勤",
              key: "work",
              color: "var(--el-color-secondary)"
            },
            {
              value: apiData.leisureNumber || 0,
              label: "空闲",
              key: "idle",
              color: "var(--el-color-primary)"
            },
            {
              value: apiData.rechargeBatteriesNumber || 0,
              label: "充电",
              key: "charging",
              color: "var(--el-color-success)"
            },
            {
              value: apiData.faultsNumber || 0,
              label: "故障",
              key: "error",
              color: "var(--el-color-danger)"
            }
          ]
        },
        {
          type: "device-space",
          height: 320,
          data:
            apiData.mineTrainDtoList?.map(item => ({
              deviceType: "deviceMineTrain",
              name: item.name || "",
              code: item.code || "",
              modeText: getTruckDriveMode(+item.driveMode),
              battery: item.electricQuantity || 0,
              status: item.status,
              statusText: item.errorLevel || item.taskCurrentStep || getStatusText(item.status?.toString(), "deviceMineTrain")!,
              isFault: item.errorFlag?.toString() === "1",
              lowBattery: (item.electricQuantity || 0) < 20,
              showMode: true,
              showBattery: true,
              rawData: { ...item, isActivateTheme: item.status?.toString() !== TruckStatusEnum.TRAIN_OFFLINE.toString() }
            })) || []
        }
      ]
    },
    {
      title: "充电桩",
      rows: [
        {
          type: "device-space",
          height: 156,
          data:
            apiData.chargingPileList?.map(item => ({
              deviceType: "deviceChargingPile",
              name: item.name || "",
              code: item.code || "",
              modeText: "自动",
              battery: 100,
              status: item.status || 0,
              statusText: getStatusText(item.status?.toString(), "deviceChargingPile")!,
              isFault: false,
              lowBattery: false,
              showMode: true,
              showBattery: true,
              rawData: { ...item, isActivateTheme: item.status?.toString() !== ChargingPileStatusEnum.CHARGING_PILE_OFFLINE }
            })) || []
        }
      ]
    },
    {
      title: "挖机",
      rows: [
        {
          type: "device-space",
          height: 156,
          data:
            apiData.bulldozersList?.map(item => ({
              deviceType: "deviceBulldozers",
              name: item.name || "",
              code: item.code || "",
              modeText: "自动",
              battery: 100,
              status: item.status || 0,
              statusText: getStatusText(item.status?.toString(), "deviceBulldozers")!,
              isFault: false,
              lowBattery: false,
              showMode: false,
              showBattery: false,
              rawData: { ...item, isActivateTheme: item.status?.toString() !== BulldozerStatusEnum.OFFLINE.toString() }
            })) || []
        }
      ]
    },
    {
      title: "破碎站",
      rows: [
        {
          type: "device-space",
          height: 74,
          data:
            apiData.crushingStationList?.map(item => ({
              deviceType: "deviceCrushingStation",
              name: item.name || "",
              code: item.code || "",
              modeText: "自动",
              battery: 100,
              status: item.status || 0,
              statusText: "正常",
              isFault: false,
              lowBattery: false,
              showMode: true,
              showBattery: false,
              rawData: { ...item, isActivateTheme: item.status?.toString() !== CrusherStatusEnum.OFFLINE.toString() }
            })) || []
        }
      ]
    },
    {
      title: "驾驶舱",
      rows: [
        {
          type: "device-space",
          height: 74,
          data:
            apiData.cockpitsList?.map(item => ({
              deviceType: "deviceCockpits",
              name: item.name || "",
              code: item.code || "",
              modeText: "自动",
              battery: 100,
              status: item.status || 0,
              statusText: getStatusText(item.status?.toString(), "deviceCockpits")!,
              isFault: false,
              lowBattery: false,
              showMode: false,
              showBattery: false,
              rawData: { ...item, isActivateTheme: item.status?.toString() !== CockpitStatusEnum.OFFLINE.toString() }
            })) || []
        }
      ]
    }
  ];
});
</script>
