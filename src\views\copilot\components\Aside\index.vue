<template>
  <el-scrollbar style="height: calc(100vh - 56px)">
    <div style="display: flex; overflow: hidden; gap: 8px; padding: 16px" class="flex-col">
      <DeviceSpace v-for="space of mappedDevices" :key="space.title" v-bind="space" />
    </div>
  </el-scrollbar>
</template>

<script lang="ts" setup>
import DeviceSpace from "./DeviceSpace.vue";
import { useDeviceDisplay } from "@/composables/useDeviceDisplay";

// 使用新的数据源
const { mappedDevices } = useDeviceDisplay();
</script>
