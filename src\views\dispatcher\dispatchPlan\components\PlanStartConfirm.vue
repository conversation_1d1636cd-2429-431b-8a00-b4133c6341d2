<template>
  <div class="container">
    <el-descriptions :column="2">
      <el-descriptions-item v-for="item of labels" :key="item.field" :label="item.label + ':'">
        {{ showValue(props.data?.[item.field]) }}
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script lang="ts" setup>
import { ElDescriptions, ElDescriptionsItem } from "element-plus";
import { showValue } from "@/utils/helpers";
import { columnMap } from "@/views/dispatcher/dispatchPlan/types";

const props = defineProps<{ data: { [key: string]: any } }>();
const labels = [
  { label: "计划编号", field: columnMap.get("计划编号") },
  { label: "任务类型", field: columnMap.get("任务类型名称") },
  { label: "班次", field: columnMap.get("班次") },
  { label: "计划开始时间", field: columnMap.get("计划开始时间") },
  { label: "计划结束时间", field: columnMap.get("计划结束时间") }
];
</script>
<style lang="scss" scoped>
.container {
  width: 100%;
  height: 100%;
  padding: 20px 20px 4px 20px;
  background: rgba(238, 243, 255, 1);
  --el-fill-color-blank: rgba(238, 243, 255, 1);
  border-radius: 8px;

  :deep(.el-descriptions__cell) {
    width: 260px;
    line-height: 100%;
    padding-bottom: 20px !important;
  }
  :deep(.el-descriptions__label) {
    font-weight: bold;
    margin-right: 10px;
    line-height: 100%;
    width: 60px;
  }
}
</style>
