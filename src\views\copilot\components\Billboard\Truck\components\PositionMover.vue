<template>
  <MessageBox
    ref="messageBoxRef"
    v-bind="messageProps"
    :beforeClose="handleBeforeClose"
    @confirm="handleConfirm"
    @cancel="handleCancel"
    :messageBox="false"
    width="584px"
    class="task-creator-dialog"
    dialogContentStyle="display: flex; flex-direction: column; align-items: center; justify-content: center; --el-border-color: #6768BF; margin-top: inherit; --el-color-danger: var(--el-color-secondary)"
  >
    <el-form :model="formData" :rules="rules" ref="formRef" style="width: 100%" label-width="80px">
      <el-form-item label="矿车" prop="truckName">
        <el-select
          v-model="formData.truckName"
          placeholder="KK-10"
          size="large"
          popper-class="add-to-task-message-box"
          style="width: 100%"
        >
          <el-option :label="formData.truckName" :value="formData.truckName" />
        </el-select>
      </el-form-item>

      <el-form-item label="朝向" prop="direction">
        <el-input v-model="formData.direction" placeholder="请输入朝向角度" size="large" style="width: 100%">
          <template #suffix>度</template>
        </el-input>
      </el-form-item>

      <el-form-item label="海拔" prop="altitude">
        <div style="display: flex; flex-direction: column; gap: 8px; width: 100%">
          <el-radio-group v-model="formData.altitude" style="--el-border-color: white">
            <el-radio v-for="option in altitudeOptions" :key="option.value" :label="option.value">
              <div class="altitude-value">
                {{ option.value }}
                <span class="altitude-unit">米</span>
              </div>
            </el-radio>
          </el-radio-group>
        </div>
      </el-form-item>
    </el-form>
  </MessageBox>
</template>

<script lang="ts" setup>
import { ref, reactive, computed } from "vue";
import MessageBox from "../../../MessageBox.vue";
import { useDeviceSelection } from "@/views/copilot/store";
import type { FormInstance, FormRules } from "element-plus";

interface AltitudeOption {
  value: string;
}
const props = defineProps<{
  cancelCallback: () => void;
  position: { lng: number; lat: number };
  direction: string;
}>();
const messageBoxRef = ref();
const messageProps = ref({ title: "定位移动", message: "" });

const formRef = ref<FormInstance>();
const formData = reactive({
  truckName: "KK-10",
  direction: props.direction,
  altitude: ""
});

const altitudeOptions = computed<AltitudeOption[]>(() => [{ value: "2288.80" }, { value: "2280.80" }]);

// 表单验证规则
const rules = reactive<FormRules>({
  altitude: [{ required: true, message: "请选择海拔高度", trigger: "change" }],
  direction: [{ required: true, message: "请输入朝向", trigger: "change" }],
  truckName: [{ required: true, message: "请选择矿车", trigger: "change" }]
});

const handleBeforeClose = () => {
  return new Promise<string | boolean>(async resolve => {
    formRef
      .value!.validate()
      .then(() => resolve(true))
      .catch(() => resolve(false));
  });
};

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields();
  }
  formData.truckName = "KK-10";
  formData.direction = "";
  formData.altitude = "";
};

const handleConfirm = async () => {
  if (!formRef.value) return;

  try {
    // TODO: 调用修改位置的API
    resetForm();
    messageBoxRef.value.close();
  } catch (error) {
    console.log("error submit!", error);
  }
};

const handleCancel = () => {
  props?.cancelCallback();
  resetForm();
};

const show = () => {
  messageBoxRef.value.show();
};

// 暴露方法给父组件
defineExpose({ show });
</script>

<style lang="scss">
.add-to-task-message-box {
  --el-bg-color-overlay: #3b3ca7;
  --el-box-shadow-light: 0px 0px 12px #252688;
  --el-fill-color-light: #2e2f9a;
  box-shadow: none !important;
}
.task-creator-dialog {
  .tip-text {
    color: #fff;
    font-size: 14px;
    padding: 10px 0;
    text-align: center;
  }
}
</style>
