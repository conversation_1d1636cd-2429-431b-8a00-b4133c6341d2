/**
 * @file 系统管理-角色管理-类型声明文件
 * <AUTHOR>
 * @date 2024/11/8
 */
import { Api } from "@form-create/element-ui";

// 角色分类枚举，分为预设角色和普通角色
export enum RoleTypeEnum {
  /**普通角色*/
  NORMAL_ROLE = "NormalRole",
  /**预设角色*/
  PRESUPPOSE_ROLE = "PresupposeRole"
}

// 角色管理模块table表格项字段声明
export const columnMap: any = new Map([
  ["角色名称", "roleName"],
  ["编码", "roleCode"]
]);
// 角色管理模块form字段声明
export const formMap: any = new Map([
  ["角色名称", "roleName"],
  ["角色编码", "roleCode"]
]);
export interface Rule {
  // 生成组件的名称，例如 'input', 'select' 等
  type: string;
  // 表单字段名称，用于数据绑定
  field?: string;
  // 组件的唯一标识符
  name?: string;
  // 字段标签
  title?: string;
  // 组件的提示信息
  info?: string;
  // 组件的默认值
  value?: any;
  // 组件的属性配置
  props?: Object;
  // 组件的内联样式
  style?: string | Object;
  // 组件的 class 样式类
  class?: string | Array<string>;
  // 设置组件的 id
  id?: string | Array<string>;
  // 组件事件处理函数
  on?: { [key: string]: Function | Function[] };
  // 插槽名，用于组件嵌套
  slot?: string;
  // 组件的 key，通常用于列表渲染时的唯一标识
  key?: string;
  // 是否必填
  $required?: boolean | string;
  // 组件的选项列表，适用于 `radio`, `select`, `checkbox` 等组件
  options?: Array<any>;
  // 选项插入的目标属性，默认插入到 `props.data` 中
  optionsTo?: string;
  // 是否原样生成组件，而不使用 FormItem 包裹
  native?: boolean;
  // 是否隐藏组件（不会渲染 DOM 元素）
  hidden?: boolean;
  // 是否显示组件（有 DOM 渲染，但可能不可见）
  display?: boolean;
  // 是否开启事件注入
  inject?: boolean | Object;
  // 组件的验证规则
  validate?: Object[];
  // 子组件列表，用于嵌套子组件
  children?: Rule[];
  // 组件的联动控制，控制其他组件的显示与否
  control?: Array<any>;
  // FormItem 的配置
  wrap?: Object;
  // 设置组件的布局规则
  col?: Object;
  // 自定义属性，如远程数据获取等
  effect?: {
    // 加载远程数据
    fetch?: Object;
  };
  // 设置组件的前缀内容，通常用于在输入框前显示图标或文本
  prefix?: string | Rule;
  // 设置组件的后缀内容，通常用于在输入框后显示图标或文本
  suffix?: string | Rule;
  // 设置组件的自定义指令
  directives?: object;
  // 是否缓存组件，只触发一次渲染
  cache?: boolean;
  // 设置回调函数，用于动态更新组件的内容
  update?: (
    value: any,
    api: Api,
    origin: {
      // init初始化触发,link关联触发,value变化触发
      origin: "change" | "init" | "link";
      // 关联触发的字段名
      linkField?: string;
    }
  ) => boolean | undefined;
  // 配置哪些字段变化时会触发当前组件的 `update` 回调
  link?: string[];
  // 设置`props`中需要双向绑定属性的名称
  sync?: string[];
  // 使用`emit`方式监听的事件名
  emit?: string[];
  // 自定义组件 `emit` 事件的前缀，默认是组件的 `field` 字段
  emitPrefix?: string;
  // 定义用于当前规则渲染的自定义组件
  component?: boolean;
  // 其他扩展属性
  [key: string]: any;
}
