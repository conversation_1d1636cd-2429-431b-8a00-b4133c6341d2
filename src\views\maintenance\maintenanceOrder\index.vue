<template>
  <div style="height: 100%; width: 100%; display: flex; flex-direction: column">
    <form-create v-model:api="fApi" :option="option" :rule="rule" style="height: 100%"></form-create>
    <!--  <div style="position: absolute; z-index: -1; left: -3000px">-->
    <!--    <PrintMaintenanceOrder :print-data="printOrderData" />-->
    <div v-show="false">
      <PrintTicket id="printTicket" ref="printTicket" v-bind="{ ...printOrderData }" />
    </div>
  </div>
</template>
<script lang="tsx" setup>
/**
 * @file 维修保养-保养工单
 * <AUTHOR>
 * @date 2025/2/5
 */
import { computed, ref, nextTick } from "vue";
import { columnMap, maintenanceStatusInfo, StatusEnum } from "./types";
import PrintMaintenanceOrder from "./components/PrintMaintenanceOrder.vue";
import formCreate from "@form-create/element-ui";
import { getAllMineTrainList } from "@/api/modules/device";
import LASelect from "@/components/LASelect.tsx";
import Print from "print-js";
import PrintTicket from "../../../components/PrintTicket/index.vue";
import {
  getMaintenanceCycleList,
  getMaintenancePositionList,
  getMaintenanceWorkOrderList,
  getRepairExperienceModel
} from "@/api/modules/repair.ts";
import { detailFormCreate } from "@/views/maintenance/maintenanceOrder/components/detailFormCreate.ts";

const printTicket = ref();
const selData: any = ref([]);
const printBtn = computed(() => {
  return selData.value.length === 0;
});
const fApi = ref();
const option = {
  form: { inline: true },
  resetBtn: false,
  submitBtn: false
};
const printLoading = ref(false);
// 需要打印的工单数据
const printOrderData = ref();
const rule = ref([
  {
    type: "SearchFormOperation",
    field: "v:search",
    wrap: { style: "marginBottom: 0px" },
    children: [
      {
        component: LASelect,
        field: "modelNumber",
        style: { width: "200px", lineHeight: "initial" },
        props: {
          fetch: getRepairExperienceModel,
          params: {
            deviceType: "deviceMineTrain"
          },
          replaceFields: { key: "code", label: "name", value: "code" },
          placeholder: "矿卡型号"
        }
      },
      {
        component: LASelect,
        field: "deviceId",
        style: { width: "200px" },
        props: {
          fetch: getAllMineTrainList,
          replaceFields: { key: "id", label: "name", value: "id" },
          placeholder: "矿卡名称"
        }
      },
      {
        component: LASelect,
        field: "status",
        style: { width: "100px" },
        props: {
          placeholder: "工单状态",
          list: [
            { label: "未派工", value: StatusEnum.NOT_ASSIGNED },
            { label: "已关闭", value: StatusEnum.CLOSED },
            { label: "待保养", value: StatusEnum.TO_MAINTAIN }
          ]
        }
      },
      {
        component: LASelect,
        field: "cycle",
        style: { width: "100px" },
        props: {
          fetch: getMaintenanceCycleList,
          params: {
            deviceType: "deviceMineTrain"
          },
          replaceFields: { key: "cycle", label: "cycle", value: "cycle" },
          placeholder: "保养间隔"
        }
      },
      {
        type: "LADateTimeRangePicker",
        name: "time",
        style: { lineHeight: "initial", height: "32px" },
        props: {
          type: "daterange",
          format: "YYYY-MM-DD",
          placeholder: ["起始日期", "截止日期"]
        },

        on: {
          "update:start": val => {
            if (val) {
              fApi.value.form["startTime"] = val;
            } else {
              fApi.value.form["startTime"] = undefined;
            }
          },
          "update:end": val => {
            if (val) {
              fApi.value.form["endTime"] = val;
            } else {
              fApi.value.form["endTime"] = undefined;
            }
          }
        }
      },
      {
        type: "input",
        field: "chargePerson",
        style: { width: "100px" },
        props: {
          size: "default",
          placeholder: "保养人"
        }
      },
      {
        type: "input",
        field: "number",
        style: { width: "100px" },
        props: {
          size: "default",
          placeholder: "保养工单号"
        }
      },
      {
        type: "el-button",
        slot: "suffix",
        style: {
          height: "32px",
          width: "60px"
        },
        on: {
          // async click() {
          //   printLoading.value = true;
          //   // 打印的数据整合
          //   printOrderData.value = { ...selData.value[0] };
          //   console.log(printOrderData.value);
          //   printLoading.value = false;
          //
          //   await nextTick();
          //   Print({
          //     printable: "printMaintenanceOrderPage",
          //     type: "html",
          //     css: "./css/printMaintenanceOrder.css",
          //     scanStyles: false,
          //     showModal: true,
          //     modalMessage: "处理中...",
          //     documentTitle: `保养工单号-${printOrderData.value.code}`
          //   });
          // }
          async click() {
            printLoading.value = true;
            // 打印的数据整合
            printOrderData.value = { ...selData.value[0] };

            console.log(333333, printOrderData.value);
            // // 获取保养项目
            const res = await getMaintenancePositionList({ workOrderId: printOrderData.value.id });
            printOrderData.value.maintenancePositionList = res.data;

            await nextTick();

            printTicket.value.print();

            printLoading.value = false;
            // await nextTick();
            // Print({
            //   printable: "maintain-ticket",
            //   type: "html",
            //   // css: "./css/printMaintenanceOrder.css",
            //   scanStyles: false,
            //   showModal: true,
            //   modalMessage: "处理中..."
            //   // documentTitle: `保养工单号-${printOrderData.value.code}`
            // });
          }
        },
        props: {
          type: "primary",
          disabled: printBtn
        },
        children: ["打印"]
      }
    ]
  },
  {
    type: "ProTable",
    field: "v:table",
    wrap: {
      style: {
        height: "calc(100% - 50px)",
        width: "100%"
      }
    },
    props: {
      columns: [
        { type: "selection", label: "计划编号" },
        {
          prop: columnMap.get("保养工单号"),
          label: "保养工单号"
        },
        {
          prop: columnMap.get("工单状态"),
          label: "工单状态",
          tag: true,
          enum: [...maintenanceStatusInfo]
        },
        {
          prop: columnMap.get("逾期天数"),
          label: "逾期天数(天)",
          render: scope => {
            return (
              <span style={{ color: scope.row.status == StatusEnum.TO_MAINTAIN && scope.row.overdueDays > 0 ? "red" : "" }}>
                {scope.row.overdueDays}
              </span>
            );
          }
        },
        {
          prop: columnMap.get("矿车名称"),
          label: "矿车名称"
        },
        {
          prop: columnMap.get("矿卡型号"),
          label: "矿卡型号"
        },
        {
          prop: columnMap.get("保养间隔"),
          label: "保养间隔(天)"
        },
        {
          prop: columnMap.get("工单生成时间"),
          label: "工单生成时间"
        },
        {
          prop: columnMap.get("保养负责人"),
          label: "保养负责人"
        },
        {
          prop: columnMap.get("保养完成时间"),
          label: "保养完成时间"
        },
        { prop: "operation", label: "操作", fixed: "right" }
      ],
      fetch: getMaintenanceWorkOrderList,
      operations: [{ content: "详情", action: "detail" }]
    },
    children: [
      // 详情组件
      {
        type: "DetailBtn",
        props: {
          action: "detail",
          dialog: {
            size: "900px",
            title: "保养工单详情"
          }
        },
        children: [detailFormCreate]
      }
    ],
    on: {
      // 勾选的方法
      selectionChange: (row: any) => {
        if (row.length > 1) {
          let del_row = row.shift(); // 删除选中的第一项
          fApi.value!.el("v:table").element.toggleRowSelection(del_row, false);
        } else {
          selData.value = row;
        }
      }
    }
  }
]);
</script>
<style lang="scss" scoped>
:deep(.el-row) {
  height: 100%;

  .el-select--large .el-select__wrapper {
    min-height: initial;
  }

  .el-form-item {
    margin-right: 0;
  }

  .el-form-item--large {
    margin-bottom: 0;
  }
}

:deep(.el-table__header-wrapper) {
  .el-checkbox {
    display: none;
  }
}

.el-form-item {
  margin-right: 4px !important;
}
</style>
