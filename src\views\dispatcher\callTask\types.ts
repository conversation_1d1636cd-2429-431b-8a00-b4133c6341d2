/**
 * @file 调度管理-呼叫任务-类型声明文件
 * <AUTHOR>
 * @date 2025/7/22
 */

// table字段声明
export const columnMap: any = new Map([
  ["任务编号", "code"],
  ["状态", "status"],
  ["铲点", "shovelPoint"],
  ["地磅", "weighbridge"],
  ["卸点", "unloadPoint"],
  ["物料类型", "materialType"],
  ["呼叫人", "caller"],
  ["呼叫时间", "callTime"],
  ["矿车", "miningTruck"],
  ["开始时间", "startTime"],
  ["重量", "weight"],
  ["里程", "mileage"],
  ["结束/取消时间", "endCancelTime"]
]);
// form字段声明
export const formMap: any = new Map([
  ["矿车名称", "deviceId"],
  ["故障码", "faultCode"],
  ["故障描述", "faultDescription"],
  ["工单状态", "workOrderStatus"],
  ["报修人", "reportPersonId"],
  ["报修人名称", "reportPersonName"],
  ["维修责任人", "repairResponsiblePersonId"],
  ["维修责任人名称", "repairResponsiblePersonName"],
  ["故障时间", "faultTime"]
]);
// 任务状态枚举
export enum StatusEnum {
  /** 未开始*/
  TASK_OFFLINE = 0,
  /** 执行中*/
  TASK_FAILURE = 1,
  /** 已取消*/
  TASK_CANCEL = 2,
  /** 已完成*/
  TASK_SUCCESS = 3
}

// 状态信息
export const StatusInfo = [
  {
    bg: "rgba(101, 102, 102, 0.1)",
    status: StatusEnum.TASK_CANCEL,
    color: "rgba(101, 102, 102, 1)",
    text: "已取消"
  },
  {
    bg: "rgba(101, 102, 102, 0.1)",
    status: StatusEnum.TASK_SUCCESS,
    color: "rgba(101, 102, 102, 1)",
    text: "已完成"
  },
  {
    bg: "rgba(234, 240, 255)",
    status: StatusEnum.TASK_OFFLINE,
    color: "rgba(53, 106, 253, 1)",
    text: "未开始"
  },
  {
    bg: "rgba(229, 249, 244)",
    status: StatusEnum.TASK_FAILURE,
    color: "rgba(0, 194, 144, 1)",
    text: "执行中"
  }
];
