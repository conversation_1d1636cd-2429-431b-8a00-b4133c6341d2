<template>
  <div style="margin-top: 20px">
    <LATitle title="保养项目" />
    <form-create v-model:api="fApi" :option="option" :rule="rule"></form-create>
  </div>
</template>

<script setup>
import LATitle from "@/components/LATitle.vue";
import { computed, ref } from "vue";
import { projectColumnMap } from "@/views/maintenance/maintenanceOrder/types.ts";
import formCreate from "@form-create/element-ui";
import { MaintenanceMethodDetailFormCreate } from "@/views/maintenance/maintenanceOrder/components/detailFormCreate.ts";

const fApi = ref();
const props = defineProps({
  data: {
    type: Array,
    default: () => []
  }
});
const option = {
  form: { inline: true },
  resetBtn: false,
  submitBtn: false
};
const rule = ref([
  {
    type: "ProTable",
    style: { maxHeight: "300px" },
    props: {
      data: computed(() => {
        return props.data;
      }),
      columns: [
        {
          prop: projectColumnMap.get("保养部位"),
          label: "保养部位"
        },
        {
          prop: projectColumnMap.get("保养间隔"),
          label: "保养间隔(天)"
        },
        {
          prop: projectColumnMap.get("材料准备"),
          label: "材料准备"
        },
        { prop: "operation", label: "操作", fixed: "right" }
      ],
      // fetch: getMaintenancePositionList,
      // params: initParam.value,
      pagination: false,
      operations: [{ content: "保养方式", action: "detail" }]
    },
    children: [
      // 详情组件
      {
        type: "DetailBtn",
        props: {
          action: "detail",
          dialog: {
            class: "maintenance-knowledge-detail",
            title: "保养知识详情"
          }
        },
        children: [MaintenanceMethodDetailFormCreate]
      }
    ]
  }
]);
</script>

<style lang="scss" scoped>
:deep(.table-box) {
  border: 1px solid #ebedf1ff;
  overflow: hidden;
  th {
    background-color: rgba(245, 246, 248, 1);
  }
  tbody {
    .el-table__row:last-child {
      td {
        border-bottom: none;
      }
    }
  }
  .el-table__inner-wrapper {
    //伪元素
    &::before {
      height: 0 !important;
    }
  }
}
</style>

<style lang="scss">
.maintenance-knowledge-detail {
  width: 900px !important;
}
</style>
