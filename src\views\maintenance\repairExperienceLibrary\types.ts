/**
 * @file 维修保养-维修经验库-类型声明文件
 * <AUTHOR>
 * @date 2024/11/21
 */

// 无人矿车表格字段声明
export const TramcarColumnMap: any = new Map([
  ["故障描述", "faultDescription"],
  ["故障码", "faultCode"],
  ["维修经验", "repairExperience"],
  ["设备型号", "modelNumber"]
]);
// 表单字段声明
export const formMap = new Map([
  ["故障描述", "faultDescription"],
  ["故障码", "faultCode"],
  ["维修经验", "repairExperience"],
  ["设备型号", "modelNumber"]
]);
// 充电桩表格字段声明
export const shiftScheduleColumnMap = new Map([
  ["故障描述", "faultDescription"],
  ["故障编码", "faultCode"],
  ["维修经验", "repairExperience"],
  ["设备型号", "modelNumber"]
]);
