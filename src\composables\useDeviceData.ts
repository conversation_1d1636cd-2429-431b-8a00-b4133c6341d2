/**
 * 设备数据消费接口
 * 遵循KISS原则，提供简单的数据访问方法
 */

import { computed } from 'vue'
import { useDeviceManager } from '@/stores/deviceManager'
import type { DeviceType } from '@/stores/deviceTypes'

/**
 * 设备数据消费hook
 */
export const useDeviceData = () => {
  const manager = useDeviceManager()
  
  return {
    // 按类型获取设备列表
    mineTrains: manager.getDevicesByType('mineTrain'),
    bulldozers: manager.getDevicesByType('bulldozer'),
    chargingPiles: manager.getDevicesByType('chargingPile'),
    crushingStations: manager.getDevicesByType('crushingStation'),
    cockpits: manager.getDevicesByType('cockpit'),
    
    // 统计数据
    statistics: manager.statistics,
    
    // 查询方法
    getDeviceByCode: manager.getDeviceByCode,
    getAllDevices: manager.getAllDevices,
    
    // 状态
    isLoading: manager.isLoading,
    lastUpdated: manager.lastUpdated,
    error: manager.error,
    
    // 管理方法（供内部使用）
    initFromAPI: manager.initFromAPI,
    updateFromWebSocket: manager.updateFromWebSocket,
    updateSingleDevice: manager.updateSingleDevice
  }
}

/**
 * 获取指定类型设备的简化接口
 */
export const useDevicesByType = (type: DeviceType) => {
  const manager = useDeviceManager()
  return manager.getDevicesByType(type)
}
