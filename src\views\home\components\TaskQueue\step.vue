<template>
  <div class="steps steps-line">
    <!--第一步起点-->
    <div class="step-start step">
      <div class="step-img">
        <img alt="" src="../../imgs/startingPointImg.png" />
        <span class="step-title">起点</span>
      </div>
    </div>
    <div v-for="leftItem in taskQueueData.startList" :key="leftItem.id" class="step-process step">
      <div class="step-img">
        <img alt="" src="../../imgs/mineCarImg.png" />
        <span class="step-title">{{ leftItem?.trainName }}({{ getExecutingTask(leftItem?.taskStepList)[0].name }})</span>
      </div>
    </div>
    <!--第二步-挖机-->
    <div class="step-midway step">
      <div class="step-img">
        <img alt="" src="../../imgs/diggingMachineImg.png" />
        <span class="step-title">挖机({{ taskQueueData?.loadingPointName || 22 }})</span>
      </div>
    </div>
    <div v-for="rightItem in taskQueueData.endList" :key="rightItem.id" class="step-process step">
      <div class="step-img">
        <img alt="" src="../../imgs/mineCarImg.png" />
        <span class="step-title">{{ rightItem.trainName }}({{ getExecutingTask(rightItem?.taskStepList)[0].name }})</span>
      </div>
    </div>
    <!--todo 可能第三步-地磅-->
    <div class="step-midway step">
      <div class="step-img">
        <img alt="" src="../../imgs/weighbridge.png" />
        <span class="step-title">地磅</span>
      </div>
    </div>
    <!--第四步-破碎站-->
    <div class="step-terminal step">
      <div class="step-img">
        <img alt="" src="../../imgs/crushingStationImg.png" />
        <span class="step-title">破碎站({{ taskQueueData?.unLoadingPointName || 33 }})</span>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { PropType } from "vue";

/**
 * @file 首页-任务队列-步骤条
 * <AUTHOR>
 * @date 2025/1/23
 */
interface TaskStep {
  id: string;
  taskId: string;
  name: string;
  type: string;
  sort: number;
  status: number;
}

// 声明taskQueueList类型
interface TaskQueue {
  // 挖机
  loadingPointName: string;
  // 卸点
  unLoadingPointName: string;
  // 左侧
  startList: Array<{
    id: string;
    projectDetailsNumber: string;
    trainName: string;
    taskStepList: Array<TaskStep>;
  }>;
  // 右侧
  endList: Array<{ id: string; projectDetailsNumber: string; trainName: string; taskStepList: Array<TaskStep> }>;
}

const props = defineProps({
  // 任务队列数据
  taskQueueData: {
    type: Object as PropType<TaskQueue>,
    default: () => {}
  }
});

//写一个函数返回 status为1的数据（正在执行中）
const getExecutingTask = (list: Array<TaskStep>): TaskStep[] => {
  return list.filter(item => item.status === 1);
};

// 模拟数据渲染left插入1，2步之间，right插入2，3步之间
const list = {
  left: [
    { id: 11111, name: "装载", code: "KK-009" },
    { id: 22222, name: "空车行驶", code: "KK-001" }
  ],
  right: [
    { id: 333, name: "重车行驶", code: "KK-009" },
    { id: 2224422, name: "卸载", code: "KK-001" }
  ]
};
</script>

<style scoped>
.steps {
  height: 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 95%;
  &.steps-line {
    position: relative;
    background-color: initial;
    top: 17px;
    background-image: radial-gradient(circle 1px, rgba(202, 217, 252, 1) 0%, rgba(202, 217, 252, 1) 100%, transparent 100%);
    background-size: 10px 4px; /* 每个圆点的直径为 2px，间距为 8px */
    background-repeat: repeat-x;
    height: 3px;
  }
  background-color: rgba(243, 247, 255) !important;

  .step {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    width: 42px;
    height: 42px;

    background-color: #fff;
    text-align: center;

    .step-img {
      position: relative;
      top: 9px;
    }
    .step-title {
      position: relative;
      top: 10px;
      font-size: 14px;
      white-space: nowrap;
      color: #666666;
    }
    &.step-start {
      /* 设置左上和左下角为半圆 */
      border-top-right-radius: 50%;
      border-bottom-right-radius: 50%;
    }
    &.step-midway {
      border-radius: 50%;
    }
    &.step-terminal {
      border-top-left-radius: 50%;
      border-bottom-left-radius: 50%;
    }
    &.step-process {
      height: 42px;
      border-radius: 50%;
      .step-title {
        top: 17px;
      }
    }
  }
}
</style>
