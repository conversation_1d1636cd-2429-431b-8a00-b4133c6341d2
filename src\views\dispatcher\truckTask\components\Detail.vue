<template>
  <div>
    <LADetailCard
      :detail-data="detailData"
      :detail-labels="detailLabels"
      :tag-icon="tag"
      :title="detailData?.name"
    ></LADetailCard>
  </div>
</template>

<script lang="ts" setup>
/**
 * @file 调度管理-矿车任务管理-详情组件
 * <AUTHOR>
 * @date 2025/2/5
 */
import LADetailCard from "@/components/LADetailCard.vue";
import { TaskTypeMap, truckTaskStatusEnum } from "@/views/dispatcher/truckTask/types.ts";
import { computed, nextTick, ref } from "vue";

const props = defineProps({
  formCreateInject: {
    type: Object,
    default: () => {}
  }
});
const detailData = ref();

nextTick(() => {
  detailData.value = props.formCreateInject.api.formData();
});

// TODO : 根据实际需求修改tag
const tag = computed(() => {
  return {
    enum: truckTaskStatusEnum,
    prop: "status",
    value: detailData?.value?.status
  };
});

const detailLabels = {
  // TODO : 根据实际需求修改字段以及枚举
  status: {
    label: "任务类型",
    formatter: (value: number, data: object) => {
      return TaskTypeMap.get(value);
    }
  },
  code: {
    label: "起点"
  },
  maxFeedRate: {
    label: "终点"
  },
  maxSpeed: {
    label: "开始时间"
  },
  maxLoad: {
    label: "完成时间"
  }
};
</script>

<style scoped></style>
