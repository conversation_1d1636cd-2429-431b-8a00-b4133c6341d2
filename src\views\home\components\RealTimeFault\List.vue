<template>
  <el-scrollbar v-if="data?.length > 0">
    <form-create
      v-show="false"
      v-model:api="faultRecordFormApi"
      :option="faultRecordFormOption"
      :rule="faultRecordFormRule"
    ></form-create>
    <div v-for="item in data" :key="item?.id" class="item-data">
      <div class="item-left">
        <div class="left-top">
          {{ item?.deviceName }}
          <el-divider direction="vertical" />
          <span>故障码：{{ item?.code }}</span>
          <el-divider direction="vertical" />
          <span>{{ formatTime(item?.createDate) }}</span>
        </div>
        <div
          :style="{ color: item?.level.toString() === '1' ? 'var(--el-color-error)' : 'rgba(255, 162, 0, 1)' }"
          class="left-bottom"
        >
          <span>{{ item?.level }}级故障</span>&nbsp;
          <span>{{ item?.content }}</span>
        </div>
      </div>
      <div class="item-right">
        <SvgIcon
          :color="item?.level === 1 ? 'var(--el-color-primary)' : 'rgba(202, 217, 252, 1)'"
          icon-style="width:32px; height:32px"
          name="IconFaultMaintenance"
          @click="faultRepair(item)"
        />
      </div>
    </div>
  </el-scrollbar>
  <PlaceholderImage v-else :type="showImgType" />
</template>

<script lang="ts" setup>
/**
 * @file 首页-实时故障-列表
 * <AUTHOR>
 * @date 2025/1/21
 */
import dayjs from "dayjs";
import { computed, ref } from "vue";
import SvgIcon from "@/components/SvgIcon/index.vue";
import PlaceholderImage from "@/components/PlaceholderImage.vue";
import { useRealTimeFaultList } from "@/views/home/<USER>";
import { faultRepairForm } from "@/views/historyData/faultRecord/components/formCreate.ts";
import { useUserStore } from "@/stores/modules/user";
import { deviceRepairWorkOrderSave } from "@/api/modules/repair.ts";

// 获取个人信息
const userInfo = useUserStore().userInfo;
const { data, error, refresh } = useRealTimeFaultList();

// 格式化时间为 HH:mm:ss
const formatTime = (time: string) => {
  if (!time) return "";
  return dayjs(time).format("HH:mm:ss");
};
// 控制占位图类型noPermission/noData
const showImgType = computed(() => {
  if (error.value && error.value.status === 401) {
    return "noPermission";
  }
  return "noData";
});
// 选中的数据
const selectedData = ref();
// 报修表单
const faultRecordFormApi = ref();
const faultRecordFormOption = ref({ form: { inline: true }, resetBtn: false, submitBtn: false });
const faultRecordFormRule = ref([
  {
    type: "AddBtn",
    field: "repairBtn",
    props: {
      isCustomDialog: true,
      formatter: () => {
        return {
          faultTime: selectedData.value.createDate,
          deviceId: selectedData.value.deviceId,
          faultCode: selectedData.value.code,
          faultDescription: selectedData.value.content,
          reportPersonId: userInfo.id,
          reportPersonName: userInfo.employeeName
        };
      },
      dialog: {
        title: "故障报修",
        style: { width: "600px" },
        class: "dialog-custom-faultRecordForm"
      },
      btn: {
        style: {
          height: "32px",
          borderRadius: "6px",
          color: "#fff",
          border: "1px solid #DDE2E8",
          background: "rgba(53, 106, 253, 1)"
        },
        content: "维修",
        auth: "repair"
      },
      submitRequest: params => {
        return deviceRepairWorkOrderSave(params).then(res => {
          setTimeout(() => {
            refresh();
          }, 1000);
          return res;
        });
      }
    },
    children: [faultRepairForm]
  }
]);
const faultRepair = data => {
  if (data.level !== 1) {
    return;
  }
  // console.log(data);
  // 处理数据转化为表单数据
  const params = {
    deviceName: data.deviceName,
    content: data.content,
    createDate: data.createDate,
    code: data.code
  };
  faultRecordFormApi.value!.findRule({ name: "FaultRecordForm" }).props.rule[0].props.data = params;
  selectedData.value = data;
  faultRecordFormApi.value.exec("repairBtn", "toggle");
};
</script>

<style scoped>
:deep(.item-data) {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 0;
  border-bottom: 1px solid #ebedf1;

  .item-left {
    .left-top {
      font-size: 14px;

      .el-divider--vertical {
        height: 0.7rem;
      }
    }

    .left-bottom {
      font-size: 14px;
      margin-top: 10px;
    }
  }

  .item-right {
    cursor: pointer;
  }
}
</style>
<style lang="scss">
.dialog-custom-faultRecordForm {
  .el-form-item--large {
    margin: 20px 40px 0;
  }
}
</style>
