/* flex */
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.flex-col {
  display: flex;
  flex-direction: column;
}
.dialog-custom-width {
  margin-top: 8vh !important;
  width: 80% !important;
  min-width: 700px !important;
}
.flex-justify-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.flex-justify-around {
  display: flex;
  align-items: center;
  justify-content: space-around;
}
.flex-align-center {
  display: flex;
  align-items: center;
}

.confirmMessageStyle {
  --el-border-radius-base: 8px;
  border-radius: 16px !important;
  padding: 20px !important;
  .el-message-box__header {
    display: flex;
    .el-message-box__title {
      align-items: center;
      font-weight: bold;
      display: flex;
      gap: 6px;
      justify-content: start;
    }
    .el-message-box__headerbtn {
      height: 60px;
      width: 60px;
    }
  }
  .el-message-box__container {
    display: flex;
    justify-content: start !important;
    padding-left: 24px;
  }
  .el-message-box__btns {
    justify-content: end !important;
  }
  .el-icon {
    color: rgba(255, 187, 0);
  }
}

/* clearfix */
.clearfix::after {
  display: block;
  height: 0;
  overflow: hidden;
  clear: both;
  content: "";
}

/* 文字单行省略号 */
.sle {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 文字多行省略号 */
.mle {
  display: -webkit-box;
  overflow: hidden;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

/* 文字多了自动換行 */
.break-word {
  word-break: break-all;
  word-wrap: break-word;
}

/* fade-transform */
.fade-transform-leave-active,
.fade-transform-enter-active {
  transition: all 0.2s;
}
.fade-transform-enter-from {
  opacity: 0;
  transition: all 0.2s;
  transform: translateX(-30px);
}
.fade-transform-leave-to {
  opacity: 0;
  transition: all 0.2s;
  transform: translateX(30px);
}

/* breadcrumb-transform */
.breadcrumb-enter-active {
  transition: all 0.2s;
}
.breadcrumb-enter-from,
.breadcrumb-leave-active {
  opacity: 0;
  transform: translateX(10px);
}
//处理黑色边框问题
:focus-visible {
  outline: none;
}
/* scroll bar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
::-webkit-scrollbar-thumb {
  background-color: var(--el-border-color-darker);
  border-radius: 20px;
}

/* nprogress */
#nprogress .bar {
  background: var(--el-color-primary) !important;
}
#nprogress .spinner-icon {
  border-top-color: var(--el-color-primary) !important;
  border-left-color: var(--el-color-primary) !important;
}
#nprogress .peg {
  box-shadow:
    0 0 10px var(--el-color-primary),
    0 0 5px var(--el-color-primary) !important;
}
//字体设置
@font-face {
  font-family: "sourceHanSans";
  src: url("../assets/font/SourceHanSansCN-Regular.otf");
  font-weight: normal;
  font-style: normal;
}
.form-item-unset.form-item-unset {
  .el-form-item,
  .el-form-item--large,
  .asterisk-left,
  .el-form-item--label-right,
  .el-form-item__content {
    margin: unset !important;
    line-height: unset !important;
    display: inherit;
  }
  //.el-form-item,
  //.el-form-item__content {
  //  width: inherit;
  //  height: inherit;
  //}
}
body {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizelegibility;
  font-family: "sourceHanSans", "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑",
    Arial, sans-serif;
}
/* 外边距、内边距全局样式 */
@for $i from 0 through 100 {
  .mt#{$i} {
    margin-top: #{$i}px !important;
  }
  .mr#{$i} {
    margin-right: #{$i}px !important;
  }
  .mb#{$i} {
    margin-bottom: #{$i}px !important;
  }
  .ml#{$i} {
    margin-left: #{$i}px !important;
  }
  .pt#{$i} {
    padding-top: #{$i}px !important;
  }
  .pr#{$i} {
    padding-right: #{$i}px !important;
  }
  .pb#{$i} {
    padding-bottom: #{$i}px !important;
  }
  .pl#{$i} {
    padding-left: #{$i}px !important;
  }
}
