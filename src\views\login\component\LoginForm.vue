<template>
  <div class="login-form" @keyup.enter="login">
    <div class="welcome-title">欢迎登录</div>
    <form-create v-model="formData" v-model:api="fapi" :option="optionForm" :rule="formRules"></form-create>
    <ElCheckbox v-model="remember">记住账号密码</ElCheckbox>
    <ElButton :loading="loading" class="login-btn submit-button" type="primary" @click="login">登录</ElButton>
  </div>
</template>

<script lang="tsx" setup>
/**
 * @file 登录页面入口页
 * <AUTHOR>
 * @date 2024/11/4
 */
import { ref, onMounted } from "vue";
import formCreate from "@form-create/element-ui";
import user from "../image/user.png";
import password from "../image/lock.png";
import { HOME_URL } from "@/config";
import { loginApi, getUserInfo } from "@/api/modules/login";
import { useUserStore } from "@/stores/modules/user";
import { useTabsStore } from "@/stores/modules/tabs";
import { useKeepAliveStore } from "@/stores/modules/keepAlive";
import { initDynamicRouter } from "@/routers/modules/dynamicRouter";
import { ElMessage } from "element-plus";
import { useRouter } from "vue-router";
import { UserStatus } from "./types";
import { useAuthStore } from "@/stores/modules/auth";
import { localGet, localRemove, localSet } from "@/utils";
interface FApi {
  submit: (callback: (formData: FormData, fApi: FApi) => void) => void;
}
// 定义类型
interface FormData {
  username?: string | null;
  password?: string | null;
}
// 页面加载时读取Local
onMounted(() => {
  getLocal();
});
const emits = defineEmits(["update:visible", "update:oldPassword"]);

const router = useRouter();
const userStore = useUserStore();
const tabsStore = useTabsStore();
const keepAliveStore = useKeepAliveStore();
const fapi = ref<FApi | null>(null);
const formData = ref<FormData>({});
const loading = ref<boolean>(false);
const remember = ref<boolean>(false);
const authStore = useAuthStore();

const login = async (): Promise<void> => {
  fapi.value?.submit(async (formData: FormData) => {
    loading.value = true;
    try {
      // 打开密码重置弹窗
      // 1. 执行登录接口
      const res = (await loginApi({ ...formData, password: formData.password ?? "".toString() })) as any;
      if (res.jwtToken) {
        userStore.setToken(res.jwtToken);
        // 2. 获取用户信息
        const { data: userInfo } = (await getUserInfo()) as any;
        userStore.setUserInfo(userInfo);
        // 如果当前用户的状态为密码待改
        if (Number(userInfo.status) === UserStatus.PASSWORD_CHANGE) {
          // 打开密码重置弹窗
          emits("update:visible", true);
          // 将旧密码传递给外层组件,外层组件传递给密码重置弹窗组件
          emits("update:oldPassword", formData.password);
        } else if (Number(userInfo.status) === UserStatus.FREEZE) {
          // 账户已冻结
          ElMessage.error("该用户已被冻结,请联系管理员");
          userStore.setToken("");
          userStore.setUserInfo({});
        } else if (Number(userInfo.status) === UserStatus.NORMAL) {
          // 如果账户的状态为正常,则进行菜单数据的获取
          // 2. 添加动态路由
          await initDynamicRouter();

          // 3. 清空 tabs、keepAlive 数据
          await tabsStore.setTabs([]);
          await keepAliveStore.setKeepAliveName([]);

          // 4. 如果勾选了“记住账号密码”，则保存账号和密码到Local
          if (remember.value && formData.username && formData.password) {
            setLocal(formData.username, formData.password); // 保存密码
          } else {
            clearLocal(); // 清除Local
          }
          // 5. 当权限处理完成后 跳转到首页防止路由未加载完成跳转到错误页面 如果没有任何菜单权限,会跳转到登录页
          if (authStore.authMenuList.length > 0) {
            // 6.储存密码用于修改密码校验
            userStore.setPassword(formData.password as string);
            await router.push(HOME_URL);
          }
        }
      } else {
        ElMessage.error("账号或密码错误");
      }
    } catch (error) {
      console.error("登录失败:", error);
      // 你可以在这里添加错误处理逻辑，例如显示错误信息
    } finally {
      loading.value = false;
    }
  });
};
// 返回一个账号图片
const userImg = () => {
  return <img src={user} alt="" />;
};
// 记住密码功能的实现
//设置Local
const setLocal = (c_name: string, c_pwd: string) => {
  localSet("username", c_name);
  localSet("password", c_pwd);
};
//读取Local
const getLocal = () => {
  if (localGet("username") && localGet("password")) {
    formData.value.username = localGet("username"); //保存到保存数据的地方
    formData.value.password = localGet("password");
    if (formData.value.username && formData.value.password) remember.value = true;
  }
};
//清除Local
const clearLocal = () => {
  localRemove("username");
  localRemove("password");
  // 清除cookie
  // document.cookie = "username=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
  // document.cookie = "password=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
};
// 返回一个密码图片
const passwordImg = () => {
  return <img src={password} alt="" />;
};

const optionForm = ref({
  submitBtn: false
});
const formRules = ref([
  {
    type: "input",
    field: "username",
    title: "账号",
    value: "",
    children: [
      {
        type: "div",
        slot: "prefix",
        class: "login-prefix",
        children: [
          {
            component: userImg
          }
        ]
      }
    ],
    props: {
      placeholder: "请输入账号 admin/user",

      clearable: true,
      class: "form-login"
    },
    effect: {
      required: "请输入账号" //自定义错误信息
    }
  },
  {
    type: "input",
    field: "password",
    class: "form-login",
    props: {
      placeholder: "请输入密码 666666",
      clearable: true,
      type: "password",
      showPassword: true
    },
    children: [
      {
        type: "div",
        slot: "prefix",
        class: "login-prefix",
        children: [
          {
            component: passwordImg
          }
        ]
      }
    ],
    title: "密码",
    value: "",
    effect: {
      required: "请输入密码" //自定义错误信息
    }
  }
]);
</script>

<style lang="scss" scoped>
@import "../index.scss";
.login-form {
  width: 380px;
  margin-bottom: 40px;
  :deep(.el-form-item--large) {
    margin-bottom: 20px;
  }
}

:deep(.form-login) {
  height: 50px;
}
:deep(.login-prefix) {
  display: flex;
  img {
    height: 20px;
    width: 20px;
  }
}
:deep(.el-input) {
  --el-input-hover-border-color: #356afdff;
  --el-input-border-color: #b5c8f3ff;
  --el-text-color-placeholder: #a6bdf3ff;
  --el-border-radius-base: 12px;
  &::placeholder {
    color: #a6bdf3;
  }
}

:deep(.el-input--large .el-input__wrapper) {
  background: #e9f0ff;
  height: 50px;
}
:deep(.el-checkbox__inner) {
  border-radius: 0 !important;
}
:deep(.el-checkbox__label) {
  padding-left: 4px;
}
:deep(.el-checkbox--large) {
  margin-bottom: 20px;
  height: fit-content;
}
</style>
