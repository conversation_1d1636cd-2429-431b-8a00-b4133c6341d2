/**
 * @file 项目下拉选择通用组件
 * <AUTHOR>
 * @date 2022/4/24
 */
import { defineComponent, ref, watch, VNode } from "vue";
import { ElOption, ElOptionGroup, ElSelect } from "element-plus";

// select默认字段
const option = {
  // 默认key字段
  key: "key",
  // 默认label字段
  label: "label",
  // 默认value字段
  value: "value"
};

export default defineComponent({
  name: "LASelect",
  props: {
    // 请求下拉列表数据的请求方法
    fetch: { type: Function },
    // 发送请求携带的请求参数
    params: { type: Object },
    // 替换select默认的字段
    replaceFields: { type: Object },
    // 由外部直接传入相关select列表数据
    list: { type: Array, default: () => [] },
    // 是否直接禁用select,而不是单独的option选项
    selectDisabled: { type: Boolean, default: false },
    // 禁用的选项列表
    disabled: { type: Array, default: () => [] },
    // 依赖值变化后重新请求数据
    afterChangeFetch: { type: Object },
    // 自定义显示label，用于展示选项与选择后显示框label不一向的情况
    /**
     * <AUTHOR>
     * @date 2025/1/14
     * @description LASelect处理展示选项label的方法
     * @param {Object} row 当前行的数据
     * @param {String} label 当前选项label
     * @param {String} replaceFieldsLabel 设置的选项label字段名
     * @return {VNode | string} 处理后的数据
     */
    renderLabel: { type: Function },
    // 分组式的select
    isGroup: { type: Boolean, default: false },
    // 过滤数据的函数(两个参数（需要过滤的条件）-不需要过滤的添加)
    /**
     * <AUTHOR>
     * @date 2025/1/13
     * @description LASelect处理数据的方法
     * @param {Object} row 当前行的数据
     * @param {String} val 当前输入的值
     * @param {Array} list 当前下拉列表的数据
     * @param {Function} api form-create的api对象
     * @return {Array} 处理后的下拉列表数据
     */
    filterFetch: { type: Function },
    formCreateInject: { type: Object, default: false }
  },
  // 触发事件，ElSelect的change事件只抛出选中的值，自定义一个change事件，不仅抛出值，把该选项也进行抛出
  emits: ["change"],
  setup(props, { emit, expose, slots: slots }) {
    const replaceFields = { ...option, ...props.replaceFields };
    // 设置默认数据为 props.list
    const options: any = ref(props.list);
    // list值改变重新赋值给下拉框option数据
    watch(
      () => props.list,
      list => (options.value = list)
    );
    // 如果外部传入请求函数，调用请求函数接收响应重新赋值给数据
    const fetchData = () => {
      if (props.fetch) {
        props.fetch(props.params).then((res: any) => {
          let list: any = [];
          // 对后端的数据格式进行兼容，有些数据key为data，有些数据key为records
          if (res && Array.isArray(res)) list = res;
          else if (res?.data && Array.isArray(res?.data)) list = res.data;
          else if (res?.data?.records && Array.isArray(res?.data?.records)) list = res.data.records;
          // 如果外部传入过滤的方法函数(包含需要过滤以及不需要过滤的方法)
          if (props.filterFetch) {
            list = props.filterFetch(
              props.formCreateInject.api.formData(),
              props.formCreateInject.rule.value,
              list,
              props.formCreateInject
            );
          }
          options.value = list;
        });
      }
    };
    fetchData();
    watch(
      () => props.afterChangeFetch,
      () => {
        return fetchData();
      },
      {
        deep: true
      }
    );
    /**
     * 选中项变化的时候触发的change事件处理函数,把选中的值，以及选中的选项一起进行抛出
     * @param value
     * <AUTHOR>
     * @date 2024/11/18
     */
    const handleChange = value => {
      let chooseOption = null;
      if (Array.isArray(value)) value = value[value.length - 1]; // 如果value是数组，取最后一项
      if (props.isGroup) {
        // 分组模式下查找选中的项
        for (const group of options.value) {
          chooseOption = group.options.find(item => item[replaceFields.value] == value);
          if (chooseOption) break; // 找到选中的项后直接退出循环
        }
      } else {
        // 非分组模式下查找选中的项
        chooseOption = options.value.find(item => item[replaceFields.value] == value);
      }

      emit("change", value, chooseOption, props.formCreateInject);
    };

    expose({ fetchData });

    return () => {
      const slotContent = slots.default;
      const optionContent = item => {
        // 插槽
        if (slotContent) {
          // @ts-ignore
          return slots.default({ item });
        } else {
          if (props.renderLabel) {
            return props.renderLabel(item, item[replaceFields.label], replaceFields.label);
          }
          return <span>{item[replaceFields.label]}</span>;
        }
      };
      return (
        <ElSelect
          value-on-clear=""
          clearable
          disabled={props.selectDisabled}
          filterable
          onChange={handleChange}
          size="large"
          style={{ width: "100%" }}
        >
          {props.isGroup
            ? options.value.map(group => {
                if (group.children?.length > 0) {
                  return (
                    <ElOptionGroup key={group[replaceFields.key]} label={group[replaceFields.label]}>
                      {group.children?.map(item => (
                        <ElOption
                          key={item[replaceFields.key]}
                          label={item[replaceFields.label]}
                          value={item[replaceFields.value]}
                          disabled={props.disabled.includes(item[replaceFields.key])}
                        >
                          {optionContent(item)}
                        </ElOption>
                      ))}
                    </ElOptionGroup>
                  );
                }
                return "";
              })
            : options.value.map(item => (
                <ElOption
                  key={item[replaceFields.key]}
                  label={item[replaceFields.label]}
                  value={item[replaceFields.value]}
                  disabled={props.disabled.includes(item[replaceFields.key])}
                >
                  {optionContent(item)}
                </ElOption>
              ))}
        </ElSelect>
      );
    };
  }
});
