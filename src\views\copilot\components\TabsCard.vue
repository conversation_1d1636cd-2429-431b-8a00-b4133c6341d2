<template>
  <div class="tabs-card">
    <div class="tabs-card-header">
      <div
        class="tabs-card-header-item"
        :style="{ width: `calc(100% / ${tabs.length})`, cursor: tabs.length > 1 ? 'pointer' : 'default' }"
        :class="tabs.length > 1 && item.key === activeTab && 'active'"
        v-for="item in tabs"
        :key="item.key"
        @click="onClick(item.key)"
      >
        <div class="tabs-card-header-item-title">{{ item.title }}</div>
      </div>
    </div>
    <el-scrollbar style="height: 100%; background-color: var(--card-bg-color)">
      <slot :name="activeTab"></slot>
    </el-scrollbar>
  </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
const props = withDefaults(defineProps<{ tabs: { key: string; title: string }[] }>(), { tabs: () => [] });
const activeTab = ref(props.tabs[0].key);
const onClick = key => {
  activeTab.value = key;
};
</script>

<style lang="scss" scoped>
.tabs-card {
  width: 100%;
  font-size: 14px;
}
.tabs-card-header {
  width: 100%;
  display: flex;
  align-items: center;
  height: 40px;
  gap: 2px;
  background-color: var(--el-bg-color);
}

.tabs-card-header-item {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--card-bg-color);
  cursor: pointer;
  box-sizing: border-box;
  border-bottom: 2px solid var(--el-bg-color);
  &.active {
    background: RGBA(24, 36, 78, 1);
    border-bottom: 2px solid rgba(0, 209, 217, 0.5);
  }
}
</style>
