<template>
  <div class="profiles">
    <div class="avatar">
      <img alt="" src="../../img/avatarIcon.svg" style="width: 60px; height: 60px" />
      <div style="display: flex; align-items: center; gap: 4px; margin-top: 10px">
        <span class="name">{{ data?.employeeName || "" }}</span>
      </div>
    </div>
    <div class="detail">
      <div v-for="item of profiles" :key="item.name" class="row">
        <span class="label">{{ item.name }}</span>
        <span class="value">{{ data[item.field] }}</span>
      </div>
    </div>
    <!--修改资料-->
    <form-create v-model:api="fApi" :option="option" :rule="rule"></form-create>
  </div>
</template>

<script lang="ts" setup>
/**
 * @file 左侧个人资料组件模块
 * <AUTHOR>
 * @date 2024/11/13
 */
import { computed, ref, watch } from "vue";
import formCreate from "@form-create/element-ui";
import { infoFormCreate } from "@/views/profile/AccountCenter/formCreate";
import { addUser } from "@/api/modules";

import { ResultEnum } from "@/enums/httpEnum";

const props = defineProps<{ data: any }>();
// 修改资料
const fApi = ref();
const option = {
  resetBtn: false,
  submitBtn: false,
  row: {
    justify: "center"
  }
};
// 资料数据
const model = ref({ ...props.data });
const emit = defineEmits(["updateUserInfo"]);

const rule = ref([
  {
    type: "AddBtn",
    name: "modifyData",
    props: {
      btn: {
        // 根据超管角色动态显示按钮 如果是超管为false，否则为true
        disabled: computed(() => {
          // 超管 return model.value.roleName.indexOf("超管") !== -1;
          return model?.value?.roleCode?.indexOf("ROLE_ADMIN") !== -1;
        }),
        icon: "",
        style: computed(() => {
          // return model.value.roleName.indexOf("超管") == -1;
          return model?.value?.roleCode?.indexOf("ROLE_ADMIN") !== -1
            ? {
                width: "86px",
                height: "32px",
                borderRadius: "6px",
                color: "rgba(189, 189, 191, 1)",
                border: "1px solid rgba(189, 189, 191, 1)",
                background: "#fff"
              }
            : {
                width: "86px",
                height: "32px",
                borderRadius: "6px",
                color: "#000",
                border: "1px solid #DDE2E8",
                background: "#fff"
              };
        }),
        content: "修改资料"
      },
      // 是否是自定义表单弹窗 未嵌入表格只是显示弹窗form
      isCustomDialog: true,
      formatter: () => {
        // console.log("个人资料", model.value);
        return model.value;
      },
      dialog: { title: "修改资料" },
      submitRequest: (data, api) => {
        // 提交的请求
        return addUser({ ...data, roleId: Array.isArray(data.roleId) ? data.roleId?.join(",") : data.roleId }).then(res => {
          // 更新仓库个人信息以及当前页面刷新
          if (res.statusCode === ResultEnum.SUCCESS) emit("updateUserInfo");
          return res;
        });
      }
    },
    children: [infoFormCreate]
  }
]);

watch(
  () => props.data,
  newVal => {
    model.value = { ...newVal, roleId: newVal.roleId?.split(",") || [] };
  }
);

const profiles = [
  { name: "账号", field: "userName" },
  { name: "手机电话", field: "mobile" },
  { name: "角色", field: "roleName" },
  { name: "所属部门", field: "orgName" },
  { name: "职位", field: "job" }
];
</script>

<style lang="scss" scoped>
.avatar {
  width: 100%;
  height: 134px;
  background: rgba(53, 106, 253, 0.1);
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-bottom: 30px;
}
.profiles {
  padding: 20px;
  width: 360px;
  height: 100%;
  background: #ffffff;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  .row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
    .label {
      color: rgba(151, 153, 152, 1);
    }
    .value {
      color: rgba(26, 26, 26, 1);
    }
  }
}
</style>
