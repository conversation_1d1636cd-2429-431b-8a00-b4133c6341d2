<template>
  <div class="record-info">
    <div class="username">{{ recordList.personName }}</div>
    <div class="record-right">
      <div class="record-content">
        <div class="time">{{ dayjs(recordList.createDate).format("YYYY-MM-DD HH:mm:ss") }}</div>
        <div class="content">
          <div style="white-space: pre-line; line-height: 1.6">{{ recordList.record }}</div>
        </div>
      </div>
      <div class="record-btn">
        <!--修改-->
        <form-create v-model:api="editApi" :option="editOption" :rule="editRule"></form-create>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
/**
 * @file 维修保养-维修经验-详情-下方记录
 * <AUTHOR>
 * @date 2025/2/12
 */

import { ref, onMounted } from "vue";
import formCreate from "@form-create/element-ui";
import { saveRepairExperienceRecord } from "@/api/modules/repair.ts";
import { userEditExperienceFormCreate } from "@/views/maintenance/repairExperienceLibrary/components/formCreate.ts";
import dayjs from "dayjs";
formCreate.component("userEditExperienceFormCreate", userEditExperienceFormCreate);

const props = defineProps({
  recordList: {
    type: Object,
    default: () => []
  }
});
const emit = defineEmits(["refresh"]);
// 编辑配置
const editApi = ref();
const editOption = {
  resetBtn: false,
  submitBtn: false,
  row: {
    justify: "center"
  }
};
const editRule = ref([
  {
    type: "AddBtn",
    props: {
      btn: {
        icon: "",
        style: {
          height: "32px",
          borderRadius: "6px",
          color: "#000",
          border: "1px solid #DDE2E8",
          background: "#fff"
        },
        content: "修改"
      },
      isCustomDialog: true,
      formatter: () => {
        return props.recordList;
      },
      dialog: { title: "修改用户经验" },
      submitRequest: params => {
        return saveRepairExperienceRecord({ ...params }).then(res => {
          emit("refresh");
          return res;
        });
      }
    },
    children: [userEditExperienceFormCreate]
  }
]);
onMounted(() => {
  editRule.value[0].children[0].props.rule[0].value = props.recordList.record;
});
</script>

<style lang="scss" scoped>
div {
  line-height: 100%;
}

.record-info {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 25px;
  margin-top: 20px;

  .record-right {
    padding-bottom: 20px;
    display: flex;
    flex: 1;
    align-items: flex-start;
    justify-content: space-between;
    border-bottom: 1px solid #ccc;
  }

  .username {
    height: 100%;
    font-weight: bold;
  }

  .time {
    margin-bottom: 20px;
  }

  .content {
    display: flex;
    flex-direction: column;
    gap: 10px;
    color: #666666;
  }

  .record-btn {
    display: flex;
    gap: 5px;
  }
}
</style>
