<template>
  <MessageBox
    ref="messageBoxRef"
    v-bind="messageProps"
    :beforeClose="handleBeforeClose"
    @confirm="handleConfirm"
    @cancel="handleCancel"
    :messageBox="false"
    dialogContentStyle="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 60px; margin-top: inherit"
  >
    <el-select
      v-model="selectedTask"
      placeholder="请选择铲点"
      size="large"
      popper-class="add-to-task-message-box"
      style="width: 80%; margin-top: 10px; margin-left: 30px"
    >
      <el-option v-for="item of tasks" :key="item.id" :label="item.name" :value="item.id" />
    </el-select>
  </MessageBox>
</template>
<script lang="ts" setup>
import { ref } from "vue";
import MessageBox from "../../../MessageBox.vue";
import { useDeviceSelection } from "@/views/copilot/store";
import { getBulldozersList, addDispatchTask } from "@/api/modules/copilot";
import { ElMessage } from "element-plus";

interface TaskOption {
  name: string;
  id: string;
}

const { selectedDeviceInfo } = useDeviceSelection();
const messageBoxRef = ref();
const messageProps = ref({ title: `一键发车-${selectedDeviceInfo?.value?.rawData?.name}`, message: "" });

// 任务列表
const tasks = ref<TaskOption[]>([]);
const selectedTask = ref("");
// 获取任务列表
const getTasks = async () => {
  const response = (await getBulldozersList().then(res => res?.data || [])) as TaskOption[];
  if (response) {
    tasks.value = response;
  }
};

const handleBeforeClose = () => {
  return new Promise(resolve => {
    if (!selectedTask.value) {
      resolve("请选择铲点");
    } else {
      resolve(true);
    }
  });
};
const handleConfirm = async () => {
  if (!selectedTask.value) {
    ElMessage.warning("请选择铲点");
    return;
  }
  await addDispatchTask({
    trainId: selectedDeviceInfo.value?.id,
    bulldozersId: selectedTask.value
  });
  // ElMessage({
  //   type: "success",
  //   message: "操作成功"
  // });
};
const handleCancel = () => {
  // messageProps.value.visible = false;
};
const show = () => {
  messageBoxRef.value.show();
  getTasks();
};
// 暴露方法给父组件
defineExpose({ show });
</script>

<style lang="scss">
.add-to-task-message-box {
  --el-bg-color-overlay: #3b3ca7;
  --el-box-shadow-light: 0px 0px 12px #252688;
  --el-fill-color-light: #2e2f9a;
}
</style>
