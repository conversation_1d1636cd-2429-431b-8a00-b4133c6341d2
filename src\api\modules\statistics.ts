import http from "@/api";
/**
 * @name 统计分析
 */

// 销售统计-销售数据-销量卡片
export const getSalesDataCard = params => {
  return http.post("/product-server/product/salesStatistics/getSalesVolume", params);
};
// 销售统计-销售数据-销量折线图
export const getSalesLine = params => {
  return http.post("/product-server/product/salesStatistics/getLineForSalesVolume", params);
};
// 销售统计-销售数据-订单量卡片
export const getOrderDataCard = params => {
  return http.post("/product-server/product/salesStatistics/getOrderVolume", params);
};
// 销售统计-订单量-折线图
export const getOrderLine = params => {
  return http.post("/product-server/product/salesStatistics/getLineForOrderVolume", params);
};
// 销售统计-销售数据-销售额卡片
export const getSalesAmountCard = params => {
  return http.post("/product-server/product/salesStatistics/getOrderAmount", params);
};
// 销售统计-销售额-折线图
export const getSalesAmountLine = params => {
  return http.post("/product-server/product/salesStatistics/getLineForSalesAmount", params);
};
// 销售统计-销售数据-退款金额卡片
export const getRefundAmountCard = params => {
  return http.post("/product-server/product/salesStatistics/getRefundAmount", params);
};
// 销售统计-退款率-折线图
export const getRefundRateLine = params => {
  return http.post("/product-server/product/salesStatistics/getLineForRefundAmount", params);
};
// 销售统计-销售数据-手续费卡片
export const getHandFeeCard = params => {
  return http.post("/product-server/product/salesStatistics/getHandFee", params);
};
// 销售统计-销售数据-净销售额卡片
export const getNetSalesAmountCard = params => {
  return http.post("/product-server/product/salesStatistics/getNetSales", params);
};
// 销售统计-销售详情-列表
export const getSalesDetailList = params => {
  return http.post("/product-server/product/salesStatistics/listQueryByPage", params);
};

// 客户偏好-温度-饼图
export const getTemperaturePie = params => {
  return http.post("/product-server/product/customerPreferences/getTemperature", params, {
    cancel: false
  });
};
// 客户偏好-温度杯数-折线图
export const getTemperatureLine = params => {
  return http.post("/product-server/product/customerPreferences/getLineForTemperature", params);
};
// 客户偏好-甜度-饼图
export const getSugarPie = params => {
  return http.post("/product-server/product/customerPreferences/getSweetness", params);
};
// 客户偏好-甜度杯数-折线图
export const getSugarLine = params => {
  return http.post("/product-server/product/customerPreferences/getLineForSweet", params);
};
// 客户偏好-商品分类排名
export const getCategoryRank = params => {
  return http.post("/product-server/product/customerPreferences/getTopNByProductCategories", params);
};
// 客户偏好-商品规格排名
export const getSpecRank = params => {
  return http.post("/product-server/product/customerPreferences/getTopNBySpecifications", params);
};

// 消费时段-订单量占比
export const getOrderNumRatio = params => {
  return http.post("/product-server/product/productOrder/getOrderVolumeProportion", params);
};
// 消费时段-周订单量
export const getWeekOrderNum = params => {
  return http.post("/product-server/product/productOrder/getWeeklyOrderVolume", params);
};
// 消费时段-日订单量
export const getDayOrderNum = params => {
  return http.post("/product-server/product/productOrder/getDayOrderVolumeByAll", params);
};
// 消费时段-渠道日订单量
export const getDayOrderNumByChannel = params => {
  return http.post("/product-server/product/productOrder/getDayOrderVolumeByType", params);
};
// 消费时段-商品日销量
export const getDayOrderNumByProduct = params => {
  return http.post("/product-server/product/productOrderDetail/getDaySalesByGoods", params);
};
// 日销售额
export const getDaySalesAmount = params => {
  return http.post("/product-server/product/productOrderDetail/getDaySalesVolume", params);
};

// 用户行为-订单渠道
export const getOrderChannel = params => {
  return http.get("/product-server/product/userBehavior/orderChannel", params);
};
// 用户行为-订单杯数
export const getOrderCups = params => {
  return http.get("/product-server/product/userBehavior/orderCups", params);
};
// 用户行为-复购率
export const getRepurchaseRate = params => {
  return http.get("/product-server/product/userBehavior/repurchaseRate", params);
};
// 用户行为-客单价
export const getCustomerPrice = params => {
  return http.get("/product-server/product/userBehavior/orderPrice", params);
};
// 用户行为-复购周期
export const getRepurchaseCycle = params => {
  return http.get("/product-server/product/userBehavior/repurchaseCycle", params);
};
// 用户行为-购买次数
export const getPurchaseFrequency = params => {
  return http.get("/product-server/product/userBehavior/purchaseFrequency", params);
};
// 用户行为-首购率
export const getFirstPurchaseRate = params => {
  return http.get("/product-server/product/userBehavior/firstPurchaseRate", params);
};
// 用户行为-客流转化率
export const getConversity = params => {
  return http.get("/product-server/product/userBehavior/getOrderConversion", params);
};

// 营销统计-活动促销-活动新增用户数
export const getActivityNewUser = params => {
  return http.post("/product-server/product/statisticalAnalysis/getPromotionAddUserCount", params);
};
// 营销统计-活动促销-活动排名
export const getActivityRank = params => {
  return http.post("/product-server/product/statisticalAnalysis/getPromotionActivitiesList", params);
};
// 营销统计-优惠券-领取用户数
export const getCouponClaimUser = params => {
  return http.post("/product-server/product/statisticalAnalysis/getCouponUserCount", params);
};
// 营销统计-优惠券-优惠券排名
export const getCouponRank = params => {
  return http.post("/product-server/product/statisticalAnalysis/getCouponList", params);
};

// 评价统计
// 评价统计-商品-评分统计
export const getGoodsScore = params => {
  return http.post("/product-server/product/productOrderEvaluate/goodsScore", params, {
    headers: { "Content-Type": "application/json" }
  });
};
// 评价统计-商品-评分占比
export const getGoodsScorePercent = params => {
  return http.post("/product-server/product/productOrderEvaluate/goodsScoreProportion", params, {
    headers: { "Content-Type": "application/json" }
  });
};
// 评价统计-商品-月均评分
export const getGoodsScoreMonth = params => {
  return http.post("/product-server/product/productOrderEvaluate/goodsMonthlyScore", params, {
    headers: { "Content-Type": "application/json" }
  });
};
// 评价统计-商品-评价详情列表
export const getGoodsEvaluateList = params => {
  return http.post("/product-server/product/productOrderEvaluate/goodsEvaluationDetails", params, {
    headers: { "Content-Type": "application/json" }
  });
};

// 评价统计-车辆-评分统计
export const getCarScore = params => {
  return http.post("/product-server/product/productOrderEvaluate/carScore", params, {
    headers: { "Content-Type": "application/json" }
  });
};
// 评价统计-车辆-评分占比
export const getCarScorePercent = params => {
  return http.post("/product-server/product/productOrderEvaluate/carScoreProportion", params, {
    headers: { "Content-Type": "application/json" }
  });
};
// 评价统计-车辆-月均评分
export const getCarScoreMonth = params => {
  return http.post("/product-server/product/productOrderEvaluate/carMonthlyScore", params, {
    headers: { "Content-Type": "application/json" }
  });
};

// 评价统计-车辆-评价详情列表
export const getCarEvaluateList = params => {
  return http.post("/product-server/product/productOrderEvaluate/carEvaluationDetails", params, {
    headers: { "Content-Type": "application/json" }
  });
};
// 矿车故障统计-故障次数
export const getMiningCarFaultCount = params => {
  return http.post("/dispatch-server/dispatch/faults/getDispatchFaultsCount", params);
};
// 矿车故障统计-故障次数折线图
export const getMiningCarFaultCountLine = params => {
  return http.post("/dispatch-server/dispatch/faults/getDispatchFaultsCountForLine", params);
};
// 矿车故障统计-故障持续时长
export const getMiningCarFaultDuration = params => {
  return http.post("/dispatch-server/dispatch/faults/getDispatchFaultsDuration", params);
};
// 矿车故障统计-故障持续时长饼图
export const getMiningCarFaultDurationPie = params => {
  return http.post("/dispatch-server/dispatch/faults/getDispatchFaultsCountForPie", params);
};
// 矿车故障统计-平均持续时长
export const getMiningCarFaultDurationAvg = params => {
  return http.post("/dispatch-server/dispatch/faults/getDispatchFaultsDurationAvg", params);
};
// 矿车故障统计-平均响应时长
export const getMiningCarFaultResponseAvg = params => {
  return http.post("/dispatch-server/dispatch/faults/getDispatchFaultsRespTimeAvg", params);
};
// 矿车故障统计-故障详情列表
export const getMiningCarFaultDetailList = params => {
  return http.post("/dispatch-server/dispatch/faults/getDispatchFaultsDetail", params);
};
