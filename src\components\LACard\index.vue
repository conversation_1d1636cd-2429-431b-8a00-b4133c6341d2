<template>
  <el-card class="box-card" v-bind="$attrs">
    <template v-if="showHeader" #header>
      <div class="card-header">
        <slot name="header">
          <b>{{ title }}</b>
        </slot>
      </div>
    </template>
    <!-- 卡片内容插槽 -->
    <slot> </slot>
  </el-card>
</template>

<script lang="ts" setup>
/**
 * @file 通用卡片组件
 * <AUTHOR>
 * @date 2022/4/28
 */
withDefaults(
  defineProps<{
    // 是否显示卡片header
    showHeader?: boolean;
    // 卡片header的title
    title?: string;
  }>(),
  {
    showHeader: true,
    title: "卡片标题"
  }
);
</script>

<style scoped>
.box-card {
  --el-card-border-radius: 8px;
  flex: 1;
  width: 100%;
  height: 100%;
}
:deep(.el-card__header) {
  background: rgba(238, 243, 255, 1);
}
</style>
