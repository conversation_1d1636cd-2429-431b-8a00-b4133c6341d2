<template>
  <div>
    <LADetailCard :detail-data="detailData" :detail-labels="detailLabels" :title="detailData?.position"></LADetailCard>
  </div>
</template>

<script lang="tsx" setup>
/**
 * @file 维修保养-保养经验库-无人奶茶车-详情组件
 * <AUTHOR>
 * @date 2025/2/26
 */
import LADetailCard from "@/components/LADetailCard.vue";
import { nextTick, ref } from "vue";
import { TramcarColumnMap } from "@/views/maintenance/maintenanceExperienceLibrary/types.ts";
const props = defineProps({
  formCreateInject: {
    type: Object,
    default: () => {}
  }
});
const detailData = ref();
nextTick(() => {
  detailData.value = props.formCreateInject.api.formData();
  // console.log("保养经验库", detailData.value);
});

const detailLabels = {
  [TramcarColumnMap.get("售卖车型号名称")]: {
    label: "奶茶车型号"
  },
  [TramcarColumnMap.get("保养间隔(天)")]: {
    label: "保养间隔"
  },
  [TramcarColumnMap.get("材料准备")]: {
    label: "材料准备"
  },
  [TramcarColumnMap.get("保养方式")]: {
    label: "保养方式",
    formatter: (value: string, data: object) => {
      // value返回的是html
      return <div v-html={value}></div>;
    }
  }
};
</script>

<style scoped></style>
