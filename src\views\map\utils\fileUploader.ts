import { ref } from 'vue'

/**
 * 支持的文件类型枚举
 */
export enum SupportedFileType {
  OSM = 'osm',
  TILES = '3d-tiles'
}

/**
 * 文件上传结果接口
 */
export interface FileUploadResult {
  fileName: string
  fileType: SupportedFileType
  fileContent: string | ArrayBuffer | null
  fileSize: number
}

/**
 * 文件上传配置接口
 */
export interface FileUploadConfig {
  /** 允许的文件类型 */
  acceptedTypes: SupportedFileType[]
  /** 文件大小限制（单位：MB） */
  maxSize?: number
}

/**
 * 文件上传错误类型
 */
export class FileUploadError extends Error {
  constructor(message: string) {
    super(message)
    this.name = 'FileUploadError'
  }
}

/**
 * 文件上传工具类
 */
export class FileUploader {
  /**
   * 验证文件类型是否支持
   * @param fileName 文件名
   * @param acceptedTypes 支持的文件类型数组
   * @returns 文件类型是否支持
   */
  private static validateFileType(
    fileName: string,
    acceptedTypes: SupportedFileType[]
  ): SupportedFileType {
    const extension = fileName.split('.').pop()?.toLowerCase()
    
    if (extension === 'osm' && acceptedTypes.includes(SupportedFileType.OSM)) {
      return SupportedFileType.OSM
    }
    
    if (extension === 'json' && acceptedTypes.includes(SupportedFileType.TILES)) {
      return SupportedFileType.TILES
    }
    
    throw new FileUploadError('不支持的文件类型')
  }

  /**
   * 验证文件大小
   * @param fileSize 文件大小（字节）
   * @param maxSize 最大允许大小（MB）
   */
  private static validateFileSize(fileSize: number, maxSize?: number): void {
    if (maxSize && fileSize > maxSize * 1024 * 1024) {
      throw new FileUploadError(`文件大小超过限制 ${maxSize}MB`)
    }
  }

  /**
   * 读取文件内容
   * @param file 文件对象
   * @returns Promise<string | ArrayBuffer | null>
   */
  private static readFile(file: File): Promise<string | ArrayBuffer | null> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      
      reader.onload = () => resolve(reader.result)
      reader.onerror = () => reject(new FileUploadError('文件读取失败'))
      
      if (file.name.endsWith('.osm')) {
        reader.readAsText(file) // OSM文件读取为文本
      } else {
        reader.readAsArrayBuffer(file) // 3D Tiles文件读取为二进制
      }
    })
  }

  /**
   * 上传单个文件
   * @param file 文件对象
   * @param config 上传配置
   * @returns Promise<FileUploadResult>
   */
  public static async uploadSingleFile(
    file: File,
    config: FileUploadConfig
  ): Promise<FileUploadResult> {
    const fileType = this.validateFileType(file.name, config.acceptedTypes)
    this.validateFileSize(file.size, config.maxSize)

    const content = await this.readFile(file)

    return {
      fileName: file.name,
      fileType,
      fileContent: content,
      fileSize: file.size
    }
  }

  /**
   * 创建文件上传处理器
   * @param config 上传配置
   * @returns 文件上传处理相关方法和状态
   */
  public static createUploader(config: FileUploadConfig) {
    const isUploading = ref(false)
    const uploadError = ref<string | null>(null)
    const uploadProgress = ref(0)

    /**
     * 处理文件上传
     * @param event 上传事件
     * @returns Promise<FileUploadResult | null>
     */
    const handleFileUpload = async (
      event: Event
    ): Promise<FileUploadResult | null> => {
      const target = event.target as HTMLInputElement
      const file = target.files?.[0]

      if (!file) {
        return null
      }

      try {
        isUploading.value = true
        uploadError.value = null
        uploadProgress.value = 0

        const result = await FileUploader.uploadSingleFile(file, config)
        uploadProgress.value = 100
        return result
      } catch (error) {
        uploadError.value = error instanceof Error ? error.message : '上传失败'
        return null
      } finally {
        isUploading.value = false
      }
    }

    return {
      isUploading,
      uploadError,
      uploadProgress,
      handleFileUpload
    }
  }
} 