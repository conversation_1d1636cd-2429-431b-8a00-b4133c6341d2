<template>
  <div>
    <form-create v-model:api="fApi" :option="option" :rule="rule"></form-create>
  </div>
</template>
<script lang="tsx" setup>
/**
 * @file 维修保养-保养经验库-奶茶车页
 * <AUTHOR>
 * @date 2025/2/12
 */
import { ref } from "vue";

import formCreate, { Api } from "@form-create/element-ui";

import MiningTruckMaintenance from "./components/MiningTruckMaintenance.vue";
import ChargeMaintenance from "./components/ChargeMaintenance.vue";

const fApi = ref();
const option = {
  form: { inline: true },
  resetBtn: false,
  submitBtn: false
};
const rule = ref([
  {
    type: "LAShadowButtonGroup",
    name: "LAShadowButtonGroup",
    wrap: { style: "marginBottom: 0" },
    value: "CoffeeCarMaintenance",
    props: {
      list: [
        { label: "无人奶茶车", value: "CoffeeCarMaintenance" },
        { label: "充电桩", value: "ChargeMaintenance" }
      ]
    },
    on: {
      "update:modelValue"(val) {
        // val="RecordsOperations"时，显示RecordsOperations组件，否则显示LoginLogs组件
        // 获取组件的list列表
        const componentsList = fApi.value.findRule({ name: "LAShadowButtonGroup" }).props.list;
        // 使用componentsList和val完成逻辑点击val时显示val对应的组件
        const show = val => {
          // console.log("切换", val, fApi.value);
          // 对componentsList里的每一的元素进行操作，如果componentsList的value等于val就显示对应的组件
          componentsList.forEach(item => {
            if (item.value == val) {
              fApi.value.hidden(false, item.value);
            } else {
              fApi.value.hidden(true, item.value);
            }
          });
        };
        show(val);
      }
    }
  },
  {
    component: MiningTruckMaintenance,
    on: {
      mounted(api: Api) {
        api.exec("v:search", "onReset");
      }
    },
    wrap: {
      style: {
        width: "100%"
      }
    },
    field: "CoffeeCarMaintenance",
    key: "1"
  },
  {
    component: ChargeMaintenance,
    on: {
      mounted(api: Api) {
        api.exec("v:search", "onReset");
      }
    },
    wrap: {
      style: {
        width: "100%"
      }
    },
    field: "ChargeMaintenance",
    key: "2"
  }
]);
</script>
<style lang="scss" scoped>
.el-tabs__content {
  padding: 32px;
  color: #6b778c;
  font-size: 12px;
  font-weight: 600;
}

:deep(.el-tabs__header) {
  display: none;
}

:deep(.el-radio-button--large .el-radio-button__inner) {
  padding: 10px 20px;
}

:deep(.LAbtn) {
  height: 32px;
}

.el-tabs--right .el-tabs__content,
.el-tabs--left .el-tabs__content {
  height: 100%;
}

:deep(.el-form-item .el-form-item) {
  margin-bottom: 0;
}

:deep(.el-form-item) {
  margin-right: 0;
}

:deep(.search-form__operator) {
  margin-bottom: 10px;
  width: 100%;

  .el-select--large .el-select__wrapper {
    min-height: initial;
    padding: 9px 16px;
  }

  .el-date-editor {
    min-height: 32px;
  }
}
</style>
<style lang="scss">
.maintenance-knowledge-detail {
  min-width: 900px;
  width: auto !important;
  p {
    margin: 0;
  }
}
</style>
