<template>
  <div class="home">
    <Toolbar
      v-if="!readonly"
      :defaultConfig="toolbarConfig"
      :editor="editor"
      mode="simple"
      style="border-bottom: 1px solid #ccc"
    />
    <Editor
      :defaultConfig="editorConfig"
      mode="simple"
      style="height: 500px; overflow-y: hidden"
      v-bind="$attrs"
      @onChange="onChange"
      @onCreated="onCreated"
    />
  </div>
</template>

<script lang="ts" setup>
import { Editor, Toolbar } from "@wangeditor/editor-for-vue";
import imageCompression from "browser-image-compression";
import { ref } from "vue";
import "@wangeditor/editor/dist/css/style.css";
import { fetchFileUpload } from "@/api/modules/upload";
// 组件属性定义（是否只读模式）
const props = defineProps<{ readonly: boolean }>();
const emit = defineEmits(["update:modelValue"]);
// 工具栏配置
const toolbarConfig = {
  // 排除不需要的功能
  excludeKeys: ["insertVideo", "group-image", "insertLink"],
  insertKeys: {
    // 插入位置
    index: 5,
    // 添加自定义图片上传按钮
    keys: ["uploadImage"]
  }
};
// 3. 修改 customUpload 函数
const editorConfig = ref({
  readOnly: props.readonly,
  MENU_CONF: {
    uploadImage: {
      maxFileSize: 2 * 1024 * 1024,
      maxNumberOfFiles: 5,
      async customUpload(file: File, insertFn: any) {
        // 压缩配置
        const options = {
          maxSizeMB: 1, // 最大文件大小
          // maxWidthOrHeight: Math.max(
          //     Math.round(origWidth * 0.8), //origWidth图片宽高压缩尺寸
          //     Math.round(origHeight * 0.8)
          // ),
          maxWidthOrHeight: 1920, // 最大分辨率
          useWebWorker: true, // 启用多线程
          fileType: file.type // 保持原始格式
        };

        // 执行压缩转换为
        const compressedBlob = await imageCompression(file, options);
        // 转换为 File 对象（关键步骤）
        const compressedFile = new File([compressedBlob], file.name, {
          type: file.type,
          lastModified: Date.now()
        });
        const formData = new FormData();
        formData.append("file", compressedFile);
        const response = (await fetchFileUpload(formData)) as any;
        const url = response.data.fullPath;
        insertFn(url, response.data.fileName, url);
      }
    }
  }
});
const editor = ref();
const onChange = value => {
  if (value.isEmpty() && value.getText() == "") {
    emit("update:modelValue", "");
  } else {
    emit("update:modelValue", value.getHtml());
  }
};

const onCreated = _editor => {
  editor.value = Object.seal(_editor);
};
</script>
<style scoped>
.home {
  width: 100%;
}
</style>
