<template>
  <div class="billboard">
    <!-- 没有设备选中时展示所有的任务队列 -->
    <TaskQueue v-if="!isDeviceSelected" />

    <!-- 根据选中的设备类型展示对应的组件 -->
    <template v-else>
      <Truck v-if="selectedDeviceType === 'deviceMineTrain'" />
      <ChargingPile v-else-if="selectedDeviceType === 'deviceChargingPile'" />
      <Bulldozer v-else-if="selectedDeviceType === 'deviceBulldozers'" />
      <Crusher v-else-if="selectedDeviceType === 'deviceCrushingStation'" />
      <!-- 未知设备类型时显示任务队列 -->
      <TaskQueue v-else />
    </template>
  </div>
</template>

<script lang="ts" setup>
/**
 * @file 动态信息
 * <AUTHOR>
 * @date 2024/11/25
 */
import TaskQueue from "./TaskQueue.vue";
// 矿卡
import Truck from "./Truck/index.vue";
// 充电桩
import ChargingPile from "./ChargingPile/index.vue";
// 挖机
import Bulldozer from "./Bulldozer/index.vue";
// 破碎站
import Crusher from "./Crusher/index.vue";
import { useDeviceSelection } from "@/views/copilot/store";

const { isDeviceSelected, selectedDeviceType } = useDeviceSelection();
</script>

<style lang="scss" scoped>
.billboard {
  height: 100%;
  width: 100%;
  overflow: hidden;
}
</style>
